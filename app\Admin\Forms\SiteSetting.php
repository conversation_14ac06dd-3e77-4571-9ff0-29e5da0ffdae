<?php

namespace App\Admin\Forms;

use Dcat\Admin\Widgets\Form;
use App\Models\SystemConfig;

class SiteSetting extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {

        foreach ($input as $k => $v) {
            $arr = ['name' => $k, 'value' => $v ?? ''];
            SystemConfig::updateOrCreate(['name' => $k], $arr);
        }
        return $this
            ->response()
            ->success('操作成功')
            ->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->tab('网站配置', function () {
            $this->text('site_name', '网站名称');
            $this->image('site_logo', '网站Logo')->uniqueName();
            $this->text('kefu_url', '客服链接');
            $this->text('shouxufeilv', '手续费');
            $this->text('zuidish<PERSON>uf<PERSON>', '最低手续费');
            $this->text('see_fees', '证交税');
            $this->time('dividend_start_time', '红利开始时间');
            $this->time('dividend_end_time', '红利结束时间');
        });


        $this->tab('存款设置', function () {
            $this->column(6, function () {
                $this->fieldset('银行卡', function () {
                    $this->number('min_recharge_money', '单笔最低')->help('银行卡，设置0，不限制');
                    $this->number('max_recharge_money', '单笔最高')->help('银行卡， 设置0，不限制');
                });
            });

            $this->column(6, function () {
                $this->fieldset('USDT', function () {
                    $this->number('min_usdt_recharge_money', '单笔最低')->help('数字货币，设置0，不限制');
                    $this->number('max_usdt_recharge_money', '单笔最高')->help('数字货币设置0，不限制');
                });
            });
        });

        $this->tab('提款设置', function () {
            $this->column(4, function () {
                $this->fieldset('基础', function () {
                    $this->time('withdraw_begin_time', '提款开始时间');
                    $this->time('withdraw_end_time', '提款结束时间');
                    $this->number('daily_withdraw_times', '每日可提款次数')->help('设置0，不限制');
                    $this->number('withdraw_fee', '提款手续费')->help('设置0，不限制');
                });
            });
            $this->column(4, function () {
                $this->fieldset('银行卡', function () {
                    $this->number('min_withdraw_money', '单笔最低')->help('银行卡，设置0，不限制');
                    $this->number('max_withdraw_money', '单笔最高')->help('银行卡，设置0，不限制');
                });
            });

            $this->column(4, function () {
                $this->fieldset('USDT', function () {
                    $this->number('min_usdt_withdraw_money', '单笔最低')->help('数字货币设置0，不限制');
                    $this->number('max_usdt_withdraw_money', '单笔最高')->help('数字货币设置0，不限制');
                });
            });


        });
        $this->tab('节假日设置', function () {
            $this->textarea('de_holidays', '德国节假日')->help('<span style="color: red">一行一个节假日</span>');
            $this->textarea('us_holidays', '美国节假日')->help('<span style="color: red">一行一个节假日</span>');;
            $this->textarea('fr_holidays', '法国节假日')->help('<span style="color: red">一行一个节假日</span>');;
            $this->textarea('sp_holidays', '西班牙节假日')->help('<span style="color: red">一行一个节假日</span>');;
            $this->textarea('en_holidays', '英国节假日')->help('<span style="color: red">一行一个节假日</span>');;
            $this->textarea('jp_holidays', '日本节假日')->help('<span style="color: red">一行一个节假日</span>');;
            $this->textarea('my_holidays', '马来西亚节假日')->help('<span style="color: red">一行一个节假日</span>');;
        });

    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'site_name' => SystemConfig::getValue('site_name'),
            'site_logo' => SystemConfig::getValue('site_logo'),
            'kefu_url' => SystemConfig::getValue('kefu_url'),
            'shouxufeilv' => SystemConfig::getValue('shouxufeilv'),
            'zuidishouxufei' => SystemConfig::getValue('zuidishouxufei'),
            'see_fees' => SystemConfig::getValue('see_fees'),

            'min_recharge_money' => SystemConfig::getValue('min_recharge_money'),
            'max_recharge_money' => SystemConfig::getValue('max_recharge_money'),
            'min_usdt_recharge_money' => SystemConfig::getValue('min_usdt_recharge_money'),
            'max_usdt_recharge_money' => SystemConfig::getValue('max_usdt_recharge_money'),

            'withdraw_begin_time' => SystemConfig::getValue('withdraw_begin_time'),
            'withdraw_end_time' => SystemConfig::getValue('withdraw_end_time'),
            'daily_withdraw_times' => SystemConfig::getValue('daily_withdraw_times'),
            'withdraw_fee' => SystemConfig::getValue('withdraw_fee'),
            'min_withdraw_money' => SystemConfig::getValue('min_withdraw_money'),
            'max_withdraw_money' => SystemConfig::getValue('max_withdraw_money'),
            'min_usdt_withdraw_money' => SystemConfig::getValue('min_usdt_withdraw_money'),
            'dividend_start_time' => SystemConfig::getValue('dividend_start_time'),
            'dividend_end_time' => SystemConfig::getValue('dividend_end_time'),
            'de_holidays' => SystemConfig::getValue('de_holidays'),
            'us_holidays' => SystemConfig::getValue('us_holidays'),
            'fr_holidays' => SystemConfig::getValue('fr_holidays'),
            'sp_holidays' => SystemConfig::getValue('sp_holidays'),
            'en_holidays' => SystemConfig::getValue('en_holidays'),
            'jp_holidays' => SystemConfig::getValue('jp_holidays'),
            'my_holidays' => SystemConfig::getValue('my_holidays'),
        ];
    }
}
