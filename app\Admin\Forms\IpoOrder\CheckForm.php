<?php

namespace App\Admin\Forms\IpoOrder;

use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\MoneyChange;
use App\Models\UsersInfo;
use Dcat\Admin\Admin;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;


class CheckForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $checkState = $input['check_state'] ?? 0;


        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!in_array($checkState, [1, 2])) {
            return $this->response()->error('操作错误');
        }


        DB::beginTransaction();
        try {
            $ipoOrder = IpoOrder::where('id', $id)->lockForUpdate()->firstOrFail();
            if (!$ipoOrder) {
                throw  new \Exception('数据不存在');
            }
            if ($ipoOrder->state != 1) {
                throw  new \Exception('当前申购订单未设置中签，不能审核');
            }
            if ($ipoOrder->check_state != 0) {
                throw  new \Exception('不是待审核状态,不能操作');
            }

            if ($checkState == 1) {
                if ($ipoOrder->zhongqianshu != $ipoOrder->renjiaonum) {
                    throw  new \Exception('当前申购订单未完成支付，不允许通过');
                }
            }


            $ipoOrder->check_state = $checkState;
            $ipoOrder->save();
            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');
        $this->text('order_sn', '订单号')->readOnly();
        $this->text('user')->readOnly();
        $this->text('name', '股票名称')->readOnly();
        $this->text('buy_price')->readOnly();
        $this->text('zhongqianshu', '中签股数')->readOnly();
        $this->text('renjiaonum', '已认缴股数')->readOnly();

        $this->text('yingrenjiao', '应认缴金额')->readOnly();
        $this->text('yirenjiao', '已认缴金额')->readOnly();
        $this->text('xurenjiao', '需认缴金额')->readOnly();
        $this->text('to_market_date', '上市日期')->readOnly();

        $this->radio('check_state', '审核')->options(['1' => '通过', '2' => '拒绝'])->required()->rules('required', ['required' => '请选择操作']);
    }


    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        $data = IpoOrder::find($id);

        return [
            'order_sn' => $data->order_sn,
            'user' => $data->user->email,
            'name' => $data->ipo->name,
            'buy_price' => $data->buy_price,
            'zhongqianshu' => $data->zhongqianshu,
            'yirenjiao' => $data->yirenjiao,
            'yingrenjiao' => $data->yingrenjiao,
            'renjiaonum' => $data->renjiaonum,
            'xurenjiao' => $data->xurenjiao,
            'to_market_date' => $data->ipo->to_market_date,
//            'success_num' => $data->buy_in_num,
//            'created_at' => $data->created_at,
        ];
    }
}
