<?php

namespace App\Admin\Forms;

use App\Models\Users;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;


class ChangeJoinStateForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        // 接收外部传递参数
        $id = $this->payload['id'] ?? null;
        $state = $input['state'] ?? 0;
        if (!$id) {
            return $this->response()->error('参数错误');
        }
        $data = Users::query()->find($id);
        if(!$data){
            return $this->response()->error('数据不存在');
        }

        $data->login_status = $state;
        $res = $data->save();
        if(!$res){
            return $this->response()->error('操作失败');
        }

        return $this->response()->success('保存成功')->refresh();
    }

    public function form()
    {
        $this->radio('state', '状态')->options([ 1 => '开启', 0 => '关闭']);
    }

    public function default()
    {
        // 接收外部传递参数
        $id = $this->payload['id'] ?? null;
        $data = Users::query()->find($id);
        return [
            'state'=> $data->login_status
        ];
    }
}
