<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\ModelTree;


class AdminPosition extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    use ModelTree;


    protected $table = 'admin_positions';

    // 标题字段名称，默认值为 title
    protected $titleColumn = 'name';

    // 排序字段名称，默认值为 order
    protected $orderColumn = 'weight';

    // Since v2.1.6-beta，定义depthColumn属性后，将会在数据表保存当前行的层级
    protected $depthColumn = 'depth';

}
