<?php

namespace App\Admin\Actions\Grid\UserOrder;


use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\TraderFollower;
use App\Models\TraderFollowerMoneyChange;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DeleteOrder extends RowAction
{
    /**
     * @return string
     */
    protected $title = '删除';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        DB::beginTransaction();
        try {
            $model = UserOrder::where('id', $id)->lockForUpdate()->firstOrFail();

            if (!in_array($model->status, [1, 4])) {
                throw new \Exception('订单不是持仓中或挂单中，无法删除');
            }

            // 跟单订单和自主订单使用不同的删除逻辑
            if ($model->trade_id > 0) {
                // 跟单订单删除逻辑
                $this->deleteTraderOrder($model);
            } else {
                // 自主订单删除逻辑
                $this->deleteSelfOrder($model);
            }

            UserOrder::destroy($id);
            DB::commit();
            return $this->response()->success('删除成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('删除失败:' . $e->getMessage())->refresh();
        }
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确定删除', ''];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }

    /**
     * 删除跟单订单
     */
    private function deleteTraderOrder($model)
    {
        // 获取跟单关系
        $follower = TraderFollower::where('user_id', $model->user_id)
            ->where('trader_id', $model->trade_id)
            ->lockForUpdate()
            ->first();

        if (!$follower) {
            throw new \Exception('跟单关系不存在');
        }

        $product = Product::query()
            ->where('id', $model->product_id)
            ->where('deleted_at', null)
            ->where('state', 1)
            ->first();

        $buy_cost = $model->buy_cost;
        $buy_fees = $model->buy_fees;
        $stamp_duty = $model->stamp_duty;

        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
            $buy_cost = calculateEuroCost($buy_cost, $model->exchange_rate, $model->user_spread);
            $buy_fees = calculateEuroCost($buy_fees, $model->exchange_rate, $model->user_spread);
            $stamp_duty = calculateEuroCost($stamp_duty, $model->exchange_rate, $model->user_spread);
        }

        $totalCost = $buy_cost + $buy_fees + $stamp_duty;

        $beforeDongjie = $follower->bet_money; // 冻结前的金额

        // 返还资金到跟单关系
        $follower->bet_money += $totalCost;
        $follower->used_money -= $buy_cost;
        $follower->save();

        // 记录跟单资金流水
        TraderFollowerMoneyChange::create([
            'money_type' => 'EUR',
            'user_id' => $model->user_id,
            'trader_id' => $model->trade_id,
            'order_sn' => $model->order_sn,
            'product_name' => $model->product->name,
            'detailed' => "跟单删除|返还本金",
            'before_money' => $beforeDongjie,
            'money' => $buy_cost,
            'after_money' => $beforeDongjie + $buy_cost,
        ]);

        // 记录资金变动 - 手续费
        if ($buy_fees > 0) {
            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $model->user_id,
                'trader_id' => $model->trade_id,
                'order_sn' => $model->order_sn,
                'product_name' => $model->product->name,
                'detailed' => "跟单删除|返回手续费",
                'before_money' => $beforeDongjie + $buy_cost,
                'money' => $buy_fees,
                'after_money' => $beforeDongjie + $buy_cost + $buy_fees,
            ]);
        }

        // 记录资金变动 - 手续费
        if ($stamp_duty > 0) {
            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $model->user_id,
                'trader_id' => $model->trade_id,
                'order_sn' => $model->order_sn,
                'product_name' => $model->product->name,
                'detailed' => "跟单删除|返回印花税",
                'before_money' => $beforeDongjie + $buy_cost + $buy_fees,
                'money' => $stamp_duty,
                'after_money' => $beforeDongjie + $buy_cost + $buy_fees + $stamp_duty,
            ]);
        }
    }

    /**
     * 删除自主订单
     */
    private function deleteSelfOrder($model)
    {
        $product = Product::findOrFail($model->product_id);

        $user_info = UsersInfo::where('user_id', $model->user_id)->lockForUpdate()->first();

        $buy_cost = $model->buy_cost;
        $buy_fees = $model->buy_fees;
        $stamp_duty = $model->stamp_duty;

        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
            // 本金
            $buy_cost = calculateEuroCost($model->buy_cost, $model->exchange_rate, $model->user_spread > 0 ? $model->user_spread : 0.00036);
            // 手续费
            $buy_fees = calculateEuroCost($model->buy_fees, $model->exchange_rate, $model->user_spread > 0 ? $model->user_spread : 0.00036);
            // 印花税
            $stamp_duty = calculateEuroCost($model->stamp_duty, $model->exchange_rate, $model->user_spread > 0 ? $model->user_spread : 0.00036);
        }

        $before = $user_info->eur_balance;

        $freeze_total = $buy_cost + $buy_fees + $stamp_duty;

        // 解冻挂单资金
        $user_info->eur_balance += $freeze_total;
        $user_info->eur_dongjie -= $buy_cost;
        $user_info->save();

        MoneyChange::create([
            'money_type' => "EUR",
            'user_id' => $model->user_id,
            'order_sn' => $model->order_sn,
            'product_name' => $product->name,
            'detailed' => "删除买入订单|代码|{$product->symbol}|产品|{$product->name}",
            'type' => MoneyChange::TYPE_TRADE_DELETE,
            'before_money' => $before,
            'money' => $buy_cost,
            'after_money' => $before + $buy_cost,
            'is_show' => 0, // 不显示流水记录
        ]);

        if ($buy_fees > 0) {
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $model->user_id,
                'order_sn' => $model->order_sn,
                'product_name' => $product->name,
                'detailed' => "删除买入手续费|代码|{$product->symbol}|产品|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE_FEE_DELETE,
                'before_money' => $before + $buy_cost,
                'money' => $buy_fees,
                'after_money' => $before + $buy_cost + $buy_fees,
                'is_show' => 0, // 不显示流水记录
            ]);
        }

        if ($stamp_duty > 0) {
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $model->user_id,
                'order_sn' => $model->order_sn,
                'product_name' => $product->name,
                'detailed' => "删除买入印花税|代码|{$product->symbol}|产品|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE_FEE_DELETE,
                'before_money' => $before + $buy_cost + $buy_fees,
                'money' => $stamp_duty,
                'after_money' => $before + $buy_cost + $buy_fees + $stamp_duty,
                'is_show' => 0, // 不显示流水记录
            ]);
        }

        MoneyChange::query()->where('user_id', $model->user_id)->where('order_sn', $model->order_sn)->update(['is_show' => 0]);
    }

}
