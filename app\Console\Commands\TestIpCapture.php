<?php

namespace App\Console\Commands;

use App\Helpers\IpHelper;
use Illuminate\Console\Command;
use Illuminate\Http\Request;

class TestIpCapture extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:ip-capture';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test IP capture functionality';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== IP获取功能测试 ===');
        
        // 模拟不同的请求环境
        $testCases = [
            [
                'name' => '直接访问',
                'headers' => [
                    'REMOTE_ADDR' => '***********00'
                ]
            ],
            [
                'name' => 'Nginx代理',
                'headers' => [
                    'REMOTE_ADDR' => '127.0.0.1',
                    'HTTP_X_REAL_IP' => '***********',
                    'HTTP_X_FORWARDED_FOR' => '***********'
                ]
            ],
            [
                'name' => 'Cloudflare CDN',
                'headers' => [
                    'REMOTE_ADDR' => '127.0.0.1',
                    'HTTP_CF_CONNECTING_IP' => '************',
                    'HTTP_X_FORWARDED_FOR' => '************, ***********'
                ]
            ],
            [
                'name' => '多层代理',
                'headers' => [
                    'REMOTE_ADDR' => '127.0.0.1',
                    'HTTP_X_FORWARDED_FOR' => '************, ***********, ***********'
                ]
            ]
        ];

        foreach ($testCases as $case) {
            $this->info("\n--- {$case['name']} ---");
            
            // 模拟环境变量
            $originalServer = $_SERVER;
            foreach ($case['headers'] as $key => $value) {
                $_SERVER[$key] = $value;
            }
            
            // 创建模拟请求
            $request = new Request();
            
            // 测试IP获取
            $realIp = IpHelper::getRealIp($request);
            $debugInfo = IpHelper::getDebugInfo($request);
            
            $this->line("获取到的IP: {$realIp}");
            $this->line("调试信息:");
            foreach ($debugInfo as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
            
            // 恢复环境变量
            $_SERVER = $originalServer;
        }
        
        $this->info("\n=== 当前真实环境测试 ===");
        
        // 测试当前环境
        $request = request();
        if ($request) {
            $realIp = IpHelper::getRealIp($request);
            $debugInfo = IpHelper::getDebugInfo($request);
            
            $this->line("当前环境获取到的IP: {$realIp}");
            $this->line("当前环境调试信息:");
            foreach ($debugInfo as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
            
            // 获取IP详细信息
            $ipInfo = IpHelper::getIpInfo($realIp);
            $this->line("IP详细信息:");
            foreach ($ipInfo as $key => $value) {
                $this->line("  {$key}: {$value}");
            }
        } else {
            $this->warn("当前环境没有HTTP请求上下文");
        }
        
        $this->info("\n=== 测试完成 ===");
        $this->info("如果注册时IP仍为空，请检查：");
        $this->info("1. 服务器是否在代理/负载均衡器后面");
        $this->info("2. 是否使用了CDN服务");
        $this->info("3. 网络配置是否正确");
        
        return 0;
    }
}
