<?php

namespace App\Admin\Forms;

use App\Models\MoneyChange;
use App\Models\Withdraw;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;
use App\Models\UsersInfo;

class WithdrawCheck extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $state = $input['state'] ?? 0;

        $remark = $input['remark'] ?? '';

        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!in_array($state, [1, 2])) {
            return $this->response()->error('操作错误');
        }


        DB::beginTransaction();
        try {
            $data = Withdraw::find($id);

            if (!$data) {
                DB::rollBack();
                return $this->response()->error('数据不存在');
            }

            if ($data->state != 0) {
                throw new \Exception('订单状态不是未审核状态，请勿重复操作');
            }
            $userInfo = UsersInfo::query()->where('user_id', $data->user_id)->lockForUpdate()->first();

            if ($state == 1) {
                $dongjieField = 'eur_dongjie';

                $userInfo->$dongjieField -= $data->money;
                $userInfo->save();
            } elseif ($state == 2) {
                $balanceField = 'eur_balance';
                $dongjieField = 'eur_dongjie';
                $moneyType = 'EUR';
                $moneyChangeType = MoneyChange::TYPE_WITHDRAW_REJECT; // 3：提款拒绝

                $beforBalance = $userInfo->$balanceField;
                $afterBalance = $userInfo->$balanceField + $data->money;
                $userInfo->$balanceField += $data->money;
                $userInfo->$dongjieField -= $data->money;
                $userInfo->save();

                // 添加流水记录
                MoneyChange::create([
                    'user_id' => $data->user->id,
                    'order_sn' => $data->order_sn,
                    'type' => $moneyChangeType,
                    'money_type' => $moneyType,
                    'before_money' => $beforBalance,
                    'money' => $data->money,
                    'after_money' => $afterBalance,
                    'detailed' => '提款申请|拒绝',
                ]);
            } else {
                throw new \Exception('未知操作');
            }

            $data->state = $state;
            $data->remark = $remark;
            $data->save();

            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作成功失败:' . $e->getMessage());
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');


        $this->radio('state', '操作')->options(['1' => '通过', '2' => '拒绝'])
            ->when([2], function (Form $form) {
                $form->text('remark', '备注')->help('选择填写');
            })
            ->default('1');
    }
}
