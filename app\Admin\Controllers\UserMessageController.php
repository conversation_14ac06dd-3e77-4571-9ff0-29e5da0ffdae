<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\UserMessage;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserMessageController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserMessage(), function (Grid $grid) {

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }
            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('type')->using([1 => '普通消息', 2 => '公告']);
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('content');
            $grid->column('state')->using([0 => '未读', 1 => '已读'])->badge([
                0 => 'danger',
                1 => 'success',
            ]);
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserMessage(), function (Show $show) {
            $show->field('id');
            $show->field('type');
            $show->field('user_id');
            $show->field('content');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserMessage(), function (Form $form) {
            $form->display('id');
            $form->radio('type')->options([1 => '普通消息', 2 => '公告']);
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()->rules('required')->rules('required', ['required' => '请选择用户']);
            $form->text('content');
//            $form->text('state');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
