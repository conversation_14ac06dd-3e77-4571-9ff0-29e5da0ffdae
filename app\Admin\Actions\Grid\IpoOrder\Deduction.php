<?php

namespace App\Admin\Actions\Grid\IpoOrder;

use App\Admin\Actions\Grid\User\Authenticatable;
use App\Admin\Actions\Grid\User\HasPermissions;
use App\Admin\Actions\Grid\User\Model;
use App\Admin\Forms\IpoOrder\DeductionForm;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Modal;

class Deduction extends RowAction
{
    /**
     * @return string
     */
    protected $title = '新股扣费';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Modal
     */
    public function render()
    {

        $form = DeductionForm::make()->payload(['id' => $this->getKey()]);

        return Modal::make()
            ->lg()
            ->title('新股扣费')
            ->body($form)
            ->button('<a class="btn btn-sm btn-success" style="color: #f4f4f4">新股扣费</a>');

    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        // return ['你确定要删除此行内容吗？', '弹窗内容'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }


}
