<?php

namespace App\Admin\Repositories;

use App\Models\IpoOrder as Model;
use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\UserMessage;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Facades\DB;

class IpoOrder extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function store(Form $form)
    {
        $result = null;


        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();
            $buyInPrice = $updates['buy_price'];
            $buyInNum = $updates['buy_num'];

            $userId = $updates['user_id'];

            $total = $buyInPrice * $buyInNum;
            $rate = get_buy_fee_rate($userId);
            $buyInFees = buy_commission($total, $rate); // 手续费
            $allAmount = $total + $buyInFees;
            $updates['xurenjiao'] = $allAmount;
            $updates['yingrenjiao'] = $allAmount;
            // 生成订单号
            $out_trade_no = generateUniqueOrderNumber();
            $updates['order_sn'] = $out_trade_no;

            $rate = get_buy_fee_rate($userId); // 申请的时候就要先绑定费率，后续根据这个费率来操作
            $updates['buy_in_fee_rate'] = $rate;


            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }


            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }

            $result = $model->save();

            $ipo = \App\Models\Ipo::find($updates['ipo_id']);
            // 给用户添加信息
            $now = date('d.m.Y H:i:s');
            $message = [
                'user_id' => $userId,
                'content' => "尊敬的会员|,|您在|{$now}|,|申购了新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|中签公布日将通知您中签结果|!"
            ];
            UserMessage::create($message);


            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
