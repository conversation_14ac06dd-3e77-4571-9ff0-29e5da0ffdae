<?php

namespace App\Helpers;

use Illuminate\Http\Request;

class IpHelper
{
    /**
     * 获取客户端真实IP地址
     * 支持代理、负载均衡器、CDN等环境
     *
     * @param Request $request
     * @return string
     */
    public static function getRealIp(Request $request)
    {
        // 优先级顺序的HTTP头列表
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // 代理服务器
            'HTTP_X_FORWARDED_FOR',      // 负载均衡器/代理
            'HTTP_X_FORWARDED',          // 代理
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理
            'HTTP_FORWARDED',            // 代理
            'HTTP_X_REAL_IP',            // Nginx代理
            'REMOTE_ADDR'                // 标准方法
        ];

        foreach ($headers as $header) {
            $ip = self::getHeaderValue($request, $header);
            if ($ip && self::isValidIp($ip)) {
                return $ip;
            }
        }

        // 如果所有方法都失败，使用Laravel默认方法
        $fallbackIp = $request->ip();

        // 确保返回有效的IP地址
        if (empty($fallbackIp) || $fallbackIp === '::1') {
            return '127.0.0.1'; // 本地环境默认IP
        }

        return $fallbackIp;
    }

    /**
     * 从HTTP头获取IP值
     *
     * @param Request $request
     * @param string $header
     * @return string|null
     */
    private static function getHeaderValue(Request $request, $header)
    {
        $value = null;

        // 尝试不同的获取方式
        if ($header === 'REMOTE_ADDR') {
            $value = $_SERVER['REMOTE_ADDR'] ?? null;
        } else {
            $value = $request->header(str_replace('HTTP_', '', $header)) 
                  ?? $_SERVER[$header] 
                  ?? null;
        }

        if (!$value) {
            return null;
        }

        // 处理多个IP的情况（用逗号分隔）
        if (strpos($value, ',') !== false) {
            $ips = explode(',', $value);
            foreach ($ips as $ip) {
                $ip = trim($ip);
                if (self::isValidIp($ip)) {
                    return $ip;
                }
            }
        }

        return trim($value);
    }

    /**
     * 验证IP地址是否有效
     *
     * @param string $ip
     * @return bool
     */
    private static function isValidIp($ip)
    {
        if (empty($ip)) {
            return false;
        }

        // 验证IP格式
        if (!filter_var($ip, FILTER_VALIDATE_IP)) {
            return false;
        }

        // 排除私有IP和保留IP（可选，根据需求调整）
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return true;
        }

        // 如果需要包含私有IP（内网环境），可以只验证格式
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * 获取IP地址的详细信息（包括地理位置等）
     *
     * @param string $ip
     * @return array
     */
    public static function getIpInfo($ip)
    {
        $info = [
            'ip' => $ip,
            'is_private' => false,
            'is_valid' => false,
            'country' => '',
            'region' => '',
            'city' => '',
        ];

        if (!self::isValidIp($ip)) {
            return $info;
        }

        $info['is_valid'] = true;

        // 检查是否为私有IP
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            $info['is_private'] = true;
            return $info;
        }

        // 这里可以集成第三方IP地理位置服务
        // 例如：ip-api.com, ipinfo.io 等
        try {
            $response = @file_get_contents("http://ip-api.com/json/{$ip}?fields=country,regionName,city,status");
            if ($response) {
                $data = json_decode($response, true);
                if ($data && $data['status'] === 'success') {
                    $info['country'] = $data['country'] ?? '';
                    $info['region'] = $data['regionName'] ?? '';
                    $info['city'] = $data['city'] ?? '';
                }
            }
        } catch (\Exception $e) {
            // 忽略地理位置获取错误
        }

        return $info;
    }

    /**
     * 记录IP获取的调试信息
     *
     * @param Request $request
     * @return array
     */
    public static function getDebugInfo(Request $request)
    {
        return [
            'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'not set',
            'HTTP_X_FORWARDED_FOR' => $request->header('X-Forwarded-For') ?? 'not set',
            'HTTP_X_REAL_IP' => $request->header('X-Real-IP') ?? 'not set',
            'HTTP_CF_CONNECTING_IP' => $request->header('CF-Connecting-IP') ?? 'not set',
            'HTTP_CLIENT_IP' => $request->header('Client-IP') ?? 'not set',
            'laravel_ip()' => $request->ip(),
            'real_ip' => self::getRealIp($request),
        ];
    }
}
