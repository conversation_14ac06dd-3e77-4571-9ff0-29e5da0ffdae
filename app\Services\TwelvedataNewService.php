<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TwelvedataNewService
{
    public static $HOST = "https://api.twelvedata.com";
    public static $KEY = "********************************";


    /**
     * 股票行情 返回最高价-最低价-当前价等
     * @param object $symbol 符号（代码）
     * @param string $exchange 交易所 注意：多个交易所的股票可能存在股票代码相同 所以必须要加上交易所
     * @return array|bool
     */
    public static function quoteByOne($pro)
    {
        $symbol = $pro->symbol;
        $exchange = '';
        // 股票和ETF需要根据交易所来查询
        if ($pro->pro_type == Product::$PRO_TYPE_STCOK || $pro->pro_type == Product::$PRO_TYPE_ETF) {
            $urlQuote = "/quote?symbol=" . $symbol . '&apikey=' . self::$KEY . '&exchange=' . $exchange;
            $urlPrice = "/price?symbol=" . $symbol . '&apikey=' . self::$KEY . '&exchange=' . $exchange;
        } else {
            $urlQuote = "/quote?symbol=" . $symbol . '&apikey=' . self::$KEY;
            $urlPrice = "/price?symbol=" . $symbol . '&apikey=' . self::$KEY;
        }


        $url = self::$HOST . '/batch';

        $param = [
            'req_1' => ['url' => $urlPrice],
            'req_2' => ['url' => $urlQuote]
        ];

        $res = self::requestPost($url, $param);

        $tableName = 'product';

        if ($res !== false) {
            $updateData = [];
            if (isset($res['data']['req_1']['response']['price'])) {
                $updateData['price'] = $res['data']['req_1']['response']['price'];
            }

            if (!isset($res['data']['req_2']['response']['code'])) {
                $updateData['datetime'] = $res['data']['req_2']['response']['datetime'];
                $updateData['timestamp'] = $res['data']['req_2']['response']['timestamp'];
                $updateData['last_quote_at'] = $res['data']['req_2']['response']['last_quote_at'];
                $updateData['open'] = $res['data']['req_2']['response']['open'];
                $updateData['high'] = $res['data']['req_2']['response']['high'];
                $updateData['low'] = $res['data']['req_2']['response']['low'];
                $updateData['close'] = $res['data']['req_2']['response']['close'];
                $updateData['volume'] = $res['data']['req_2']['response']['volume'] ?? 0;
                $updateData['previous_close'] = $res['data']['req_2']['response']['previous_close'];
                $updateData['change'] = $res['data']['req_2']['response']['change'];
                $updateData['percent_change'] = $res['data']['req_2']['response']['percent_change'];
                $updateData['average_volume'] = $res['data']['req_2']['response']['average_volume'] ?? 0;
                $updateData['is_market_open'] = $res['data']['req_2']['response']['is_market_open'];
            }

            if (!empty($updateData)) {
                // 股票和ETF需要根据交易所来查询
                if ($pro->pro_type == Product::$PRO_TYPE_STCOK || $pro->pro_type == Product::$PRO_TYPE_ETF) {
                    DB::table($tableName)->where('symbol', $symbol)->where('exchange', $exchange)->update($updateData);
                } else {
                    DB::table($tableName)->where('symbol', $symbol)->update($updateData);
                }
            }
        }
        $pro->refresh();
        return $pro;
    }

    /**
     * 股票行情 返回最高价-最低价-当前价等- 批量
     * @param object $pros 产品
     * @return array|bool
     */
    public static function quoteByBatch($pros)
    {
        if ($pros->isEmpty()) {
            return false;
        }

        $paramData = [];
        foreach ($pros as $item) {
            if ($item->pro_type == Product::$PRO_TYPE_STCOK || $item->pro_type == Product::$PRO_TYPE_ETF) {
                $quotoIdx = 'req-quote-' . $item->symbol . '-' . $item->exchange;
                $priceIdx = 'req-price-' . $item->symbol . '-' . $item->exchange;

                $quoteUrl = "/quote?symbol=" . $item->symbol . '&apikey=' . self::$KEY . '&exchange=' . $item->exchange;
                $priceUrl = "/price?symbol=" . $item->symbol . '&apikey=' . self::$KEY . '&exchange=' . $item->exchange;
            } else {
                $quotoIdx = 'req-quote-' . $item->symbol;
                $priceIdx = 'req-price-' . $item->symbol;

                $quoteUrl = "/quote?symbol=" . $item->symbol . '&apikey=' . self::$KEY;
                $priceUrl = "/price?symbol=" . $item->symbol . '&apikey=' . self::$KEY;
            }

            $paramData[$quotoIdx] = [
                'url' => $quoteUrl
            ];
            $paramData[$priceIdx] = [
                'url' => $priceUrl
            ];
        }

//        var_dump(json_encode($paramData));

        $url = self::$HOST . '/batch';
        $res = self::requestPost($url, $paramData);

        $tableName = 'product';

        if ($res !== false) {
            $updateData = [];
            $updateNoExchangeData = [];

            foreach ($res['data'] as $k => $item) {
                $kArr = explode('-', $k);
                $symbolStr = $kArr[2];
                if (count($kArr) == 4) {
                    $exchange = $kArr[3];

                    // 接口存在code 就是有报错，不处理
                    if (!isset($item['response']['code'])) {
                        if ($kArr[1] == 'quote') {
                            // 行情
                            $updateData[$symbolStr][$exchange]["datetime"] = $item['response']['datetime'];//"2025-05-23",
                            $updateData[$symbolStr][$exchange]["timestamp"] = $item['response']['timestamp'];//1748007000,
                            $updateData[$symbolStr][$exchange]["last_quote_at"] = $item['response']['last_quote_at'];//1748007000,
                            $updateData[$symbolStr][$exchange]["open"] = $item['response']['open']; //"108.089996",
                            $updateData[$symbolStr][$exchange]["high"] = $item['response']['high']; //"111.019997",
                            $updateData[$symbolStr][$exchange]["low"] = $item['response']['low']; //"107.67000",
                            $updateData[$symbolStr][$exchange]["close"] = $item['response']['close']; //"110.31000",
                            $updateData[$symbolStr][$exchange]["volume"] = $item['response']['volume'] ?? 0; //"30825000",
                            $updateData[$symbolStr][$exchange]["previous_close"] = $item['response']['previous_close']; //"110.71000",
                            $updateData[$symbolStr][$exchange]["change"] = $item['response']['change']; //"-0.40000153",
                            $updateData[$symbolStr][$exchange]["percent_change"] = $item['response']['percent_change']; //"-0.36130569",
                            $updateData[$symbolStr][$exchange]["average_volume"] = $item['response']['average_volume']; //"45064930",
                            $updateData[$symbolStr][$exchange]["is_market_open"] = $item['response']['is_market_open'] ? 1 : 0; //false,

                        } elseif ($kArr[1] == 'price') {
                            $updateData[$symbolStr][$exchange]['price'] = $item['response']['price'];
                        }
                    }
                } else {
                    // 接口存在code 就是有报错，不处理
                    if (!isset($item['response']['code'])) {
                        if ($kArr[1] == 'quote') {
                            // 行情
                            $updateNoExchangeData[$symbolStr]["datetime"] = $item['response']['datetime'];//"2025-05-23",
                            $updateNoExchangeData[$symbolStr]["timestamp"] = $item['response']['timestamp'];//1748007000,
                            $updateNoExchangeData[$symbolStr]["last_quote_at"] = $item['response']['last_quote_at'] ?? 0;//1748007000,
                            $updateNoExchangeData[$symbolStr]["open"] = $item['response']['open']; //"108.089996",
                            $updateNoExchangeData[$symbolStr]["high"] = $item['response']['high']; //"111.019997",
                            $updateNoExchangeData[$symbolStr]["low"] = $item['response']['low']; //"107.67000",
                            $updateNoExchangeData[$symbolStr]["close"] = $item['response']['close']; //"110.31000",
                            $updateNoExchangeData[$symbolStr]["previous_close"] = $item['response']['previous_close']; //"110.71000",
                            $updateNoExchangeData[$symbolStr]["change"] = $item['response']['change']; //"-0.40000153",
                            $updateNoExchangeData[$symbolStr]["percent_change"] = $item['response']['percent_change']; //"-0.36130569",
                            $updateNoExchangeData[$symbolStr]["is_market_open"] = $item['response']['is_market_open'] ? 1 : 0; //false,

                        } elseif ($kArr[1] == 'price') {
                            $updateNoExchangeData[$symbolStr]['price'] = $item['response']['price'];
                        }
                    }
                }

                foreach ($updateData as $key => $val) {
                    foreach ($val as $k => $v) {
                        DB::table($tableName)->where('symbol', "{$key}")->where('exchange', "{$k}")->update($v);
                    }
                }

                foreach ($updateNoExchangeData as $bk => $bval) {
                    DB::table($tableName)->where('symbol', "{$bk}")->update($bval);
                }
            }
        }
        return $res;
    }

    /**
     * 产品K线
     * @param object $pro k线
     * @return array
     */

    public static function kline($pro, $lineType = 11)
    {

        $klineType = [
            1 => '1min',
            2 => '5min',
            3 => '15min',
            4 => '30min',
            5 => '45min',
            6 => '1h',
            7 => '2h',
            8 => '4h',
            9 => '1day',
            10 => '1week',
            11 => '1month',
        ];

        $url = self::$HOST . '/time_series?outputsize=3000&symbol=' . $pro->symbol . '&apikey=' . self::$KEY . '&interval=' . $klineType[$lineType];
        if ($pro->pro_type == Product::$PRO_TYPE_STCOK || $pro->pro_type == Product::$PRO_TYPE_ETF) {
            $url .= '&exchange=' . $pro->exchange;
        }

        $res = self::curlGet($url);

        if(isset($res['values'])){
            return $res['values'];
        }

    }


    /**
     * 获取实时价格
     * @param string $symbol 符号（代码）
     * @param string $tableName 表名
     * @return array|bool
     */
    public static function price($symbol, $tableName)
    {
        if (!DB::getSchemaBuilder()->hasTable($tableName)) {
            throw new \Exception('Table ' . $tableName . ' Not Found');
        }

        $url = self::$HOST . "/price?symbol=" . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if ($res !== false || isset($res['price'])) {
            DB::table($tableName)->where('symbol', $symbol)->update([
                "price" => $res['price'],
            ]);
        }

    }

    /**
     * 市场推动者，获取今日涨幅或跌幅最大的股票列表。
     * @param $type
     * @param $direction
     * @param $country
     * @return mixed|null
     */
    public static function get_market_movers($type, $direction, $country)
    {
        // type: stocks, etf, mutual_funds, forex, crypto
        // direction: gainers, losers
        $url = self::$HOST . '/market_movers/' . $type. '?direction=' . $direction . '&country=' . $country . '&outputsize=5&apikey=' . self::$KEY;

        $res = self::curlGet($url);
        if(isset($res['values'])){
            return $res['values'];
        }

        Log::error('获取市场推动者失败|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取股票的分析师建议
     * Recommendations
     * This API endpoint returns the average of all analyst recommendations and classifies them as Strong Buy, Buy, Hold, or Sell. Also, it returns a recommendation score.
     * 此 API 端点返回所有分析师建议的平均值，并将其分类为“强力买入”、“买入”、“持有”或“卖出”。此外，它还会返回建议评分。
     * * @return mixed|null
     */
    public static function recommendations($symbol, $exchange){
        $url = self::$HOST . '/recommendations?symbol=' . $symbol. '&apikey=' . self::$KEY;

        $res = self::curlGet($url);
        if(isset($res['status']) && $res['status'] == 'ok' && !empty($res['rating'])){
            return $res;
        }

        Log::error('获取股票的分析师建议|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取股票的基本资料
     * @param string $symbol 符号（代码）
     * @param string $exchange 交易所
     * @return array|null
     */
    public static function getProfile($symbol, $exchange)
    {
        $url = self::$HOST . '/profile?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['symbol'])) {
            return $res;
        }

        Log::error('获取股票的基本资料|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取股票统计数据
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getStatistics($symbol, $exchange)
    {
        $url = self::$HOST . '/statistics?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['statistics'])) {
            return $res;
        }

        Log::error('获取股票统计数据|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取股票收益数据
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getEarnings($symbol, $exchange)
    {
        $url = self::$HOST . '/earnings?symbol=' . $symbol . '&outputsize=30&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['earnings'])) {
            return $res;
        }

        Log::error('获取股票收益数据|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取盈利预测
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getEstimate($symbol, $exchange)
    {
        $url = self::$HOST . '/earnings_estimate?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['earnings_estimate'])) {
            return $res;
        }

        Log::error('获取盈利预测|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 获取收入估算
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getRevenue($symbol, $exchange)
    {
        $url = self::$HOST . '/revenue_estimate?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['revenue_estimate'])) {
            return $res;
        }

        Log::error('获取收入估算|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 每股收益趋势
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getEpsTrend($symbol, $exchange)
    {
        $url = self::$HOST . '/eps_trend?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['eps_trend'])) {
            return $res;
        }

        Log::error('获取每股收益趋势|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 每股收益修订
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getEpsRevisions($symbol, $exchange)
    {
        $url = self::$HOST . '/eps_revisions?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['eps_revision'])) {
            return $res;
        }

        Log::error('获取每股收益修订|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 增长预测
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getGrowthEstimates($symbol, $exchange)
    {
        $url = self::$HOST . '/growth_estimates?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['growth_estimates'])) {
            return $res;
        }

        Log::error('获取增长预测|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 分析师评级
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getAnalystRatingsLight($symbol, $exchange)
    {
        $url = self::$HOST . '/analyst_ratings/light?symbol=' . $symbol . '&outputsize=10&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['ratings'])) {
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 历史价格
     * @param $symbol
     * @param $exchange
     * @param $lineType
     * @return mixed|null
     */
    public static function getHistoricalPrice($symbol, $exchange, $lineType, $start_date, $end_date)
    {
        $klineType = [
            1 => '1min',
            2 => '5min',
            3 => '15min',
            4 => '30min',
            5 => '45min',
            6 => '1h',
            7 => '2h',
            8 => '4h',
            9 => '1day',
            10 => '1week',
            11 => '1month',
        ];

        $url = self::$HOST . '/time_series?symbol=' . $symbol . '&interval=' . $klineType[$lineType] . '&start_date=' . $start_date . '&end_date=' . $end_date . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if(isset($res['values'])){
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 损益表（年度、季度）
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getIncomeStatement($symbol, $exchange, $period)
    {
        $url = self::$HOST . '/income_statement?symbol=' . $symbol . '&period=' . $period . '&apikey=' . self::$KEY;

        $res = self::curlGet($url);

        if (isset($res['income_statement'])) {
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 资产负债表（年度、季度）
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getBalanceSheet($symbol, $exchange, $period)
    {
        $url = self::$HOST . '/balance_sheet?symbol=' . $symbol . '&period=' . $period . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['balance_sheet'])) {
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 现金流（年度、季度）
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getCashFlow($symbol, $exchange, $period)
    {
        $url = self::$HOST . '/cash_flow?symbol=' . $symbol . '&period=' . $period . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['cash_flow'])) {
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    /**
     * 持有人
     * @param $symbol
     * @param $exchange
     * @return mixed|null
     */
    public static function getHolders($symbol, $exchange)
    {
        $url = self::$HOST . '/fund_holders?symbol=' . $symbol . '&apikey=' . self::$KEY;
        $res = self::curlGet($url);

        if (isset($res['fund_holders'])) {
            return $res;
        }

        Log::error('获取分析师评级|url:' . $url . '响应内容：' . json_encode($res));

        return null;
    }

    protected static function curlGet($url)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);


        // 可选：跳过服务器证书验证（不推荐）
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        $response = curl_exec($ch);
        $errorNo = curl_errno($ch);
        curl_close($ch);

        if ($errorNo) {
            Log::error('请求数据源失败|状态码: ' . $errorNo, '|url:' . $url);
            return false;
//            $this->error('cURL 错误: ' . curl_error($ch));
        }

        $return = json_decode($response, true);
        if (!$return || empty($return)) {
//            Queue::push(TelegramNoticeJob::class, $logData, 'telegramNotice');
            Log::error('请求数据源失败,|url:' . $url . '响应内容：' . $response);
            return false;
        }

        if (isset($return['code']) && $return['code'] == '401') {
            Log::error('请求数据源失败,|url:' . $url . '响应内容：' . $response);
            return false;
        }
        return $return;
    }

    /**
     * @param $url
     * @param $data
     * @return false|mixed
     */
    protected static function requestPost($url, $data)
    {
        $curl = curl_init();

        // 可选：跳过服务器证书验证（不推荐）
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($data), //
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);

        $logData = [
            'url' => $url,
            'param' => $data,
            'response' => $response,
        ];

        if ($error) {
            $logData['error'] = $error;
//            Queue::push(TelegramNoticeJob::class, $logData, 'telegramNotice');
            Log::error('http失败|编码:' . $error . '|' . json_encode($logData));
            return false;
        }
//        var_dump(json_encode($data));
        $return = json_decode($response, true);
        if (!$return || empty($return) || !isset($return['code']) || $return['code'] != '200') {
            Log::error('响应内容失败|' . json_encode($logData));
            return false;
        }
        return $return;
    }
}
