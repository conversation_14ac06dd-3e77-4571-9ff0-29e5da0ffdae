<?php

namespace App\Admin\Forms\Trader;

use App\Admin\Renderable\AllProductTable;
use App\Admin\Renderable\TraderFollowUserTable;
use App\Models\ExchangeRate;
use App\Models\Product;
use App\Models\TraderFollowerMoneyChange;
use App\Models\UserMessage;
use App\Models\Users;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use App\Models\TraderFollower;
use App\Models\MoneyChange;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;


class CreateOrderForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $user_ids = $input['user_id'] ?? []; // 用户ID数组
        $product_id = $input['product_id'] ?? 0;
        $direction_data = $input['direction_data'] ?? 0;
        $buy_price = $input['buy_price'] ?? 0;
        $buy_quantity = $input['buy_quantity'] ?? 0;
        $lever = $input['lever'] ?? 1;
        $buy_time = $input['buy_time'];
//        $take_profit = $input['take_profit'] ?? 0;
//        $stop_loss = $input['stop_loss'] ?? 0;

        // 验证参数
        if (empty($user_ids) || !is_array($user_ids)) {
            return $this->response()->error('请选择用户');
        }
        if (empty($product_id) || !is_numeric($product_id)) {
            return $this->response()->error('请选择产品');
        }
        if (!in_array($direction_data, [1, 2])) {
            return $this->response()->error('交易方向不合法');
        }
        if ($buy_price <= 0) {
            return $this->response()->error('买入价格必须大于0');
        }
        if ($buy_quantity <= 0) {
            return $this->response()->error('买入数量必须大于0');
        }
        if ($lever <= 0) {
            return $this->response()->error('杠杆倍数必须大于0');
        }

        DB::beginTransaction();
        try {
            // 获取当前交易员ID
            $traderId = $this->payload['id'] ?? null;
            if (!$traderId) {
                throw new \Exception('交易员ID不能为空');
            }

            // 验证产品是否存在
            $product = Product::where('id', $product_id)
                ->where('deleted_at', null)
                ->where('state', 1)
                ->first();
            if (!$product) {
                throw new \Exception('产品不存在或状态不合法');
            }

            $successCount = 0;
            $failedUsers = [];

            // 为每个用户创建订单
            foreach ($user_ids as $user_id) {
                try {
                    $this->createOrderForUser($user_id, $traderId, $product, [
                        'product_id' => $product_id,
                        'direction_data' => $direction_data,
                        'buy_price' => $buy_price,
                        'buy_quantity' => $buy_quantity,
                        'lever' => $lever,
                        'buy_time' => $buy_time,
                    ]);
                    $successCount++;
                } catch (\Exception $e) {
                    $user = Users::find($user_id);
                    $failedUsers[] = ($user->email ?? "用户ID:{$user_id}") . " - " . $e->getMessage();
                }
            }

            DB::commit();

            $message = "成功为 {$successCount} 个用户创建订单";
            if (!empty($failedUsers)) {
                $message .= "，失败用户：" . implode('; ', $failedUsers);
            }

            return $this->response()->success($message)->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }
    }

    /**
     * 为单个用户创建订单
     */
    private function createOrderForUser($user_id, $trader_id, $product, $orderData)
    {
        // 验证用户是否存在
        $user = Users::find($user_id);
        if (!$user) {
            throw new \Exception('用户不存在');
        }

        // 验证用户是否已经关注当前交易员
        $follower = TraderFollower::where('user_id', $user_id)
            ->where('trader_id', $trader_id)
            ->where('state', 1)
            ->first();
        if (!$follower) {
            throw new \Exception('用户未关注该交易员');
        }

        // todo: 计算买入成本
        $buy_cost_currency = $buy_cost = round($orderData['buy_price'] * $orderData['buy_quantity'] / $orderData['lever'], 2);

        $rate = get_buy_fee_rate($user->id);

        // todo: 计算手续费
        $buy_fees_currency = $buy_fees = buy_commission($buy_cost, $rate);

        // todo: 西班牙市值超过10亿欧元的股票需要加多一个印花税
        // 印花税：0.2%
        $stamp_duty_currency = $stamp_duty = 0;
        if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
            $stamp_duty_currency = $stamp_duty = round($buy_cost * 0.002, 2); // 印花税
        }

        // 如果当前产品不是欧元结算，则需要去计算欧元成本
        $exchange_rate = 1;
        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
            if (in_array($product->pro_type, [2, 3, 4, 5])) {
                // todo: 调用接口获取一次实时的接口

                // 获取当前产品的货币与欧元的汇率
                $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                    ->where('to_currency', 'EUR')
                    ->firstOrFail();
            }else{
                // 获取当前产品的货币与欧元的汇率
                $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                    ->where('to_currency', 'EUR')
                    ->firstOrFail();
            }

            // 要买入的金额
            // 实时 EUR/USD 汇率,
            // 价差值
            $buy_cost = calculateEuroCost($buy_cost, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $buy_fees = calculateEuroCost($buy_fees, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $stamp_duty = calculateEuroCost($stamp_duty, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $exchange_rate = $exchangeRate->rate;
        }

        // 生成订单号
        $order_sn = generateUniqueOrderNumber();

        // 检查用户余额是否足够（从冻结资金中扣除）
        $totalCost = $buy_cost + $buy_fees + $stamp_duty;

        // 验证用户投资总金额是否足够本次建仓
        if ($follower->bet_money < $totalCost) {
            throw new \Exception('用户投资金额不足，需要：' . $totalCost . '，可用：' . $follower->bet_money);
        }

        DB::beginTransaction();
        try {
            // 创建订单
            $order = UserOrder::create([
                'user_id' => $user_id,
                'trade_id' => $trader_id, // 设置交易员ID，标识为跟单订单
                'order_sn' => $order_sn,
                'product_id' => $orderData['product_id'],
                'product_type' => $product->pro_type,
                'direction_data' => $orderData['direction_data'],
                'buy_type' => 2, // 限价单
                'buy_price' => $orderData['buy_price'],
                'buy_quantity' => $orderData['buy_quantity'],
                'lever' => $orderData['lever'],
                'status' => 1, // 持仓中
                'buy_cost' => $buy_cost_currency,
                'buy_time' => strtotime($orderData['buy_time']),
                'buy_fees' => $buy_fees_currency,
                'buy_fees_rate' => $rate,
                'market_cap' => $product->price * $orderData['buy_quantity'],
                'currency' => $product->currency ?: 'EUR', // 默认货币为EUR
                'source' => 1, // 普通买入
                'exchange_rate' => $exchange_rate,
                'user_spread' => $user->spread > 0 ? $user->spread : 0.00036,
                'stamp_duty' => $stamp_duty_currency,
            ]);

            $beforeDongjie = $follower->bet_money; // 冻结前的金额
            // 减少用户投资总金额
            $follower->bet_money -= $totalCost;
            $follower->used_money += $buy_cost;
            if (!$follower->save()) {
                throw new \Exception('更新用户投资金额失败');
            }

            // 记录资金变动 - 买入成本
            $protype = [1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '数字货币'];
            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $user_id,
                'trader_id' => $trader_id,
                'order_sn' => $order_sn,
                'product_name' => $product->name,
                'detailed' => "跟单交易|{$protype[$product->pro_type]}|买入|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                'before_money' => $beforeDongjie,
                'money' => -$buy_cost,
                'after_money' => $beforeDongjie - $buy_cost,
                'created_at' => strtotime($orderData['buy_time']),
                'updated_at' => strtotime($orderData['buy_time']),
            ]);

            // 记录资金变动 - 手续费
            if ($buy_fees > 0) {
                TraderFollowerMoneyChange::create([
                    'money_type' => "EUR",
                    'user_id' => $user_id,
                    'trader_id' => $trader_id,
                    'order_sn' => $order_sn,
                    'product_name' => $product->name,
                    'detailed' => "跟单交易|{$protype[$product->pro_type]}|手续费|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'before_money' => $beforeDongjie - $buy_cost,
                    'money' => -$buy_fees,
                    'after_money' => $beforeDongjie - $buy_cost - $buy_fees,
                    'created_at' => strtotime($orderData['buy_time']),
                    'updated_at' => strtotime($orderData['buy_time']),
                ]);
            }

            // 记录资金变动 - 手续费
            if ($stamp_duty > 0) {
                TraderFollowerMoneyChange::create([
                    'money_type' => "EUR",
                    'user_id' => $user_id,
                    'trader_id' => $trader_id,
                    'order_sn' => $order_sn,
                    'product_name' => $product->name,
                    'detailed' => "跟单交易|{$protype[$product->pro_type]}|印花税|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'before_money' => $beforeDongjie - $buy_cost - $buy_fees,
                    'money' => -$stamp_duty,
                    'after_money' => $beforeDongjie - $buy_cost - $buy_fees - $stamp_duty,
                    'created_at' => strtotime($orderData['buy_time']),
                    'updated_at' => strtotime($orderData['buy_time']),
                ]);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }
        return $order;
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $id = $this->payload['id'] ?? null;

        //$this->confirm('您确定', 'content');
        $this->multipleSelectTable('user_id', '用户')
            ->from(TraderFollowUserTable::make(['id' => $id])) // 设置渲染类实例，并传递自定义参数
            ->model(Users::class, 'id', 'email') // 设置编辑数据显示
            ->required()
            ->rules('required', ['required' => '请选择用户']);
        $this->selectTable('product_id', '产品')
            ->from(AllProductTable::make()) // 设置渲染类实例，并传递自定义参数
            ->model(Product::class, 'id', 'name') // 设置编辑数据显示
            ->required()
            ->rules('required', ['required' => '请选择用户']);

        $this->radio('direction_data', '方向')->options(['1' => '买涨', '2' => '买跌'])->default('1')->required();
        $this->text('buy_price', '买入价格')->required()->rules('required|numeric|min:0.01', ['required' => '请输入买入价格', 'numeric' => '请输入数字', 'min' => '不能低于0']);
        $this->text('buy_quantity', '买入股数')->required()->rules('required|integer|min:1', ['required' => '请输入买入股数', 'numeric' => '请输入整数', 'min' => '不能低于1']);
        $this->text('lever', '杠杆')->default(1)->rules('required|integer|min:1|max:100', ['required' => '请输入杠杆', 'numeric' => '请输入数字', 'min' => '不能低于1', 'max' => '不能高于100'])->required();
        $this->datetime('buy_time', '买入时间')->default(date('Y-m-d H:i:s'));

//        $this->text('take_profit', '止盈金额')->default(0)->rules('numeric|min:0', ['numeric' => '请输入数字', 'min' => '不能低于0'])->help('设置为0表示不设置止盈');
//        $this->text('stop_loss', '止损金额')->default(0)->rules('numeric|min:0', ['numeric' => '请输入数字', 'min' => '不能低于0'])->help('设置为0表示不设置止损');

    }
}
