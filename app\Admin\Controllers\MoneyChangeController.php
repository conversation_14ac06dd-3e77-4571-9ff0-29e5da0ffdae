<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\MoneyChange;
use App\Models\MoneyChange as MoneyChangeModel;
use App\Services\AdminUserService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;


class MoneyChangeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MoneyChange(), function (Grid $grid) {

            $grid->disableCreateButton();
            $grid->disableEditButton();

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('order_sn');
            $grid->column('money_type');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('type')->using(MoneyChangeModel::$TYPE);
            $grid->column('before_money');
            $grid->column('money')->display(function ($money) {
                if ($money >0) {
                    return "<span style='color: #15c832'>{$money}</span>";
                }elseif ($money < 0) {
                    return "<span style='color: red'>{$money}</span>";
                }else{
                    return "{$money}";
                }
            });
            $grid->column('after_money');
            $grid->column('product_name');
            $grid->column('detailed');
            $grid->column('is_show', '展示')->using([0 => '否', 1 => '是'])->badge(
                [
                    1 => 'success',
                    0 => 'gray',
                ],
            );
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();
                $filter->like('user.email', '用户');
                $filter->like('order_sn');
                $filter->equal('type')->select(MoneyChangeModel::$TYPE);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MoneyChange(), function (Show $show) {
            $show->field('id');
            $show->field('money_type');
            $show->field('user_id');
            $show->field('order_sn');
            $show->field('product_name');
            $show->field('detailed');
            $show->field('type');
            $show->field('before_money');
            $show->field('money');
            $show->field('after_money');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new MoneyChange(), function (Form $form) {
            $form->display('id');
            $form->text('money_type');
            $form->text('user_id');
            $form->text('order_sn');
            $form->text('product_name');
            $form->text('detailed');
            $form->text('type');
            $form->text('before_money');
            $form->text('money');
            $form->text('after_money');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
