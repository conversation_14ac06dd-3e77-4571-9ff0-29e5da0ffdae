<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\ProductTable;
use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\DividendOrder;
use App\Models\Product;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Grid\Dividend\Check;

class DividendOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new DividendOrder(), function (Grid $grid) {

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user', 'product']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('order_sn', '订单号');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('product.name', '股票名称');
            $grid->column('product.symbol', '股票代码');
            $grid->column('buy_in_price');
            $grid->column('buy_in_num');
            $grid->column('buy_in_market_val');
            $grid->column('buy_in_fee_rate')->display(function ($buy_in_fee_rate) {
                return "<span style='color: red'>{$buy_in_fee_rate}%</span>";
            });
            $grid->column('buy_in_fee');
            $grid->column('stamp_duty', '印花税');
            $grid->column('exchange_rate', '货币兑换汇率(买入时)');
            $grid->column('user_spread', '用户汇率价差');
            $grid->column('lever', '杠杆');
            $grid->column('success_num');
            $grid->column('success_fee');
            $grid->column('success_in_time');
            $grid->column('state')->using([0 => '待审核', 1 => '审核通过', 2 => '审核拒绝'])->dot(
                [
                    1 => 'success',
                    2 => 'danger',
                ],
                'primary' // 第二个参数为默认值
            );
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();
                $actions->append(new Check());
            });

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new DividendOrder(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('product_id');
            $show->field('buy_in_price');
            $show->field('buy_in_num');
            $show->field('buy_in_market_val');
            $show->field('buy_in_fee_rate');
            $show->field('buy_in_fee');
            $show->field('success_num');
            $show->field('success_fee');
            $show->field('success_in_time');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new DividendOrder(), function (Form $form) {
            $form->display('id');
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()->rules('required')->help('<span style="color: red">请先选用户，否则无法计算手续费。如果多次切换用户，需要重新填写申购价格或申购股数、杠杆，否则无法计算</span>');
            $form->selectTable('product_id')
                ->from(ProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Product::class, 'id', 'name')->required(); // 设置编辑数据显示
            $form->text('buy_in_price')->required()->rules('required|numeric|min:0.01')->help('<span style="color: red">只能输入数字，否则无法计算手续费</span>');
            $form->text('buy_in_num')->required()->rules('required|numeric|min:1')->help('<span style="color: red">只能输入数字，否则无法计算手续费</span>');
            $form->text('lever')->required()->rules('required|numeric|min:1|max:100')->default(1)->help('<span style="color: red">只能输入数字，否则无法计算手续费</span>');
            $form->text('buy_in_market_val')->readonly();
            $form->text('buy_in_fee_rate')->readonly();
            $form->text('buy_in_fee')->readonly();
            $form->text('success_num')->readonly();
            $form->text('success_fee')->readonly();
            $form->text('success_market_val', '成交市值')->readonly();
            $form->datetime('success_in_time')->required()->rules('required')->default(date('Y-m-d H:i:s'));
            $form->radio('state', '操作')->options(['1' => '通过'])
                ->default('1');

            \Dcat\Admin\Admin::script(<<<'JS'
    function triggerDividendCalc() {
        let price = $('input[name="buy_in_price"]').val();
        let num = $('input[name="buy_in_num"]').val();
        let lever = $('input[name="lever"]').val();
        let user_id = $('input[name="user_id"]').val();
        if (!price || !num || !lever || !user_id || price == 0 || num == 0 || lever == 0 || user_id == 0) {
            return;
        }

        Dcat.loading();
        $.post({
    url: '/admin/dividend-order/calculate',
    data: {
        user_id: user_id,
        price: price,
        num: num,
        lever:lever
    },
    success: function(res) {
        Dcat.loading(false);
        if (res.error) {
            Dcat.error(res.error);
            return;
        }

        $('input[name="buy_in_market_val"]').val(res.market_val);
        $('input[name="buy_in_fee"]').val(res.fee);
        $('input[name="buy_in_fee_rate"]').val(res.fee_rate);
        $('input[name="success_num"]').val(res.success_num);
        $('input[name="success_fee"]').val(res.fee);
        $('input[name="success_market_val"]').val(res.success_market_val);
    },
    error: function() {
        Dcat.loading(false);
        Dcat.error('计算失败，请稍后再试');
    }
});
    }

    $(document).on('input', 'input[name="buy_in_price"], input[name="buy_in_num"], input[name="lever"]', Dcat.helpers.debounce(triggerDividendCalc, 500));
JS
            );

        });
    }

    public function calculateDividend()
    {
        $user_id = request('user_id');
        $price = request('price');
        $num = request('num');
        $lever = request('lever');

        if (!$user_id || !$price || !$num || !$lever) {
            return response()->json(['error' => '参数不完整'], 422);
        }

        // 根据用户获取手续费率，如果

        $marketVal = ($price * $num) / $lever;
        $rate = get_buy_fee_rate($user_id);

        $fee = buy_commission($marketVal, $rate); // 手续费
        $successNum = $num; // 成交数量 = 买入数量
        $successMarketVal = $marketVal; // 成交市值 = 市值

        return response()->json([
            'market_val' => $marketVal,
            'fee' => $fee, 2,
            'fee_rate' => $rate,
            'success_num' => $successNum,
            'success_market_val' => round($successMarketVal, 2),
        ]);
    }
}
