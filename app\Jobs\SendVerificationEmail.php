<?php

namespace App\Jobs;

use App\Services\EmailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendVerificationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $email;
    public $code;
    public $type;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 60;

    /**
     * Create a new job instance.
     *
     * @param string $email
     * @param string $code
     * @param string $type
     */
    public function __construct($email, $code, $type = 'register')
    {
        $this->email = $email;
        $this->code = $code;
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // 简化日志记录，避免权限问题
            \Log::info("Processing email job for: {$this->email}");

            $result = EmailService::sendVerificationCode($this->email, $this->code, $this->type);

            if (!$result) {
                throw new \Exception("Failed to send verification email to {$this->email}");
            }

            \Log::info("Email job completed successfully for: {$this->email}");

        } catch (\Exception $e) {
            \Log::error("Email job failed for {$this->email}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception)
    {
        Log::error("Email job failed after all retries", [
            'email' => $this->email,
            'type' => $this->type,
            'error' => $exception->getMessage()
        ]);
    }
}
