# 订单API测试文档

## 🎯 **高性能实时订单列表API（完整格式）**

### 1. 订单列表接口

**接口地址：** `GET /api/user-orders`

**请求参数：**
```json
{
    "status": 1,           // 可选，订单状态：1=持仓中,2=已平仓,3=平仓中,4=挂单中,5=已撤单
    "currency": "eur"      // 可选，币种：eur,usd,gbp,jpy,myr，默认eur
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "orders": [
            {
                "id": 123,
                "order_sn": "ORD20241203001",
                "order_type": "self",
                "trade_id": 0,
                "product_id": 456,
                
                // 基础交易信息
                "direction_data": 1,
                "direction_text": "买涨",
                "buy_type": 1,
                "buy_type_text": "市价",
                "buy_price": 100.50,
                "buy_quantity": 10,
                "buy_cost": 1005.00,
                "buy_fees": 5.03,
                "lever": 1,
                "status": 1,
                "status_text": "持仓中",
                "currency": "eur",
                
                // 实时盈亏信息
                "current_price": 102.30,
                "profit_amount": 18.00,
                "profit_percentage": 1.79,
                "market_cap": 1023.00,
                
                // 止盈止损信息
                "take_profit": 50.00,
                "stop_loss": 20.00,
                "auto_close_info": {
                    "can_take_profit": false,
                    "can_stop_loss": false,
                    "take_profit_distance": 32.00,
                    "stop_loss_distance": -38.00,
                    "risk_level": "normal"
                },
                
                // 显示信息
                "display_type": "product",
                "display_name": "苹果公司",
                "display_symbol": "AAPL",
                "display_icon": "/images/aapl.png",
                
                // 产品信息
                "product_info": {
                    "id": 456,
                    "name": "苹果公司",
                    "symbol": "AAPL",
                    "icon": "/images/aapl.png",
                    "currency": "usd",
                    "pro_type": 1,
                    "country": "us"
                },
                
                // 时间信息
                "buy_time": 1701590400,
                "sell_time": null,
                "created_at": "2024-12-03T10:00:00Z"
            }
        ],
        "statistics": {
            "total_count": 45,
            "self_count": 30,
            "follow_count": 15
        },
        "server_time": "2024-12-03T12:30:45Z"
    }
}
```

### 2. 交易员订单详情接口

**接口地址：** `GET /api/trader-orders/{traderId}`

**请求参数：**
```json
{
    "currency": "eur"      // 可选，币种过滤
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "trader_info": {
            "id": 789,
            "name": "张三",
            "nickname": "股神张三",
            "avatar": "/avatars/trader789.jpg",
            "introduction": "专业股票交易员",
            "level": 2,
            "level_text": "精英"
        },
        "statistics": {
            "total_orders": 25,
            "holding_orders": 5,
            "closed_orders": 20,
            "pending_orders": 0,
            "total_cost": 50000.00,
            "total_profit": 2500.00,
            "total_market_cap": 12500.00,
            "buy_orders": 15,
            "sell_orders": 10,
            "win_rate": 75.50
        },
        "orders": [
            // 订单详情数组，格式同上
        ]
    }
}
```

## 🚀 **性能优化特性**

### 1. 缓存策略
- ✅ **静态数据缓存5分钟**：订单基本信息、产品信息
- ❌ **价格数据不缓存**：实时获取最新股票价格
- ✅ **智能更新**：只对持仓中的订单获取实时价格

### 2. 查询优化
- 只查询必要字段，减少数据传输
- 批量获取价格，减少API调用
- 按产品类型分组处理（股票vs其他）
- 按国家分组处理股票（不同国家不同API）

### 3. 实时性保证
- 股票价格直接从JiangshanService获取
- 其他产品从TwelvedataNewService获取
- 盈亏计算与AutoOrderDeal完全一致
- 止盈止损状态实时判断

## 📊 **前端轮询建议**

```javascript
// 建议的前端轮询策略
const pollInterval = 5000; // 5秒轮询
const maxRetries = 3;

async function fetchOrders() {
    try {
        const response = await fetch('/api/user-orders', {
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });
        
        const data = await response.json();
        if (data.code === 200) {
            updateOrderList(data.data);
        }
    } catch (error) {
        console.error('获取订单失败:', error);
        handleRetry();
    }
}

// 根据页面可见性调整轮询频率
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // 页面隐藏时降低频率
        clearInterval(pollTimer);
        pollTimer = setInterval(fetchOrders, 30000); // 30秒
    } else {
        // 页面可见时恢复正常频率
        clearInterval(pollTimer);
        pollTimer = setInterval(fetchOrders, 5000); // 5秒
    }
});
```

## 🔧 **测试命令**

```bash
# 测试订单列表
curl -X GET "http://your-domain/api/user-orders?status=1&currency=eur" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试交易员订单详情
curl -X GET "http://your-domain/api/trader-orders/123?currency=eur" \
     -H "Authorization: Bearer YOUR_TOKEN"
```
