<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Dividend;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Renderable\ProductTable;
use App\Models\Product;


class DividendController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Dividend(), function (Grid $grid) {
            $model = $grid->model()->with(['product'])->where('type', 1);

            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('product.name', '股票名称');
            $grid->column('product.symbol', '股票代码');
            $grid->column('buy_in_price');
            $grid->column('buy_in_ratio');
            $grid->column('start_time');
            $grid->column('end_time');
//            $grid->column('type');
            $grid->column('state')->switch();
//            $grid->column('refer_scope');
//            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Dividend(), function (Show $show) {
            $show->field('id');
            $show->field('product_id');
            $show->field('buy_in_price');
            $show->field('buy_in_ratio');
            $show->field('start_time');
            $show->field('end_time');
            $show->field('type');
            $show->field('state');
            $show->field('refer_scope');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Dividend(), function (Form $form) {
            $form->display('id');
            $form->selectTable('product_id')
                ->from(ProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Product::class, 'id', 'name')->required(); // 设置编辑数据显示
            $form->number('buy_in_price')->required()->rules('required|numeric|min:0.01', ['required' => '必须填入买入价格', 'numeric'=>'请输入数字', 'min'=>'不能低于0.01']);
            $form->display('buy_in_ratio')->default(1);
            $form->datetime('start_time')->required();
            $form->datetime('end_time')->required();
            $form->hidden('type')->default(1);
            $form->switch('state');
//            $form->text('refer_scope');

            $form->display('created_at');
            $form->display('updated_at');

            $form->saving(function (Form $form) {
                if (strtotime($form->end_time) < strtotime($form->start_time)) {
                    throw new \Exception('结束时间必须大于开始时间');
                }
            });
        });
    }
}
