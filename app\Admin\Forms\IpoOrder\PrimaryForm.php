<?php

namespace App\Admin\Forms\IpoOrder;

use App\Models\ExchangeRate;
use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\UserMessage;
use App\Models\Users;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;


class PrimaryForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $zhongqianshu = $input['zhongqianshu'] ?? 0;
        $state = $input['state'] ?? 0;


        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!is_numeric($zhongqianshu)) {
            return $this->response()->error('中签股数不是数字类型');
        }

        if ($zhongqianshu <= 0) {
            return $this->response()->error('中签股数必须要大于0');
        }

        if (!in_array($state, [1, 2])) {
            return $this->response()->error('操作错误');
        }


        DB::beginTransaction();
        try {
            $ipoOrder = IpoOrder::find($id);
            $ipo = Ipo::query()->find($ipoOrder->ipo_id);
            if (!$ipoOrder) {
                throw  new \Exception('数据不存在');
            }
            if ($ipoOrder->state != 0) {
                throw  new \Exception('不是申购中，不允许设置中签');
            }
            if ($state == 1) {
                $buy_price = $ipoOrder->buy_price;

                $users = Users::query()->where('id', $ipoOrder->user_id)->first();

                $exchange_rate = 1;
                // 如果不是德国和西班牙的IPO
                if (!in_array($ipo->country, ['de', 'sp'])) {
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();

                    // 换算金额和费用
                    $buy_price = calculateEuroCost($buy_price, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);
                    $exchange_rate = $exchangeRate->rate;
                }

                $money = $buy_price * $zhongqianshu;
                $rate = $ipoOrder->buy_in_fee_rate;
                $fees = buy_commission($money, $rate);

                $allAmount = $money + $fees;

                $ipoOrder->exchange_rate = $exchange_rate;
                $ipoOrder->user_spread = $users->spread > 0 ? $users->spread : 0.00036;
                $ipoOrder->zhongqianshu = $zhongqianshu;
                $ipoOrder->yingrenjiao = $allAmount;
                $ipoOrder->xurenjiao = $allAmount;

                $message = [
                    'user_id' => $ipoOrder->user_id,
                    'content' => "尊敬的会员|,|您申购的新股|{$ipo->name}|代码|{$ipo->symbol}|已中签|,|中签数量是|:|{$ipoOrder->zhongqianshu}|股|!"
                ];

            } else {
                $ipoOrder->zhongqianshu = 0;
                $ipoOrder->yingrenjiao = 0;
                $ipoOrder->xurenjiao = 0;
                $message = [
                    'user_id' => $ipoOrder->user_id,
                    'content' => "尊敬的会员|,|您申购的新股|{$ipo->name}|代码|{$ipo->symbol}|未能中签|!"
                ];
            }
            $ipoOrder->state = $state;
            $ipoOrder->save();

            UserMessage::create($message);


            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');
        $this->text('order_sn', '订单号')->readOnly();
        $this->text('user')->readOnly();
        $this->text('buy_price')->readOnly();
        $this->text('buy_num')->readOnly();
        $this->text('zhongqianshu', '中签股数')->required()->rules('required|numeric|min:1', [
            'required' => '请输入中签股数',
            'numeric' => '中签股数',
            'min' => '中签股数必须要大于0',
        ]);
        $this->datetime('chouqian_date', '抽签日期')->readOnly();
        $this->radio('state', '操作')->options(['1' => '中签', '2' => '拒签'])->required()->rules('required', ['required' => '请选择操作']);
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        $data = IpoOrder::find($id);

        return [
            'order_sn' => $data->order_sn,
            'user' => $data->user->email,
            'buy_num' => $data->buy_num,
            'buy_price' => $data->buy_price,
            'chouqian_date' => $data->ipo->chouqian_date,
            'zhongqianshu' => $data->buy_num,
//            'success_num' => $data->buy_in_num,
//            'created_at' => $data->created_at,
        ];
    }
}
