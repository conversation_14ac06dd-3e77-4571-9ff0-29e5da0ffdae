<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\ProductTable;
use App\Admin\Repositories\BigTrade;
use App\Models\Product;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class BigTradeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new BigTrade(), function (Grid $grid) {

            $model = $grid->model()->with(['product']);

            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('product.name', '股票名称');
            $grid->column('product.symbol', '股票代码');
            $grid->column('buy_in_price');
//            $grid->column('nums');
            $grid->column('state')->switch();
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new BigTrade(), function (Show $show) {
            $show->field('id');
            $show->field('product_id');
            $show->field('buy_in_price');
            $show->field('nums');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new BigTrade(), function (Form $form) {
            $form->display('id');
            $form->selectTable('product_id')
                ->from(ProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Product::class, 'id', 'name')->required(); // 设置编辑数据显示
            $form->number('buy_in_price')->required()->rules('required|numeric|min:0.01',['required' => '必须填入买入价格', 'numeric'=>'请输入数字', 'min'=>'不能低于0.01']);
//            $form->text('nums');
            $form->switch('state');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
