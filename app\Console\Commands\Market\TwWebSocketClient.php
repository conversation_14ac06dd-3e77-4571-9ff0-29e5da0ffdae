<?php

namespace App\Console\Commands\Market;

use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Workerman\Lib\Timer;
use Workerman\Worker;
use Workerman\Connection\AsyncTcpConnection;
use Illuminate\Support\Facades\Redis;


class TwWebSocketClient extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'market:tw_ws {worker_command}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'WebSocket 客户端：连接服务端并动态订阅频道';

    protected $wsUrl = 'ws://ws.twelvedata.com/v1/quotes/price?apikey=********************************'; // 修改为你的 WebSocket 服务地址


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 设置日志路径（推荐存放到 Laravel 的 storage/logs）
        Worker::$logFile = storage_path('logs/workerman.log');
        Worker::$stdoutFile = storage_path('logs/workerman_stdout.log');
        Worker::$pidFile = storage_path('logs/workerman.pid');
        $worker = new Worker();
        $worker->count = 1;
        $worker->name = '12 Data Websocket';

        $path = storage_path() . '/gateway/';
        file_exists($path) || @mkdir($path);
        Worker::$pidFile = $path . 'websocket.pid';

        $worker->onWorkerStart = function () {
            $this->startWebSocket();
        };

        Worker::runAll();
    }

    protected function startWebSocket()
    {
        $this->connection = new AsyncTcpConnection($this->wsUrl);

        // 可选：自签名证书时需关闭验证
        $this->connection->transport = 'ssl';


        $this->connection->onConnect = function ($conn) {
            $this->info("已连接 WebSocket 服务器");

            // 需要从关注表中，分组组当前关注的产品，然后去订阅 TODO
//            $subscribedChannels = ['AAPL'];
            $subscribedChannels = Product::query()->whereIn('pro_type', [2, 3, 4, 5])->where('state', 1)->pluck('symbol')->toArray();

            $this->sendInitSubscribe($subscribedChannels);

            Timer::add(10, function () use ($conn) {
                $conn->send(json_encode(['action' => 'heartbeat']));
            });
        };

        $this->connection->onMessage = function ($conn, $msg) {
            $this->info("收到消息：{$msg}");
            // 解码 JSON 数据
            $data = json_decode($msg, true);

            // 检查解码是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                $errorMsg = "无效的 JSON 数据: " . json_last_error_msg();
                $this->error($errorMsg);
                return;
            }

            if (isset($data['event'])) {
                if ($data['event'] == 'price') {
                    $this->executeRes($data);
                }

            } else {
                $this->error('未检测到对应字段:' . $msg);
            }

        };

        $this->connection->onClose = function () {
            $this->error("WebSocket 连接已关闭");
        };

        $this->connection->onError = function ($conn, $code, $msg) {
            $this->error("连接错误: [{$code}] {$msg}");
        };

        $this->connection->connect();
    }


    protected function sendInitSubscribe($subscribedChannels)
    {
        if (!$this->connection) return;

        $payload = [
            'action' => 'subscribe',
            'params' => [
                'symbols' => implode(',', array_values($subscribedChannels)),
            ],
        ];
        $this->connection->send(json_encode($payload));

        $this->info("发送订阅消息: " . json_encode($payload));
    }

    /**
     * 处理ws接收到的数据
     */
    protected function executeRes($data)
    {
        $this->info("开始处理数据");

        // 更新价格
        $symbol = $data['symbol'];
        $nowPrice = $data['price'];
        // 根據代碼找產品
        $searchProduct = Product::query()->where('symbol', $symbol)->whereIn('pro_type', [2, 3, 4, 5])->first();
        if (!$searchProduct) {
            $this->error("產品不存在：{$symbol}");
            return false;
        }
        $product = Product::find($searchProduct->id);
        $product->price = $nowPrice;
        $product->save();
        $this->info("==========={$symbol}當前價格更新完畢！！！=================");

        $this->info("开始持倉更新");
        // 更新持仓 TODO

        // 获取持仓和挂单订单
        $orders = UserOrder::query()->where('product_id', $product->id)->whereIn('status', [1, 4])->get();
        if ($orders->isEmpty()) {
            echo '没有持仓订单：' . json_encode($data) . "\r\n";
            return false;
        }

        //=========================更新挂单 持仓===========
        echo "更新满足条件的买涨买跌的挂单 为持仓\r\n";
        // 好像我设置的价格，只要低于当前加，都可以转持仓，不论买涨买跌
        // 方向1 涨 2跌
        UserOrder::query()
            ->where('product_id', $product->id)
            ->where('buy_type', 2)
            ->where('direction_data', 1)
            ->where('buy_price', '>=', $nowPrice)
            ->where('status', 4)
            ->update(['status' => 1]);

        UserOrder::query()
            ->where('product_id', $product->id)
            ->where('buy_type', 2)
            ->where('direction_data', 2)
            ->where('buy_price', '<=', $nowPrice)
            ->where('status', 4)
            ->update(['status' => 1]);
        echo "更新市值盈亏\r\n";
        //=========================更新挂单 持仓end===========

        foreach ($orders as $item) {

            DB::beginTransaction();
            try {
                $order = UserOrder::find($item->id);

                //=========================更新市值，赢亏===========
                // 根据当前价格- 更新满足条件的买涨买跌的挂单 为持仓
                //  `buy_type` '买入类型:1=市价,2=限价',
                // 买入方向:2=买跌,1=买涨
                $shizhi = round($nowPrice * $order->buy_quantity, 3);
                if ($order['direction_data'] == '1') {    //如果是多单 长
                    $multiplier = 1;
                    $yingkui = round(($nowPrice - $order->buy_price) * $order->buy_quantity * $multiplier, 3);
                } else {    //如果是空单 跌
                    $multiplier = 1;
                    $yingkui = round(($order->buy_price - $nowPrice) * $order->buy_quantity * $multiplier, 3);
                }
                //=========================更新市值，赢亏===========

                // 持仓情况
                if ($order->status == 1) {
                    // 止盈止损的处理
                    if ($order->take_profit > 0 || $order->stop_loss > 0) {
                        echo "开始止盈止损的处理\r\n";
                        // 是否锁仓
                        if ($order->lock == 1) {
                            echo '止盈止损处理，已锁仓：订单号|' . $order->order_sn . "\r\n";
                            DB::commit();
                            continue;
                        }

                        if ($order->direction_data == 1) {  //如果是多单 长
                            if ($nowPrice >= $order->take_profit) {
                                //止盈卖出
                                $selltype = 3;//止盈卖出
                                $pc_price = $order->take_profit;
                            }
                            if ($nowPrice <= $order->stop_loss) {
                                $selltype = 4;//止损卖出
                                $pc_price = $order->stop_loss;
                            }
                        } else {    //如果是空单 跌
                            if ($nowPrice <= $order->take_profit) {
                                $selltype = 3;//止盈卖出
                                $pc_price = $order->take_profit;
                            }
                            if ($nowPrice >= $order['stop_loss_price']) {
                                $selltype = 4;//止损卖出
                                $pc_price = $order['stop_loss_price'];
                            }
                        }

                        if (!isset($selltype)) {
                            echo '止盈止损未满足条件：订单号|' . $order->order_sn . "\r\n";
                            DB::commit();
                            continue;
                        }

                        /**
                         * 买涨的情况 盈亏 = 现价-买入价 （正数为盈利、负数为亏损）
                         * 买跌的情况 盈亏 = 买入价-现价（正数为盈利、负数为亏损）
                         */
                        if ($order['fangxiang_data'] == '1') {    // 如果是买多
                            $multiplier = 1;
                            $money = round(($pc_price - $order['price']) * $order['shuliang'] * $multiplier, 2);
                        } else {    // 如果是买空
                            $multiplier = 1;
                            $money = round(($order['price'] - $pc_price) * $order['shuliang'] * $multiplier, 2);
                        }

                        $sellMoney = $pc_price * $order->buy_quantity;
                        $sellFees = sell_commission($sellMoney, $order->user_id);

                        // 判断证交费是否==0
                        $se_fees_money = 0;
                        $benjin = $pc_price * $order->buy_quantity;
                        $se_fees = SystemConfig::getValue('see_fees');
                        if ($order['lever'] > 1) {
                            $benjin = $benjin * $order['lever'];
                        }

                        // 修改持仓数据
                        $order->status = 2; //平仓
                        $order->sell_price = $pc_price; //平仓价格
                        $order->sell_money = $sellMoney; // 卖出市值
                        $order->sell_time = time(); // 卖出时间
                        $order->sell_fees = $sellFees['sxf'] + $sellFees['see_fees']; // 卖出手续费
                        $order->sell_type = $selltype; // 卖出类型
                        $order->profit_amount = $money; // 赢亏金额
                        $order->save();
                        $userInfo = UsersInfo::query()->where('user_id', $order->user_id)->lockForUpdate()->first();


                        $wallet = getAccountByPro($product);
                        $balanceField = $wallet['balance_field'];
                        $dongjieField = $wallet['dongjie_field'];
                        $currency = $wallet['currenct'];
                        $before = $userInfo->$balanceField;

                        // 最终平仓金额要减去手续费
                        $pingcangjine = $order->buy_cost + $money - ($sellFees['sxf'] + $sellFees['see_fees']);

                        $userInfo->$balanceField += $pingcangjine;
                        $userInfo->$dongjieField -= $order->buy_cost;
                        $userInfo->save();

                        MoneyChange::create([
                            'money_type' => $currency,
                            'user_id' => $order->user_id,
                            'order_sn' => $order->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "平仓订单|代码|{$product->symbol}|产品|{$product->name}",
                            'type' => MoneyChange::TYPE_TRADE_SELL,
                            'before_money' => $before,
                            'money' => ($order->buy_cost + $money),
                            'after_money' => $before + ($order->buy_cost + $money),
                        ]);

                        if ($sellFees['sxf'] > 0) {
                            MoneyChange::create([
                                'money_type' => $currency,
                                'user_id' => $order->user_id,
                                'order_sn' => $order->order_sn,
                                'product_name' => $product->name,
                                'detailed' => "平仓订单手续费|代码|{$product->symbol}|产品|{$product->name}",
                                'type' => MoneyChange::TYPE_TRADE_FEE_SELL,
                                'before_money' => $before + ($order->buy_cost + $money),
                                'money' => -$sellFees['sxf'],
                                'after_money' => $before + ($order->buy_cost + $money) - $sellFees['sxf'],
                            ]);
                        }
                        if ($sellFees['see_fees'] > 0) {
                            MoneyChange::create([
                                'money_type' => $currency,
                                'user_id' => $order->user_id,
                                'order_sn' => $order->order_sn,
                                'product_name' => $product->name,
                                'detailed' => "平仓订单证交费|代码|{$product->symbol}|产品|{$product->name}",
                                'type' => MoneyChange::TYPE_TRADE_FEE_SELL,
                                'before_money' => $before + ($order->buy_cost + $money) - $sellFees['sxf'],
                                'money' => -$sellFees['see_fees'],
                                'after_money' => $before + ($order->buy_cost + $money) - $sellFees['sxf'] - $sellFees['see_fees'],
                            ]);
                        }
                    } else {
                        // 正常挂持仓更新
                        echo date('Y-m-d H:i:s') . '实时更新持仓中的订单盈亏：' . json_encode($data) . "\r\n";
                        echo "开始更新盈虧=========================" . $order['order_sn'] . "\r\n";

                        // 加了杠杆的情况下，如果用户亏损的钱超过了他的买入本金，就立刻平仓
                        if ($order->lever > 1) {
                            echo $order->order_sn . "设置了杠杆\r\n";
                            if ($yingkui < 0 && $order->buy_cost + $yingkui <= 0) {
                                echo $order->order_sn . "设置了杠杆===用户亏损的钱超过了他的买入本金，执行平仓\r\n";
                                $order->status = 2;
                                $order->sell_type = 2;
                                $order->sell_time = time();
                                $order->sell_fees = 0; // 卖出手续费
                                $order->sell_price = $nowPrice; // 卖出价格
                                $order->profit_amount = -$order->buy_cost; // 赢亏

                                $userInfo = UsersInfo::query()->where('user_id', $order->user_id)->lockForUpdate()->first();


                                $wallet = getAccountByPro($product);
                                $balanceField = $wallet['balance_field'];
                                $dongjieField = $wallet['dongjie_field'];
                                $currency = $wallet['currenct'];

                                $userInfo->$dongjieField -= $order->buy_cost;
                                $userInfo->save();
                                echo "股票杠杆强制平仓成功==" . date('Y-m-d H:i:s') . "==" . $order->order_sn . "\r\n";
                            }
                            $order->market_cap = $shizhi; // 当前市值
                        } else {
//                            var_dump($shizhi, $yingkui);
                            $order->market_cap = $shizhi;
                            $order->profit_amount = $yingkui;
                        }
                        $order->save();
                    }
                }
                DB::commit();
                echo "處理完成\r\n";
            } catch (\Exception $e) {
                DB::rollBack();
                echo "系统错误" . $e->getMessage() . "\r\n";
            }
        }
    }
}
