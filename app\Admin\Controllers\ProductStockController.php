<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ProductStock;
use App\Models\Product as Model;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class ProductStockController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new ProductStock(), function (Grid $grid) {

            if (!Admin::user()->inRoles(['administrator'])) {
                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchActions();
            }

            $country = request()->get('country');

            $model = $grid->model();
            if (!$country) {
                $model->where('country', 'de');
            }
            $model->where('pro_type', Model::$PRO_TYPE_STCOK)
                ->orderBy('state', 'desc')
                ->orderBy('weight', 'desc');
//                ->orderBy('id', 'desc');

            $grid->selector(function (Grid\Tools\Selector $selector) {
//                $selector->selectone('exchange', '交易所', [
//                    'XNAS' => '纳克达斯',
//                    'NYSE' => '纽约证券交易所',
//                    'FWB' => '法兰克福证券交易所',
//                    'XETR' => 'XETR(德)',
//                    'Euronext Paris' => '巴黎泛欧交易所',
//                    'MYX' => '馬來西亞股票交易所',
//                    'TSE' => '东京证券交易所',
//                    'LSE' => '伦敦证券交易所',
//                    'BME' => '马德里证券',
//                    'AQUIS' => 'AQUIS',
//                    'ARCX' => 'ARCX',
//                    'BATS' => 'BATS',
//                    'FSE' => '福岡證券交易所',
//                    'LSIN' => 'LSIN(英)',
//                    'NAG' => '名古屋證券交易所',
//                    'OTC' => 'OTC',
//                    'SAPSE' => 'SAPSE',
//                    'STOCKS' => 'STOCKS',
//                    'XASE' => 'XASE',
//                    'XNYS' => 'XNYS',
//                ]);

//                $selector->selectone('country', '国家', [
//                    'us' => '美国',
//                    'de' => '德国',
//                    'sp' => '西班牙',
//                    'fr' => '法国',
//                    'en' => '英国',
//                    'jp' => '日本',
//                    'my' => '马来西亚',
//                ]);
            });


            $grid->column('id')->sortable();
//            $grid->column('logo')->image('', 50, 50);
//            $grid->column('category_id');
            $grid->column('symbol');
            $grid->column('name');
//            $grid->column('currency');
            $grid->column('exchange')->using(Model::$PRO_STOCK_EXCHANGE);
//            $grid->column('mic_code');
            $grid->column('country');
//            $grid->column('type');
//            $grid->column('figi_code');
//            $grid->column('cfi_code');
//            $grid->column('isin');
//            $grid->column('timestamp');
//            $grid->column('last_quote_at');
//            $grid->column('datetime');
            $grid->column('price');
            $grid->column('open');
            $grid->column('high');
            $grid->column('low');
            $grid->column('close');
            $grid->column('volume');
            $grid->column('previous_close');
            $grid->column('change')->display(function ($change) {
                if ($this->percent_change > 0) {
                    return "<span style='color:#00a870'>{$change}⬆</span>";
                } elseif ($this->percent_change < 0) {
                    return "<span style='color:red'>{$change}⬇</span>";
                } else {
                    return $change;
                }
            });
            $grid->column('percent_change')->display(function ($percent_change) {
                if ($percent_change > 0) {
                    return "<span style='color:#00a870'>{$percent_change}⬆</span>";
                } elseif ($percent_change < 0) {
                    return "<span style='color:red'>{$percent_change}⬇</span>";
                } else {
                    return $percent_change;
                }
            });
//            $grid->column('average_volume');
            $grid->column('is_hot')->switch();
            $grid->column('state')->switch();
//            $grid->column('fifty_two_week');
            $grid->column('is_market_open')->display(function ($is_market_open) {
                if ($is_market_open == 1) {
                    return '<span style="color: #14b114">是</span>';
                } else {
                    return '<span style="color: #bdc1bd">否</span>';
                }
            });
//            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->quickSearch(['name', 'symbol']);
            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();
                $filter->expand(); // 让筛选默认展开
                $filter->equal('country')->select([
                    'us' => '美国',
                    'de' => '德国',
                    'sp' => '西班牙',
                    'fr' => '法国',
                    'en' => '英国',
                    'jp' => '日本',
                    'my' => '马来西亚',
                ])
                    ->load('exchange', admin_url('/product_stock_country_exchange'))
                    ->default('de')->width(3);
                // 交易所下拉（初始为空，选了国家后自动加载）
                $filter->equal('exchange')->select()->width(3);


                $filter->equal('symbol')->width(2);
                $filter->equal('state')->select(['0' => '禁用', '1' => '启用'])->width(2);
                $filter->equal('is_hot')->select(['0' => '否', '1' => '是'])->width(2);
            });

            $grid->paginate(10);

            // 禁用分页选择器
            $grid->disablePerPages();
        });
    }

    public function exchangeByCountry(Request $request)
    {
        $country = $request->get('q', 'de');

        $countryExchange = Model::$PRO_STOCK_COUNTRY_EXCHANGE;
        $ret = [];
        foreach ($countryExchange[$country] as $k => $exchange) {
            $ret[] = [
                'id' => $k,
                'text' => $exchange,
            ];
        }
        return response()->json($ret);
//        return Admin::json();
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ProductStock(), function (Show $show) {
            $show->disableEditButton();
            $show->disableDeleteButton();
            $show->field('id');
            $show->field('category_id');
            $show->field('symbol');
            $show->field('name');
            $show->field('currency');
            $show->field('exchange');
            $show->field('mic_code');
            $show->field('country');
            $show->field('type');
            $show->field('figi_code');
            $show->field('cfi_code');
            $show->field('isin');
            $show->field('timestamp');
            $show->field('last_quote_at');
            $show->field('datetime');
            $show->field('price');
            $show->field('open');
            $show->field('high');
            $show->field('low');
            $show->field('close');
            $show->field('volume');
            $show->field('previous_close');
            $show->field('change');
            $show->field('percent_change');
            $show->field('average_volume');
            $show->field('is_market_open');
            $show->field('fifty_two_week');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ProductStock(), function (Form $form) {
            $form->display('id');
            $form->text('category_id');
            $form->text('symbol');
            $form->text('name');
            $form->text('currency');
            $form->text('exchange');
            $form->text('mic_code');
            $form->text('country');
            $form->text('type');
            $form->text('figi_code');
            $form->text('cfi_code');
            $form->text('isin');
            $form->text('timestamp');
            $form->text('last_quote_at');
            $form->text('datetime');
            $form->text('price');
            $form->text('open');
            $form->text('high');
            $form->text('low');
            $form->radio('close');
            $form->text('volume');
            $form->text('previous_close');
            $form->text('change');
            $form->text('percent_change');
            $form->text('average_volume');
            $form->switch('is_market_open');
//            $form->text('fifty_two_week');
            $form->switch('is_hot');
            $form->switch('state');
            // 数字输入框
            $form->number('weight');

//            $form->display('created_at');
//            $form->display('updated_at');
        });
    }
}
