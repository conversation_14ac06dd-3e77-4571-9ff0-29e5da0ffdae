<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\Withdraw\Check;
use App\Admin\Repositories\Withdraw;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class WithdrawController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Withdraw(), function (Grid $grid) {

            // 禁用行选择器（去掉勾选框）
            $grid->disableRowSelector();
            $grid->disableCreateButton();


            $model = $grid->model()->with(['user', 'cardData']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('order_sn');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('exchange_rate', '货币兑换汇率(买入时)');
            $grid->column('user_spread', '用户汇率价差');
            $grid->column('user.email', '账户');
            $grid->column('type', '提款方式')->using([1 => '银行卡', 2 => 'USDT']);
            $grid->column('card', '银行卡')->display(function ($card) {
                if ($this->type == 1) {
                    $html = "";
                    $html .= "<p>银行：{$this->cardData->bank}</p>";
                    $html .= "<p>持卡人姓名：{$this->cardData->bank_owner}</p>";
                    $html .= "<p>卡号：{$this->cardData->bank_card_no}</p>";
                    return $html;
                } else {
                    $html = "";
                    $html .= "<p>网络协议：{$this->cardData->network}</p>";
                    $html .= "<p>地址：{$this->cardData->usdt_address}</p>";
                    return $html;
                }
            });
            $grid->column('money');
            $grid->column('state')->display(function ($state) {
                if ($state == 1) {
                    return '<span style="color: #43bd43">已审核</span>';
                } elseif ($state == 2) {
                    return '<span style="color: #bc0f24">审核拒绝</span>';
                } else {
                    return '<span style="color: #cac8c8">待审核</span>';
                }
            });
            $grid->column('remark');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();


            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
                if ($actions->row->state == 0) {
                    if (Admin::user()->can('funds-withdraw-check')) {
                        $actions->append(new Check());
                    }

                }

            });


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Withdraw(), function (Show $show) {
            // ✅ 移除右上角的删除按钮
            $show->disableDeleteButton();
            $show->disableEditButton();
            $show->field('id');
            $show->field('user_id');
            $show->field('order_sn');
            $show->field('type');
            $show->field('user_card_id');
            $show->field('money');
            $show->field('state');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Withdraw(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('order_sn');
            $form->text('type');
            $form->text('user_card_id');
            $form->text('money');
            $form->text('status');
            $form->text('remark');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
