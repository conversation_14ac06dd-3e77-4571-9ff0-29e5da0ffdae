<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\RechargeUsdt;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeUsdtController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RechargeUsdt(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('type');
            $grid->column('address');
            $grid->column('state')->switch();
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RechargeUsdt(), function (Show $show) {
            $show->field('id');
            $show->field('type');
            $show->field('name');
            $show->field('address');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeUsdt(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('type')->help('TRC20或ERC20');;
            $form->text('address');
            $form->switch('state');

//            $form->display('created_at');
//            $form->display('updated_at');
        });
    }
}
