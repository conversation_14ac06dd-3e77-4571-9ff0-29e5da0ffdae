<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use App\Services\JiangshanService;
use App\Services\TwelvedataNewService;
use Illuminate\Http\Request;

class ProductController extends ApiBaseController
{
    /**
     * 产品搜索接口（精确搜索，实时数据）
     */
    public function search(Request $request)
    {
        $keyword = $request->input('keyword', '');
        $pro_type = $request->input('pro_type'); // 产品类型筛选
        $limit = $request->input('limit', 50); // 限制返回数量，默认50

        // 验证参数
        if (empty($keyword) || strlen(trim($keyword)) < 1) {
            return $this->returnMsg(422, __('api.搜索关键词不能为空'));
        }

        // 限制搜索关键词长度
        $keyword = trim($keyword);
        if (strlen($keyword) > 50) {
            return $this->returnMsg(422, __('搜索关键词过长'));
        }

        try {
            $query = Product::query()
                ->where('state', 1) // 只搜索启用的产品
                ->where('deleted_at', null); // 排除已删除的产品

            // 精确搜索条件：代码或名称完全匹配
            $query->where(function ($q) use ($keyword) {
                $q->where('symbol', $keyword)
                  ->orWhere('name', $keyword);
            });

            // 产品类型筛选
            if (!empty($pro_type) && is_numeric($pro_type)) {
                $query->where('pro_type', $pro_type);
            }

            // 排序：权重优先，然后按热门程度，最后按ID
            $query->orderBy('weight', 'desc')
                  ->orderBy('is_hot', 'desc')
                  ->orderBy('id', 'desc');

            // 不分页，直接获取结果，限制数量
            $products = $query->limit($limit)->get([
                'id',
                'pro_type',
                'symbol',
                'name',
                'currency',
                'exchange',
                'country',
                'other_id',
                'logo'
            ]);

            if ($products->isEmpty()) {
                return $this->returnMsg(200, 'success', []);
            }

            // 调用三方数据源获取实时数据
            $this->updateRealTimeData($products);

            // 重新查询获取更新后的数据
            $productIds = $products->pluck('id')->toArray();
            $updatedProducts = Product::whereIn('id', $productIds)
                ->orderBy('weight', 'desc')
                ->orderBy('is_hot', 'desc')
                ->orderBy('id', 'desc')
                ->get([
                    'id',
                    'pro_type',
                    'symbol',
                    'name',
                    'currency',
                    'exchange',
                    'country',
                    'price',
                    'open',
                    'high',
                    'low',
                    'close',
                    'volume',
                    'previous_close',
                    'change',
                    'percent_change',
                    'is_market_open',
                    'is_hot',
                    'logo'
                ]);

            // 格式化返回数据
            $formattedData = $updatedProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'pro_type' => $product->pro_type,
                    'pro_type_text' => $this->getProTypeText($product->pro_type),
                    'symbol' => $product->symbol,
                    'name' => $product->name,
                    'currency' => $product->currency,
                    'exchange' => $product->exchange,
                    'country' => $product->country,
                    'country_text' => $this->getCountryText($product->country),
                    'price' => (float)$product->price,
                    'open' => (float)$product->open,
                    'high' => (float)$product->high,
                    'low' => (float)$product->low,
                    'close' => (float)$product->close,
                    'volume' => (float)$product->volume,
                    'previous_close' => (float)$product->previous_close,
                    'change' => (float)$product->change,
                    'percent_change' => (float)$product->percent_change,
                    'is_market_open' => (bool)$product->is_market_open,
                    'is_hot' => (bool)$product->is_hot,
                    'logo' => $product->logo,
                    'display_text' => "{$product->symbol} - {$product->name}",
                ];
            });

            return $this->returnMsg(200, 'success', $formattedData->toArray());

        } catch (\Exception $e) {
            return $this->returnMsg(500, __('api.搜索失败:') . $e->getMessage());
        }
    }

    /**
     * 搜索建议（热门产品推荐）
     */
    public function suggestions(Request $request)
    {
        $pro_type = $request->input('pro_type');
        $limit = $request->input('limit', 10);

        try {
            $query = Product::query()
                ->where('state', 1)
                ->where('deleted_at', null)
                ->where('is_hot', 1); // 只返回热门产品

            // 产品类型筛选
            if (!empty($pro_type) && is_numeric($pro_type)) {
                $query->where('pro_type', $pro_type);
            }

            $products = $query->orderBy('weight', 'desc')
                             ->orderBy('id', 'desc')
                             ->limit($limit)
                             ->get([
                                 'id',
                                 'pro_type',
                                 'symbol',
                                 'name',
                                 'currency',
                                 'price',
                                 'change',
                                 'percent_change',
                                 'logo'
                             ]);

            $formattedData = $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'pro_type' => $product->pro_type,
                    'pro_type_text' => $this->getProTypeText($product->pro_type),
                    'symbol' => $product->symbol,
                    'name' => $product->name,
                    'currency' => $product->currency,
                    'price' => (float)$product->price,
                    'change' => (float)$product->change,
                    'percent_change' => (float)$product->percent_change,
                    'logo' => $product->logo,
                    'display_text' => "{$product->symbol} - {$product->name}",
                ];
            });

            return $this->returnMsg(200, 'success', $formattedData);

        } catch (\Exception $e) {
            return $this->returnMsg(500, __('api.获取推荐失败:') . $e->getMessage());
        }
    }

    /**
     * 热门产品
     */
    public function hotProduct(Request $request)
    {
        $type = $request->input('type'); // 产品类型  股票、大宗商品、外汇等。

        // 重新查询数据库，获取更新后的分页数据
        $query = Product::query()->select('id', 'pro_type', 'name', 'symbol', 'exchange', 'country', 'price', 'open', 'high', 'low', 'close', 'volume', 'previous_close', 'change', 'percent_change', 'average_volume');
        if ($type) {
            $query->where('pro_type', $type);
        }

        $result = $query->where('state', 1)
            ->where('is_hot', 1)
            ->orderBy('weight', 'desc')
            ->paginate(10);

        return $this->returnMsg(200, 'success', $result);
    }

    public function listStockProduct(Request $request)
    {
        $country = $request->input('country', 'de'); // 股票產品

        $result = Product::query()->where('state', 1)
            ->where('pro_type', 1)
            ->where('country', $country)
            ->select('id', 'pro_type', 'other_id')
            ->orderBy('weight', 'desc')
            ->paginate(10);

        $jiangshan = new JiangshanService();
        $updateResult = $jiangshan->get_new_data_serices_list($result->getCollection(), $country);

        // 替换分页对象里的集合
        $result->setCollection($updateResult);

        $result->makeHidden(['created_at', 'updated_at', 'deleted_at', 'state']);


        return $this->returnMsg(200, 'success', $result);


    }

    /**
     * 产品列表
     */
    public function listProduct(Request $request)
    {
        $type = $request->input('type'); // 产品类型 、大宗商品、外汇等。

        if (!in_array($type, [2, 3, 4, 5])) {
            $type = 0;
        }

        $query = Product::query();
        if ($type) {
            $query->where('pro_type', $type);
        }

        $result = $query->where('state', 1)
            ->orderBy('weight', 'desc')
            ->paginate(10);

        // 更新三方数据
        TwelvedataNewService::quoteByBatch($result);

        // 重新查询数据库，获取更新后的分页数据
        $query = Product::query()
            ->select('id', 'pro_type', 'name', 'symbol', 'exchange', 'country', 'price', 'open', 'high', 'low', 'close', 'volume', 'previous_close', 'change', 'percent_change', 'average_volume');
        if ($type) {
            $query->where('pro_type', $type);
        }

        $result = $query->where('state', 1)
            ->orderBy('weight', 'desc')
            ->paginate(10);

        return $this->returnMsg(200, 'success', $result);
    }

    /**
     * 产品详情
     */
    public function detailProduct(Request $request)
    {
        $id = $request->get('id'); // 产品类型  股票、大宗商品、外汇等。

        $product = Product::query()->find($id);
        if (!$product) {
            return $this->returnMsg(500, __('api.产品不存在'));
        }
        // 更新三方数据
        if ($product->pro_type == 1) {
            $jiangshan = new JiangshanService();
            $result = $jiangshan->get_new_data_serices($product);
            $result->makeHidden(['created_at', 'updated_at', 'deleted_at', 'other_id', 'state', 'weight', 'is_hot', 'fifty_two_week']);
        } else {
            TwelvedataNewService::quoteByOne($product);
            // 重新查询数据库，获取更新后的分页数据
            $result = Product::query()
                ->select('id', 'pro_type', 'name', 'symbol', 'exchange', 'country', 'price', 'open', 'high', 'low', 'close', 'volume', 'previous_close', 'change', 'percent_change', 'average_volume')
                ->find($id);
        }


        return $this->returnMsg(200, 'success', $result);

    }


    /**
     * 产品K线
     */

    public function klineProduct(Request $request)
    {
        $id = $request->get('id', 0); // 产品类型  股票、大宗商品、外汇等。

        $lineType = $request->get('line_type'); // 类型


        // 匠山 1=1分钟 ，2=5分钟，3= 15分钟 ，4=30分钟 ，5=45分钟 ，6=天， 7=周 ，8=月 以匠山的爲準

        if (!in_array($lineType, [1, 2, 3, 4, 5, 6, 7, 8])) {
            $lineType = 8;
        }

        $product = Product::query()->find($id);
        if (!$product) {
            return $this->returnMsg(500, __('api.产品不存在'));
        }

        if ($product->pro_type != 1) {
            // 12數據 1: 1分钟，2：5分钟，3：15分钟，4：30分钟，5：45分钟，6：1h,7:2h,8:4h,9:1day,10:1week, 11:month

            // 如果不是股票，需要把k綫類型轉換成12數據的
            $lineTypeArr = [
                1 => 1,
                2 => 2,
                3 => 2,
                4 => 2,
                5 => 2,
                6 => 9,
                7 => 10,
                8 => 11,
            ];
            $lineType12 = $lineTypeArr[$lineType];
            $res = TwelvedataNewService::kline($product, $lineType12);
        } else {
            $jiangshan = new JiangshanService();
            $res = $jiangshan->getKline($product, $lineType);
        }


        if (!$res) {
            return $this->returnMsg(500, 'No Data');
        }

        return $this->returnMsg(200, 'ok', $res);
    }

    /**
     * 涨跌幅排行
     */
    public function rankProduct(Request $request)
    {
        $type = $request->input('type', 'stocks');
        $direction = $request->input('direction', 'gainers');
        $country = $request->input('country', 'USA');

        // 参数校验
        $typeList = ['stocks', 'etf', 'mutual_funds', 'forex', 'crypto'];
        $directionList = ['gainers', 'losers'];
        $countryList = ['USA', 'UK', 'JP', 'DE', 'FR', 'IN', 'BR', 'AU'];

        $type = in_array($type, $typeList) ? $type : 'stocks';
        $direction = in_array($direction, $directionList) ? $direction : 'gainers';
        $country = 'USA';

        $cacheKey = "market_movers:{$type}:{$direction}:{$country}";
        $cachedSymbols = cache()->get($cacheKey);

        // 1. 先获取榜单（缓存中无则从API获取再缓存）
        if (!$cachedSymbols) {
            $apiResult = TwelvedataNewService::get_market_movers($type, $direction, $country);

            if (empty($apiResult) || !is_array($apiResult)) {
                return $this->returnMsg(500, __('api.获取榜单失败'));
            }
            // 只缓存需要的symbol/exchange信息即可
            $cachedSymbols = [];
            foreach ($apiResult as $item) {
                $cachedSymbols[] = [
                    'symbol' => $item['symbol'],
                    'exchange' => $item['exchange'] ?? '',
                ];
            }
            cache()->put($cacheKey, $cachedSymbols, 10 * 60);
        }

        // 2. 查询本地产品表（实时）
        $symbols = array_column($cachedSymbols, 'symbol');
        $exchanges = array_column($cachedSymbols, 'exchange');
        $productsQuery = Product::query()->whereIn('symbol', $symbols);
//        if (in_array($type, ['stocks', 'etf'])) {
//            $productsQuery->whereIn('exchange', $exchanges);
//        }
        $products = $productsQuery->get();

        // 3. 批量更新行情
        // 如果你的 quoteByBatch 方法直接更新产品实例，可以直接用 products。否则传 symbol/exchange
        TwelvedataNewService::quoteByBatch($products);

        // 更新完行情以后，在重新查询一次产品数据
        $product_res = $productsQuery->get();

        // 4. 组装返回结果（保证顺序与榜单一致，可按 symbol+exchange 唯一键查找）
        $productsMap = [];
        foreach ($product_res as $p) {
            $key = $p->symbol;
            $productsMap[$key] = $p;
        }

        $result = [];
        foreach ($cachedSymbols as $item) {
            $key = $item['symbol'] ;
            $product = $productsMap[$key] ?? null;
            $result[] = [
                'symbol' => $item['symbol'],
                'exchange' => $item['exchange'],
                'product' => $product, // 可自行决定返回的字段范围
            ];
        }

        return $this->returnMsg(200, 'success', $result);
    }

    /**
     * 市场分析推荐产品
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function recommendationsProduct(Request $request)
    {
        $exchange = $request->input('exchange', 'NASDAQ');
        $cacheKey = "recommendations:{$exchange}";

        $marketOverview = cache()->remember($cacheKey, 60 * 60, function () use ($exchange) {
            $symbols = Product::query()
                ->where('pro_type', 1)
                ->where('country', 'de')
                ->inRandomOrder()
                ->limit(8)
                ->pluck('symbol')
                ->toArray();

            if (empty($symbols)) {
                return []; // 返回空，避免后续报错
            }

            return collect($symbols)->map(function ($sym) use ($exchange) {
                $data = TwelvedataNewService::recommendations($sym, $exchange);
                return $data ? [
                    'symbol' => $sym,
                    'overview' => $data,
                ] : null;
            })->filter()->values()->toArray();
        });

        if (empty($marketOverview)) {
            return $this->returnMsg(500, [], __('api.没有推荐产品'));
        }

        return $this->returnMsg(200, $marketOverview, __('api.请求成功'));
    }

    /**
     * 产品详情 - 公司信息
     * @param Request $request
     * @param string $symbol 产品代码
     * @param string $exchange 交易所
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function profileProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_profile:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getProfile($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 产品统计数据
     * @param Request $request
     * @param string $symbol 产品代码
     * @param string $exchange 交易所
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function statisticsProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_statistics:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getStatistics($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 产品收益统计
     * @param Request $request
     * @param string $symbol 产品代码
     * @param string $exchange 交易所
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function earningsProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_earnings:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getEarnings($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 盈利预测
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function estimateProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_estimate:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getEstimate($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 收入估算
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function revenueProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_revenue:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getRevenue($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 每股收益趋势
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function epsTrendProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_epsTrend:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getEpsTrend($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 每股收益修订
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function epsRevisionsProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_epsRevisions:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getEpsRevisions($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 增长预测
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function growthEstimatesProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_growth_estimates:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getGrowthEstimates($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 分析师评级
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function analystRatingsLightProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_analyst_ratings_light:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getAnalystRatingsLight($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 历史价格
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function historicalPriceProduct(Request $request)
    {
        $today = date('Y-m-d');

        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        $lineType = $request->input('line_type'); // 类型
        $start_date = $request->input('start_date', date('Y-m-d', strtotime('-30 days', strtotime($today))));
        $end_date = $request->input('end_date', $today);

        // 1: 1分钟，2：5分钟，3：15分钟，4：30分钟，5：45分钟，6：1h,7:2h,8:4h,9:1day,10:1week, 11:month
        if (!in_array($lineType, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11])) {
            $lineType = 9;
        }

        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_historical_price:{$symbol}:{$exchange}:{$lineType}:{$start_date}:{$end_date}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getHistoricalPrice($symbol, $product->exchange, $lineType, $start_date, $end_date);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 损益表（年度、季度）
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function incomeStatementProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        $period = $request->input('period');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange) || empty($period)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        if (!in_array($period, ['annual', 'quarterly'])) {
            $period = 'annual'; // 默认年度
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_income_statement:{$symbol}:{$exchange}:{$period}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getIncomeStatement($symbol, $product->exchange, $period);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 资产负债表（年度、季度）
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function balanceSheetProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        $period = $request->input('period');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange) || empty($period)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        if (!in_array($period, ['annual', 'quarterly'])) {
            $period = 'annual'; // 默认年度
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_balance_sheet:{$symbol}:{$exchange}:{$period}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getBalanceSheet($symbol, $product->exchange, $period);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 现金流（年度、季度）
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function cashFlowProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        $period = $request->input('period');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange) || empty($period)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        if (!in_array($period, ['annual', 'quarterly'])) {
            $period = 'annual'; // 默认年度
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_cash_flow:{$symbol}:{$exchange}:{$period}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getCashFlow($symbol, $product->exchange, $period);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 持有人
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function holdersProduct(Request $request)
    {
        $symbol = $request->input('symbol');
        $exchange = $request->input('exchange');
        // 如果有传exchange，建议使用
        if (empty($symbol) || empty($exchange)) {
            return $this->returnMsg(400, [], __('api.参数错误'));
        }

        // 查找产品
        $product = Product::query()->where('symbol', $symbol)->first();
        if (!$product) {
            return $this->returnMsg(404, [], __('api.产品信息未找到'));
        }

        // 推荐：加上exchange，防止同名symbol不同交易所冲突
        $cacheKey = "product_holders:{$symbol}:{$exchange}";
        $profile = cache()->get($cacheKey);
        if ($profile) {
            return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
        }

        // 调接口
        $profile = TwelvedataNewService::getHolders($symbol, $product->exchange);
        if (empty($profile)) {
            return $this->returnMsg(500, [], __('api.产品信息获取失败'));
        }

        // 缓存半个月（建议用 Carbon 或 now()->addDays(15)）
        cache()->put($cacheKey, $profile, 60 * 60 * 24 * 15);

        return $this->returnMsg(200, $profile, __('api.产品信息获取成功'));
    }

    /**
     * 获取产品类型文本
     */
    private function getProTypeText($proType)
    {
        $types = [
            1 => '股票',
            2 => '大宗商品',
            3 => 'ETF',
            4 => '外汇',
            5 => '数字货币'
        ];

        return $types[$proType] ?? '未知';
    }

    /**
     * 获取国家文本
     */
    private function getCountryText($country)
    {
        $countries = [
            'United States' => '美国',
            'Germany' => '德国',
            'South Korea' => '韩国',
            'France' => '法国',
            'Netherlands' => '荷兰',
            'United Kingdom' => '英国',
            'Norway' => '挪威',
            'Japan' => '日本',
            'Spain' => '西班牙',
        ];

        return $countries[$country] ?? $country;
    }

    /**
     * 更新产品实时数据
     */
    private function updateRealTimeData($products)
    {
        foreach ($products as $product) {
            try {
                if ($product->pro_type == 1) {
                    // 股票使用江山服务
                    $jiangshan = new \App\Services\JiangshanService();
                    $jiangshan->get_new_data_serices($product);
                } else {
                    // 其他产品使用TwelveData
                    \App\Services\TwelvedataNewService::quoteByOne($product);
                }

                // 刷新模型数据
                $product->refresh();

            } catch (\Exception $e) {
                // 记录错误但不中断搜索
                \Log::warning('获取产品实时数据失败', [
                    'product_id' => $product->id,
                    'symbol' => $product->symbol,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
