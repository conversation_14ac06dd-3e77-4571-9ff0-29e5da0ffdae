<?php

namespace App\Services;

use App\Models\ExchangeRate;
use App\Models\SystemConfig;
use App\Models\UserOrder;
use App\Models\Product;
use App\Models\Users;
use App\Models\UsersInfo;
use App\Models\MoneyChange;
use Illuminate\Support\Facades\DB;

class OrderService
{
    /**
     * 下单
     * $params = ['product_id', 'direction_data' , 'buy_type', 'buy_price', 'buy_quantity', 'lever', 'take_profit', 'stop_loss'];
     */
    public static function buy($user, $params)
    {
        // 参数验证

        if (empty($params['product_id']) || !is_numeric($params['product_id'])) {
            throw new \Exception(__('api.产品ID不能为空'));
        }
        $product = Product::where('id', $params['product_id'])
            ->where('deleted_at', null)
            ->where('state', 1)
            ->first();
        if (!$product) {
            throw new \Exception(__('api.产品不存在或状态不合法'));
        }
        if (!in_array($params['direction_data'] ?? null, [1, 2])) {
            throw new \Exception(__('api.买入方向不合法'));
        }
        if (!in_array($params['buy_type'] ?? null, [1, 2])) {
            throw new \Exception(__('api.买入类型不合法'));
        }
        if (!isset($params['buy_price']) || !is_numeric($params['buy_price']) || $params['buy_price'] <= 0) {
            throw new \Exception(__('api.买入价格不能小于0'));
        }
        if (!isset($params['buy_quantity']) || !is_numeric($params['buy_quantity']) || $params['buy_quantity'] <= 0) {
            throw new \Exception(__('api.买入数量不能小于0'));
        }
        if (!isset($params['lever']) || !is_numeric($params['lever']) || $params['lever'] < 1 || $params['lever'] > 100) {
            throw new \Exception(__('api.买入杠杆必须在1-100之间'));
        }
        if (isset($params['take_profit']) && (!is_numeric($params['take_profit']) || $params['take_profit'] < 0)) {
            throw new \Exception(__('api.止盈金额必须大于等于0'));
        }
        if (isset($params['stop_loss']) && (!is_numeric($params['stop_loss']) || $params['stop_loss'] < 0)) {
            throw new \Exception(__('api.止损金额必须大于等于0'));
        }

        // 拉取最新行情
        $product = Product::find($params['product_id']);

        // 先验证是否开盘
        if (!is_market_on($product)) {
            throw new \Exception(__('api.请在开盘时间进行交易'));
        }

        if ($product->pro_type == 1) {
            $Jiangshan = new JiangshanService();
            $jiangshan = $Jiangshan->get_new_data_serices($product);
            $product->price = $jiangshan['price'] ?? $product->price;
        } else {
            $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
            $product->price = $quote['price'] ?? $product->price;
        }

        $buy_type = $params['buy_type'];
        $buy_price = $buy_type == 1 ? $product->price : $params['buy_price'];
//        $is_market_open = $product->is_market_open == 1;
//        $status = $is_market_open ? 1 : 4;
//        $is_open_buy = $is_market_open ? 0 : 1;

//        if ($buy_type == 2 && $is_market_open) {
//            if (($params['direction_data'] == 1 && $buy_price > $product->price) || ($params['direction_data'] == 2 && $buy_price < $product->price)) {
//                $status = 1;
//                $is_open_buy = 0;
//            }
//        }
        if ($buy_type == 2) {
            $status = 4; // 挂单
        } else {
            $status = 1;
        }
        // todo: 计算买入成本
        $buy_cost_currency = $buy_cost = round($buy_price * $params['buy_quantity'] / $params['lever'], 2);

        if ($buy_cost <= 0) throw new \Exception(__('api.买入成本必须大于0'));

        // 获取对应的费率，没有配置就获取全局的
        $rate = get_buy_fee_rate($user->id);

        // todo: 计算手续费
        $buy_fees_currency = $buy_fees = buy_commission($buy_cost, $rate);

        // todo: 西班牙市值超过10亿欧元的股票需要加多一个印花税
        // 印花税：0.2%
        $stamp_duty_currency = $stamp_duty = 0;
        if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
            $stamp_duty_currency = $stamp_duty = round($buy_cost * 0.002, 2); // 印花税
        }

        // 如果当前产品不是欧元结算，则需要去计算欧元成本
        $exchange_rate = 1;
        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
            if (in_array($product->pro_type, [2, 3, 4, 5])) {
                // todo: 调用接口获取一次实时的接口

                // 获取当前产品的货币与欧元的汇率
                $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                    ->where('to_currency', 'EUR')
                    ->firstOrFail();
            }else{
                // 获取当前产品的货币与欧元的汇率
                $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                    ->where('to_currency', 'EUR')
                    ->firstOrFail();
            }

            // 要买入的金额
            // 实时 EUR/USD 汇率,
            // 价差值
            $buy_cost = calculateEuroCost($buy_cost, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $buy_fees = calculateEuroCost($buy_fees, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $stamp_duty = calculateEuroCost($stamp_duty, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $exchange_rate = $exchangeRate->rate;
        }

        // 生成订单号
        $out_trade_no = generateUniqueOrderNumber();

        DB::beginTransaction();
        try {
            $user_info = UsersInfo::where('user_id', $user->id)->lockForUpdate()->first();
            if (!$user_info) throw new \Exception(__('api.用户资金信息不存在'));

            /*if ($product->currency) {
                $currency = strtolower($product->currency);
                $balance = $currency . '_balance';
                $dongjie = $currency . '_dongjie';
            } else {
                if ($product->pro_type == 4) {
                    // 外汇需要额外处理
                    $arr = explode('/', $product->pro_type);
                    $balance = strtolower($arr[1]) . '_balance';
                    $dongjie = strtolower($arr[1]) . '_dongjie';
                    $currency = strtoupper($arr[1]);
                } else {
                    $balance = 'usd_balance';
                    $dongjie = 'usd_dongjie';
                    $currency = 'USD';
                }
            }*/

            /*$wallet = getAccountByPro($product);
            $balanceField = $wallet['balance_field'];
            $dongjieField = $wallet['dongjie_field'];
            $currency = $wallet['currency'];
            $before = $user_info->$balanceField;

            $before = $user_info->$balance;
            $freeze_total = $buy_cost + $buy_fees;
            if ($before < $freeze_total) throw new \Exception('用户资金不足');

            $user_info->$balanceField = $before - $freeze_total;
            $user_info->$dongjieField += $buy_cost;
            $user_info->save();*/

            // todo: 默认扣欧元，所以不需要再请求这个
            $before = $user_info->eur_balance;

            // 买入本金 = 买入成本 + 手续费 + 印花税
            $freeze_total = $buy_cost + $buy_fees + $stamp_duty;
            if ($before < $freeze_total) throw new \Exception(__('api.用户资金不足'));

            $user_info->eur_balance = $before - $freeze_total;
            $user_info->eur_dongjie += $buy_cost;
            $user_info->save();

            $order = UserOrder::create([
                'user_id' => $user->id,
                'order_sn' => $out_trade_no,
                'product_id' => $params['product_id'],
                'product_type' => $product->pro_type,
                'direction_data' => $params['direction_data'],
                'buy_type' => $buy_type,
                'buy_price' => $buy_price,
                'buy_quantity' => $params['buy_quantity'],
                'lever' => $params['lever'],
                'is_open_buy' => 0, // 没什么卵用了
                'status' => $status,
                'buy_cost' => $buy_cost_currency,
                'take_profit' => $params['take_profit'] ?? 0,
                'stop_loss' => $params['stop_loss'] ?? 0,
                'buy_time' => time(),
                'buy_fees' => $buy_fees_currency,
                'stamp_duty' => $stamp_duty_currency,
                'exchange_rate' => $exchange_rate,
                'user_spread' => $user->spread > 0 ? $user->spread : 0.00036,
                'buy_fees_rate' => $rate,
                'market_cap' => $product->price * $params['buy_quantity'],
                'currency' => $product->currency ?? 'EUR',
            ]);
            $protype = [
                1 => '股票',
                2 => '大宗商品',
                3 => 'ETF',
                4 => '外汇',
                5 => '数字货币',
            ];
            self::recordMoneyChange([
                'money_type' => 'EUR',
                'user_id' => $user->id,
                'order_sn' => $out_trade_no,
                'product_name' => $product->name,
                'detailed' => "购买|{$protype[$product->pro_type]}|,|产品名称|:|{$product->name}|,|代码|:|{$product->symbol}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => -$buy_cost,
                'after_money' => $before - $buy_cost,
            ]);
            if ($buy_fees > 0) {
                self::recordMoneyChange([
                    'money_type' => 'EUR',
                    'user_id' => $user->id,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "购买|{$protype[$product->pro_type]}|手续费|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'type' => MoneyChange::TYPE_TRADE,
                    'before_money' => $before - $buy_cost,
                    'money' => -$buy_fees,
                    'after_money' => $before - $buy_cost - $buy_fees,
                ]);
            }
            if ($stamp_duty > 0) {
                self::recordMoneyChange([
                    'money_type' => 'EUR',
                    'user_id' => $user->id,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "购买|{$protype[$product->pro_type]}|印花税|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'type' => MoneyChange::TYPE_TRADE,
                    'before_money' => $before - $buy_cost - $buy_fees,
                    'money' => -$stamp_duty,
                    'after_money' => $before - $buy_cost - $buy_fees - $stamp_duty,
                ]);
            }

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 平仓（主动或自动）
     * $closeType: 1=主动, 2:强制平仓， 3=止盈, 4=止损, 5=开盘自动
     */
    public static function sell($user_id, $orderId, $closeType = 1)
    {
        DB::beginTransaction();
        try {
            $order = UserOrder::where('id', $orderId)
                ->where('user_id', $user_id)
                ->lockForUpdate()
                ->first();

            if (!$order) throw new \Exception(__('api.订单不存在'));
            if ($order->status != 1) throw new \Exception(__('api.订单状态异常，无法平仓'));
            if ($order->is_lock == 1) throw new \Exception(__('api.订单已锁定，请联系客服'));
            if ($order->trade_id > 0) throw new \Exception(__('api.当前订单属于交易员订单'));

            $product = Product::find($order->product_id);

            // 获取产品的实时价格
            if ($product->pro_type == 1) {
                $Jiangshan = new JiangshanService();
                $jiangshan = $Jiangshan->get_new_data_serices($product);
                $curPrice = $jiangshan['price'] ?? $product->price;
            } else {
                $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
                $curPrice = $quote['price'] ?? $product->price;
            }

            // 盈亏金额
            $profitLoss = $order->direction_data == 1
                ? round(($curPrice - $order->buy_price) * $order->buy_quantity, 2)
                : round(($order->buy_price - $curPrice) * $order->buy_quantity, 2);

            // todo: 计算卖出金额（本金+盈亏金额）
            $sellMoney_currency = $sellMoney = $order->buy_cost + $profitLoss;

            if ($sellMoney <= 0) $sellMoney = 0.01;

            $rate = get_buy_fee_rate($user_id);

            // todo: 计算卖出手续费，不再需要证交税，西班牙的印花税只有买入时候收取
            $sxf_currency = $sxf = sell_commission($sellMoney, $rate);

            // if ($sellMoney <= 0) $sellMoney = 0.01;
            // $sell_fees = sell_commission($sellMoney, $user_id);
            // $sxf = $sell_fees['sxf'];
            // $see_fee = $sell_fees['see_fees'];

            // 如果当前产品不是欧元结算，则需要去计算欧元成本
            $eur_dongjie = $order->buy_cost;
            $sell_exchange_rate = 1;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                if (in_array($product->pro_type, [2, 3, 4, 5])) {
                    // todo: 调用接口获取一次实时的接口

                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }else{
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }
                $users = Users::where('id', $user_id)->first();

                // 要卖出的金额
                // 实时 EUR/USD 汇率,
                // 价差值
                $sellMoney = calculateEuroCost($sellMoney, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);
                $sxf = calculateEuroCost($sxf, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);

                $sell_exchange_rate = $exchangeRate->rate * (1 - $users->spread);

                // 计算卖出时候用户冻结余额应该减去多少，需要按照买入时候的汇率来计算
                $eur_dongjie = calculateEuroCost($order->buy_cost, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
            }

            $user_info = UsersInfo::where('user_id', $user_id)->lockForUpdate()->first();

//            if ($product->currency) {
//                $currency = strtolower($product->currency);
//                $balance = $currency . '_balance';
//                $dongjie = $currency . '_dongjie';
//            } else {
//                if ($product->pro_type == 4) {
//                    // 外汇需要额外处理
//                    $arr = explode('/', $product->pro_type);
//                    $balance = strtolower($arr[1]) . '_balance';
//                    $dongjie = strtolower($arr[1]) . '_dongjie';
//                    $currency = strtoupper($arr[1]);
//                } else {
//                    $balance = 'usd_balance';
//                    $dongjie = 'usd_dongjie';
//                    $currency = 'USD';
//                }
//            }

            /*$wallet = getAccountByPro($product);
            $balanceField = $wallet['balance_field'];
            $dongjieField = $wallet['dongjie_field'];
            $currency = $wallet['currency'];
            $before = $user_info->$balanceField;

            $user_info->$balanceField += $pingcangjine;
            $user_info->$dongjieField -= $order->buy_cost;
            $user_info->save();*/

            $before = $user_info->eur_balance;

            $user_info->eur_balance += $sellMoney - $sxf;
            $user_info->eur_dongjie -= $eur_dongjie;
            $user_info->save();

            $order->status = 2;
            $order->sell_price = $curPrice;
            $order->sell_money = $sellMoney_currency; // 卖出金额(本金+盈亏金额)
            $order->profit_amount = $profitLoss;
            $order->sell_fees = $sxf_currency;
            $order->sell_quantity = $order->buy_quantity; // 卖出数量
            $order->see_fee = 0; // 证交税不再需要
            $order->sell_exchange_rate = $sell_exchange_rate; // 卖出时候的汇率
            $order->sell_time = time();
            $order->sell_type = $closeType;
            $order->is_open_sell = 0;
            $order->market_cap = $curPrice * $order->buy_quantity;
            $order->save();

            $protype = [
                1 => '股票',
                2 => '大宗商品',
                3 => 'ETF',
                4 => '外汇',
                5 => '数字货币',
            ];
            self::recordMoneyChange([
                'money_type' => 'EUR',
                'user_id' => $user_id,
                'order_sn' => $order->order_sn,
                'product_name' => $product->name,
                'detailed' => "{$protype[$product->pro_type]}|平仓|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => $sellMoney,
                'after_money' => $before + $sellMoney,
            ]);

            if ($sxf) {
                self::recordMoneyChange([
                    'money_type' => 'EUR',
                    'user_id' => $user_id,
                    'order_sn' => $order->order_sn,
                    'product_name' => $product->name,
                    'detailed' => "{$protype[$product->pro_type]}|平仓|手续费|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'type' => MoneyChange::TYPE_TRADE,
                    'before_money' => $before + $sellMoney,
                    'money' => -$sxf,
                    'after_money' => $before + $sellMoney - $sxf,
                ]);
            }

            /*if ($see_fee) {
                self::recordMoneyChange([
                    'money_type' => 'EUR',
                    'user_id' => $user_id,
                    'order_sn' => $order->order_sn,
                    'product_name' => $product->name,
                    'detailed' => "{$protype[$product->pro_type]}|平仓|证交费|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                    'type' => MoneyChange::TYPE_TRADE,
                    'before_money' => $before + $sellMoney - $sxf,
                    'money' => -$see_fee,
                    'after_money' => $before + $sellMoney - $sxf - $see_fee,
                ]);
            }*/

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 撤销订单（仅挂单状态可撤销）
     */
    public static function cancel($user_id, $orderId)
    {
        DB::beginTransaction();
        try {
            $order = UserOrder::where('id', $orderId)
                ->where('user_id', $user_id)
                ->lockForUpdate()
                ->first();

            if (!$order) throw new \Exception(__('api.订单不存在'));
            if ($order->status != 4) throw new \Exception(__('api.只有挂单状态可撤销'));
            if ($order->is_lock == 1) throw new \Exception(__('api.订单已锁定，请联系客服'));

            $product = Product::find($order->product_id);
            $user_info = UsersInfo::where('user_id', $user_id)->lockForUpdate()->first();
            $unfreeze = $order->buy_cost + $order->buy_fees + $order->stamp_duty;

            /*$wallet = getAccountByPro($product);
            $balanceField = $wallet['balance_field'];
            $dongjieField = $wallet['dongjie_field'];
            $currency = $wallet['currency'];*/
            $buy_cost = $order->buy_cost;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                // 要买入的金额
                // 实时 EUR/USD 汇率,
                // 价差值
                $buy_cost = calculateEuroCost($order->buy_cost, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
                $buy_fees = calculateEuroCost($order->buy_fees, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
                $stamp_duty = calculateEuroCost($order->stamp_duty, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);

                $unfreeze = $buy_cost + $buy_fees + $stamp_duty;
            }

            $before = $user_info->eur_balance;
            // 解冻挂单资金
            $user_info->eur_balance += $unfreeze;
            $user_info->eur_dongjie -= $buy_cost;
            $user_info->save();

            // 直接删除订单
//            $order->delete(); // 如果需要记录撤单信息，可以不删除

            $order->status = 5; // 已撤单
            $order->save();

            MoneyChange::create([
                'money_type' => 'EUR',
                'user_id' => $order->user_id,
                'order_sn' => $order->order_sn,
                'product_name' => $product->name,
                'detailed' => "撤销订单|代码|{$product->symbol}|产品|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => $unfreeze,
                'after_money' => $before + $unfreeze,
                'is_show' => 0
            ]);

            // 删除对应的资金记录
            MoneyChange::query()->where('order_sn', $order->order_sn)->update(['is_show' => 0]);

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 资金流水
     */
    public static function recordMoneyChange($params)
    {
        return MoneyChange::create($params);
    }


    /**
     * 根据数据源 更新股票订单
     * @param string $json
     */
    public static function updateStockOrderByTop($json)
    {
        $data = json_decode($json, true);
        if (!$data) {
            echo "无效数据";
            return false;
        }
        $nowPrice = $data['Last'] ?? 0; // 当前价格
        $tmpVal = [
            'change' => $data['Chg'] ?? 0.00, // 涨跌数
            'percent_change' => $data['ChgPct'] ?? 0.00, // 涨跌百分比
            'price' => $nowPrice, // 当前价格
            'open' => $data['Open'] ?? 0, // 开盘价
            'close' => $data['PrevClose'] ?? 0, // 前收盘价
            'high' => $data['High'] ?? 0, // 最高价
            'low' => $data['Low'] ?? 0, // 最低价
            'volume' => $data['Volume'] ?? 0, // 成交量
        ];
        // 获取产品
        $product = Product::query()->where('other_id', $data['Id'])->first();
        if (!$product) {
            echo '数据库中未找到股票：' . json_encode($data) . "\r\n";
            return false;
        }


        // 更新股票数据
        $rs = Product::query()->where('id', $product->id)->update($tmpVal);
        if (!$rs) {
            echo '数据库更新失败：' . $data['Symbol'] . "\r\n";
        } else {
            echo '数据库更新成功：' . $data['Symbol'] . "\r\n";
        }

        if ($nowPrice <= 0) {
            echo '价格异常：' . $data['Symbol'] . "\r\n";
            return false;
        }

        // 获取持仓和挂单订单
        $orders = UserOrder::query()->where('product_id', $product->id)->whereIn('status', [1, 4])->get();
        if ($orders->isEmpty()) {
            echo '没有持仓订单：' . json_encode($data) . "\r\n";
            return false;
        }

        //=========================更新挂单 持仓===========
        echo "更新满足条件的买涨买跌的挂单 为持仓{$product->symbol}|{$product->id}\r\n";
        // 好像我设置的价格，只要低于当前加，都可以转持仓，不论买涨买跌
        // 方向1 涨 2跌
        UserOrder::query()
            ->where('product_id', $product->id)
//            ->where('buy_type', 2)
            ->where('direction_data', 1)
            ->where('buy_price', '>=', $nowPrice)
            ->where('status', 4)
            ->update(['status' => 1]);

        UserOrder::query()
            ->where('product_id', $product->id)
//            ->where('buy_type', 2)
            ->where('direction_data', 2)
            ->where('buy_price', '<=', $nowPrice)
            ->where('status', 4)
            ->update(['status' => 1]);
        echo "更新市值盈亏\r\n";
        //=========================更新挂单 持仓end===========


        foreach ($orders as $item) {
            DB::beginTransaction();
            try {
                $order = UserOrder::find($item->id);
                //=========================更新市值，赢亏===========
                // 根据当前价格- 更新满足条件的买涨买跌的挂单 为持仓
                //  `buy_type` '买入类型:1=市价,2=限价',
                // 买入方向:2=买跌,1=买涨
                $shizhi = round($nowPrice * $order->buy_quantity, 3);
                if ($order['direction_data'] == '1') {    //如果是多单 长
                    $multiplier = 1;
                    $yingkui = round(($nowPrice - $order->buy_price) * $order->buy_quantity * $multiplier, 3);
                } else {    //如果是空单 跌
                    $multiplier = 1;
                    $yingkui = round(($order->buy_price - $nowPrice) * $order->buy_quantity * $multiplier, 3);
                }
                //=========================更新市值，赢亏===========

                // 持仓情况
                if ($order->status == 1) {
                    // 止盈止损的处理
                    if ($order->take_profit > 0 || $order->stop_loss > 0) {
                        echo "开始止盈止损的处理\r\n";
                        // 是否锁仓
                        if ($order->lock == 1) {
                            echo '止盈止损处理，已锁仓：订单号|' . $order->order_sn . "\r\n";
                        }

                        if ($order->direction_data == 1) {  //如果是多单 长
                            if ($nowPrice >= $order->take_profit) {
                                //止盈卖出
                                $selltype = 3;//止盈卖出
                                $pc_price = $order->take_profit;
                            }
                            if ($nowPrice <= $order->stop_loss) {
                                $selltype = 4;//止损卖出
                                $pc_price = $order->stop_loss;
                            }
                        } else {    //如果是空单 跌
                            if ($nowPrice <= $order->take_profit) {
                                $selltype = 3;//止盈卖出
                                $pc_price = $order->take_profit;
                            }
                            if ($nowPrice >= $order->stop_loss) {
                                $selltype = 4;//止损卖出
                                $pc_price = $order->stop_loss;
                            }
                        }

                        if (!isset($selltype)) {
                            echo '止盈止损未满足条件：订单号|' . $order->order_sn . "\r\n";
                            continue;
                        }

                        /**
                         * 买涨的情况 盈亏 = 现价-买入价 （正数为盈利、负数为亏损）
                         * 买跌的情况 盈亏 = 买入价-现价 （正数为盈利、负数为亏损）
                         */
                        $profitLoss = $order->direction_data == 1
                            ? round(($pc_price - $order->buy_price) * $order->buy_quantity, 2)
                            : round(($order->buy_price - $pc_price) * $order->buy_quantity, 2);

                        // todo: 计算卖出金额（本金+盈亏金额）
                        $sellMoney_currency = $sellMoney = $order->buy_cost + $profitLoss;

                        if ($sellMoney <= 0) $sellMoney = 0.01;

                        $rate = get_buy_fee_rate($order->user_id);

                        // todo: 计算卖出手续费，不再需要证交税，西班牙的印花税只有买入时候收取
                        $sxf_currency = $sxf = sell_commission($sellMoney, $rate);

                        $pingcangjine = $sellMoney - $sxf;
                        if ($pingcangjine <= 0) $pingcangjine = 0.01;

                        // 如果当前产品不是欧元结算，则需要去计算欧元成本
                        $eur_dongjie = $order->buy_cost;
                        $sell_exchange_rate = 1;
                        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                            $user = Users::where('id', $order->user_id)->first();
                            // 要卖出的金额
                            // 实时 EUR/USD 汇率,
                            // 价差值
                            $sellMoney = calculateEuroCost($sellMoney, $order->exchange_rate, $user->spread > 0 ? $user->spread : 0.00036);
                            $sxf = calculateEuroCost($sxf, $order->exchange_rate, $user->spread > 0 ? $user->spread : 0.00036);

                            $sell_exchange_rate = $order->exchange_rate * (1 - $user->spread);

                            // 计算卖出时候用户冻结余额应该减去多少，需要按照买入时候的汇率来计算
                            $eur_dongjie = calculateEuroCost($order->buy_cost, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
                        }

                        $user_info = UsersInfo::where('user_id', $order->user_id)->lockForUpdate()->first();

                        $before = $user_info->eur_balance;

                        $user_info->eur_balance += $sellMoney - $sxf;
                        $user_info->eur_dongjie -= $eur_dongjie;
                        $user_info->save();

                        // 修改持仓数据
                        $order->status = 2; //平仓
                        $order->sell_price = $pc_price; //平仓价格
                        $order->sell_money = $sellMoney_currency; // 卖出金额(本金+盈亏金额)
                        $order->profit_amount = $profitLoss;
                        $order->sell_fees = $sxf_currency;
                        $order->sell_exchange_rate = $sell_exchange_rate; // 卖出时候的汇率
                        $order->sell_time = time(); // 卖出时间
                        $order->sell_type = $selltype; // 卖出类型
                        $order->market_cap = $pc_price * $order->buy_quantity;
                        $order->save();

                        $protype = [
                            1 => '股票',
                            2 => '大宗商品',
                            3 => 'ETF',
                            4 => '外汇',
                            5 => '数字货币',
                        ];
                        self::recordMoneyChange([
                            'money_type' => 'EUR',
                            'user_id' => $order->user_id,
                            'order_sn' => $order->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "{$protype[$product->pro_type]}|平仓|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                            'type' => MoneyChange::TYPE_TRADE_FEE_SELL,
                            'before_money' => $before,
                            'money' => $sellMoney,
                            'after_money' => $before + $sellMoney,
                        ]);

                        if ($sxf) {
                            self::recordMoneyChange([
                                'money_type' => 'EUR',
                                'user_id' => $order->user_id,
                                'order_sn' => $order->order_sn,
                                'product_name' => $product->name,
                                'detailed' => "{$protype[$product->pro_type]}|平仓|手续费|,|产品名称|:|{$product->name}|,|代码|:{$product->symbol}",
                                'type' => MoneyChange::TYPE_TRADE_FEE_SELL,
                                'before_money' => $before + $sellMoney,
                                'money' => -$sxf,
                                'after_money' => $before + $sellMoney - $sxf,
                            ]);
                        }
                    } else {
                        // 正常挂持仓更新
                        echo date('Y-m-d H:i:s') . '实时更新持仓中的订单盈亏：' . json_encode($data) . "\r\n";
                        echo "开始更新盈虧=========================" . $order['order_sn'] . "\r\n";

                        // 加了杠杆的情况下，如果用户亏损的钱超过了他的买入本金，就立刻平仓
                        if ($order->lever > 1) {
                            echo $order->order_sn . "设置了杠杆\r\n";
                            if ($yingkui < 0 && $order->buy_cost + $yingkui <= 0) {
                                echo $order->order_sn . "设置了杠杆===用户亏损的钱超过了他的买入本金，执行平仓\r\n";
                                $order->status = 2;
                                $order->sell_type = 2;
                                $order->sell_time = time();
                                $order->sell_fees = 0; // 卖出手续费
                                $order->sell_price = $nowPrice; // 卖出价格
                                $order->profit_amount = -$order->buy_cost; // 赢亏

                                $userInfo = UsersInfo::query()->where('user_id', $order->user_id)->lockForUpdate()->first();

                                $eur_dongjie = $order->buy_cost;
                                if (in_array($product->type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                                    // 计算卖出时候用户冻结余额应该减去多少，需要按照买入时候的汇率来计算
                                    $eur_dongjie = calculateEuroCost($order->buy_cost, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
                                }

                                $userInfo->eur_dongjie -= $eur_dongjie;
                                $userInfo->save();

                                echo "杠杆强制平仓成功==" . date('Y-m-d H:i:s') . "==" . $order->order_sn . "\r\n";
                            }
                            $order->market_cap = $shizhi; // 当前市值
                        } else {
//                            var_dump($shizhi, $yingkui);
                            $order->market_cap = $shizhi;
                            $order->profit_amount = $yingkui;
                        }
                        $order->save();
                    }
                }
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                echo "系统错误" . $e->getMessage() . "\r\n";
            }
        }
    }
}
