<?php

namespace App\Services;

use App\Models\ExchangeRate;
use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\UserMessage;
use App\Models\UserOrder;
use App\Models\Users;
use App\Models\UsersInfo;
use Illuminate\Support\Facades\DB;

class IpoService
{
    /**
     * 新股转股票
     */
    public static function ipoToProduct()
    {
        $todayStart = date("Y-m-d 00:00:00");
        $todayEnd = date("Y-m-d 23:59:59");

        $ipos = Ipo::query()
            ->where('state', 1)
            ->where('to_market_date', '>=', $todayStart)
            ->where('to_market_date', '<=', $todayEnd)
            ->get();
        if ($ipos->isEmpty()) {
            echo "今日暂无新股上市\r\n";
            return false;
        }
        foreach ($ipos as $item) {
            if ($item->to_pro_id > 0) {
                echo "新股已经已经转展品了|代码:{$item->symbol}|{$item->country}|{$item->exchange}\r\n";
                continue;
            }
            // 先查找股票存不存在
            $product = Product::query()->where('country', $item->country)->where('exchange', $item->exchange)->where('symbol', $item->symbol)->first();
            if ($product) {
                // 存在，就把股票的id 绑定到新股上面去，就完成新股转展品了
                echo "匹配成功|代码:{$item->symbol}|{$product->id}|股票产品已存在,开始绑定\r\n";
                $productid = $product->id;
                Ipo::query()->where('id', $item->id)->update(['to_pro_id' => $productid]);
            } else {
                echo "代码:{$item->symbol}|{$item->country}|{$item->exchange}|股票列表中，未匹配到当前新股\r\n";
            }
        }
    }

    /**
     * 新股转持仓
     */

    public static function ipoToOrder()
    {
        // 获取已中签，并且未持仓的数据
        $ipoOrders = IpoOrder::query()
            ->with('ipo')
            ->where('state', 1)
            ->where('grant_state', 0)
            ->get();

        if ($ipoOrders->isEmpty()) {
            echo "暂无已中签、且未持仓的数据\r\n";
            return false;
        }
        foreach ($ipoOrders as $order) {
            if ($order->check_state == 0) {
                echo $order->order_sn . "|待审核状态\r\n";
                continue;
            }

            // 审核不通过的退钱
            if ($order->check_state == 2) {
                self::returnMoney($order);
                continue;
            }

            if ($order->ipo->to_pro_id == 0) {
                echo $order->order_sn . "|新股记录还没有转成产品\r\n";
                continue;
            }

            if ($order->pay_state != 1) {
                echo $order->order_sn . "|申购记录还没有支付完成\r\n";
                continue;
            }

            $now = time();
            $toMarketDate = strtotime($order->ipo->to_market_date . ' 09:00:00');
            if ($now < $toMarketDate) {
                echo "当前时间不处于发券日\r\n";
                continue;
            }
            $totalMoney = $order->zhongqianshu * $order->buy_price;
            $rate = $order->buy_in_fee_rate;
            $buyFees = buy_commission($totalMoney, $rate);

            // 查询当前股票
            $pro = Product::query()->where('id', $order->ipo->to_pro_id)->first();
            $jiangshan = new JiangshanService();
            $pro = $jiangshan->get_new_data_serices($pro); // 获取最新价格
            $marketCap = $pro->price * $order->zhongqianshu;
            $yingkui = round(($pro->price - $order->buy_price) * $order->zhongqianshu, 2);

            DB::beginTransaction();
            try {
                $currentOrder = IpoOrder::query()->where('id', $order->id)->lockForUpdate()->first();
                // 添加持仓
                UserOrder::create([
                    'user_id' => $order->user_id,
                    'order_sn' => $order->order_sn,
                    'product_id' => $pro->id,
                    'product_type' => $pro->pro_type,
                    'direction_data' => 1, // 买涨
                    'buy_type' => 1, // 市价
                    'buy_price' => $order->buy_price,
                    'buy_quantity' => $order->zhongqianshu,
                    'lever' => 1, // 杠杆
                    'is_open_buy' => 0,
                    'status' => 1,
                    'buy_cost' => $order->zhongqianshu * $order->buy_price,
                    'profit_amount' => $yingkui,
                    'market_cap' => $marketCap,
                    'take_profit' => 0,
                    'stop_loss' => 0,
                    'buy_time' => time(),
                    'buy_fees' => $buyFees, // 手续费
                    'buy_fees_rate' => $rate, // 手续费费率
                    'source' => 2, // 新股转入
                    'currency' => $pro->currency, // 使用货币
                    'exchange_rate' => $order->exchange_rate, // 汇率
                    'user_spread' => $order->user_spread, // 用户汇率价差
                ]);
                $currentOrder->grant_state = 1; // 持仓状态修改
                $currentOrder->save();
                // 给用户发消息
                $message = [
                    'user_id' => $currentOrder->user_id,
                    'content' => "尊敬的会员|,|您申购的新股|{$pro->name}|代码|{$pro->symbol}|已中签|,|中签数量是|:|{$currentOrder->zhongqianshu}|股|,|已经扣除对应款项并且转为持仓,新股上市后即可正常交易|!"
                ];
                UserMessage::create($message);
                DB::commit();
                echo $currentOrder->order_sn . ":已转持仓\r\n";
            } catch (\Exception $e) {
                DB::rollBack();
                echo $currentOrder->order_sn . ":转持仓失败|{$e->getMessage()}\r\n";
            }


        }
    }

    /**
     * 自動公佈中簽
     * 1:到了抽籤日，計算用戶提前設置的中簽數，
     * 2：計算賬戶餘額
     * 3：給用戶中簽扣款
     *      a:金額充足，紫銅扣款，設置支付狀態已已支付，審核狀態審核通過，等新股上市后，就會自動轉持倉
     *      b:金額不足，扣部分，後面再有定時任務自動補齊剩餘的扣款
     */
    public static function autoPrimary()
    {
        // 獲取今天抽籤的新股
        $todayStart = date('Y-m-d 00:00:00');
        $todayEnd = date('Y-m-d 23:59:59');
        $ipoIds = Ipo::query()
            ->where('state', 1)
            ->where('chouqian_date', '>=', $todayStart)
            ->where('chouqian_date', '<=', $todayEnd)
            ->pluck('id'); // 直接返回數組
        if (empty($ipoIds)) {
            echo date('Y-m-d') . "--今日暂无新股发券\r\n";
        }

        // 獲取這些新股下的申購訂單 只获取 申购中的订单
        $ipoOrders = IpoOrder::query()->whereIn('ipo_id', $ipoIds)->where('state', 0)->get();

        foreach ($ipoOrders as $item) {
            DB::beginTransaction();
            try {
                $ipo = Ipo::query()->find($item->ipo_id);
                $order = IpoOrder::query()->where('id', $item->id)->lockForUpdate()->first();
                if ($order->zhongqianshu <= 0) {
                    throw new \Exception("{$item->order_sn}:{$ipo->name}|暂未设置中签数量");
                }
//                if ($order->state != 0) {
//                    throw new \Exception("{$item->order_sn}:{$ipo->name}|订单不处于申购中状态");
//                }

                $users = Users::query()->where('id', $order->user_id)->first();
                $exchange_rate = 1;

                $buy_price = $order->buy_price;
                if (!in_array($ipo->country, ['de', 'sp'])) {
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();

                    // 换算金额和费用
                    $buy_price = calculateEuroCost($order->buy_price, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);
                    $exchange_rate = $exchangeRate->rate;
                }

                $total = $order->zhongqianshu * $buy_price; // 縂需要繳納的本金
                $rate = $order->buy_in_fee_rate; // 根据提交的费率来计算
                $buyFees = buy_commission($total, $rate); // 手續費

                $userInfo = UsersInfo::where('user_id', $order->user_id)->lockForUpdate()->firstOrFail();
                $balance = 'eur_balance';
                $dongjie = 'eur_dongjie';
                $before = $userInfo->$balance;

                $total = round($total, 3);
                $buyFees = round($buyFees, 3);

                $allTotal = $buyFees + $total; // 總共需要扣錢  本金+手續費
                if ($before < $allTotal) {
                    // 資金不足
                    $oneStockFees = buy_commission($buy_price, $rate); // 一股手续费
                    $nums = intval($before / ($buy_price + $oneStockFees)); // 直接去掉小数，也不要四舍五入
                    if ($nums <= 0) {
                        // 一股都不够
                        $order->yingrenjiao = $allTotal;
                        $order->yirenjiao = 0;
                        $order->xurenjiao = $allTotal;
                        $order->state = 1;
                        $order->renjiaocishu = 1;
                        $order->renjiaonum = 0;
                        $order->exchange_rate = $exchange_rate;
                        $order->user_spread = $users->spread > 0 ? $users->spread : 0.00036;
                        $order->save();
                        $userMessage = [
                            'user_id' => $order->user_id,
                            'content' => "尊敬的会员|,|您申购的新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|已中签|,|中签数量|:|{$order->zhongqianshu}|股|,|您的余额不足以扣除对应款项|,|需要|:|{$allTotal}|,|请尽快存款补足应交缴款|!",
                        ];
                        echo "{$order->order_sn}----{$ipo->name}----一股都不够扣\r\n";
                    } else {
                        // 有部分资金
                        // 根据资金能买的股数
                        $canTotal = $nums * $buy_price;
                        $canBuyFees = buy_commission($canTotal, $rate);
                        $canAllTotal = $canTotal + $canBuyFees;

                        // 扣資金,凍結資金
                        $userInfo->$balance -= round($canAllTotal, 3);
                        $userInfo->$dongjie += $canTotal;
                        $userInfo->save();


                        // 应缴 、已缴、需缴
                        $xurenjiao = $allTotal - $canAllTotal;
                        $order->yingrenjiao = $allTotal;
                        $order->yirenjiao = $canAllTotal;
                        $order->xurenjiao = $xurenjiao;
                        $order->state = 1;
                        $order->pay_state = 2; // 支付中
                        $order->renjiaocishu = 1;
                        $order->renjiaonum = $nums;
                        $order->exchange_rate = $exchange_rate;
                        $order->user_spread = $users->spread > 0 ? $users->spread : 0.00036;
                        $order->save();

                        // 添加资金流水
                        MoneyChange::create([
                            'money_type' => "EUR",
                            'user_id' => $order->user_id,
                            'order_sn' => $order->order_sn,
                            'product_name' => $order->name,
                            'detailed' => "股票申购|代码|{$ipo->symbol}|名称|{$ipo->name}",
                            'type' => MoneyChange::TYPE_TRADE,
                            'before_money' => $before,
                            'money' => -$canTotal,
                            'after_money' => $before - $canTotal,
                        ]);

                        if ($canBuyFees > 0) {
                            // 添加资金流水-手续费
                            MoneyChange::create([
                                'money_type' => "EUR",
                                'user_id' => $order->user_id,
                                'order_sn' => $order->order_sn,
                                'product_name' => $ipo->name,
                                'detailed' => "股票申购手续费|代码|{$ipo->symbol}|名称|{$ipo->name}",
                                'type' => MoneyChange::TYPE_TRADE_FEE,
                                'before_money' => $before - $canTotal,
                                'money' => -$canBuyFees,
                                'after_money' => $before - $canTotal - $canBuyFees,
                            ]);
                        }
                        $userMessage = [
                            'user_id' => $order->user_id,
                            'content' => "尊敬的会员|,|您申购的新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|已中签|,|中签数量|:|{$order->zhongqianshu}|股|,|但您的账户可用余额不足|,|已经扣除部分对应款项|:|{$canAllTotal}|还需|:|{$xurenjiao}|,|请尽快存款补足应交缴款|!",
                        ];

                        echo "{$order->order_sn}----{$ipo->name}----账户金额不充足，|只能购买{$nums}股|已经扣除部分对应款项|:|{$canAllTotal}|还需|:|{$xurenjiao}\r\n";
                    }
                }
                else {
                    // 資金充足
                    // 扣資金,凍結資金
                    $userInfo->$balance -= $allTotal;
                    $userInfo->$dongjie += $total;
                    $userInfo->save();

                    // 修改申購訂單
                    $order->xurenjiao = 0;
                    $order->yirenjiao = $allTotal;
                    $order->yingrenjiao = $allTotal;
                    $order->pay_state = 1;
                    $order->state = 1;
                    $order->renjiaocishu = 1;
                    $order->renjiaonum = $order->zhongqianshu;
                    $order->exchange_rate = $exchange_rate;
                    $order->user_spread = $users->spread > 0 ? $users->spread : 0.00036;
                    $order->save();

                    // 添加资金流水
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $order->user_id,
                        'order_sn' => $order->order_sn,
                        'product_name' => $order->name,
                        'detailed' => "股票申购|代码|{$ipo->symbol}|名称|{$ipo->name}",
                        'type' => MoneyChange::TYPE_TRADE,
                        'before_money' => $before,
                        'money' => -$total,
                        'after_money' => $before - $total,
                    ]);

                    if ($buyFees > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $order->user_id,
                            'order_sn' => $order->order_sn,
                            'product_name' => $ipo->name,
                            'detailed' => "股票申购手续费|代码|{$ipo->symbol}|名称|{$ipo->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE,
                            'before_money' => $before - $total,
                            'money' => -$buyFees,
                            'after_money' => $before - $total - $buyFees,
                        ]);
                    }
                    $userMessage = [
                        'user_id' => $order->user_id,
                        'content' => "尊敬的会员|,|您申购的新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|已中签|,|中签数量|:|{$order->zhongqianshu}|股|,|已经扣除对应款项|,|新股上市后即可正常交易|!",
                    ];
                    echo "{$order->order_sn}----{$ipo->name}----账户资金充足。=====已中签，已扣除全部款项\r\n";
                }
                UserMessage::create($userMessage);

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                echo "失敗：" . $e->getMessage() . "\r\n";
            }

        }

    }

    /**
     * 自动扣费
     * 已开始抽签，上市前面的新股
     * 已中签，待审核，且是待支付或者支付中的，需要给他们自动扣费
     *
     */
    public static function autoDeduction()
    {
        $today = date('Y-m-d 23:59:59');
        $ipoIds = Ipo::query()->where('chouqian_date', '<=', $today)->pluck('id')->all();
        if (empty($ipoIds)) {
            echo "今日暂无新股发券\r\n";
            return true;
        }
        foreach ($ipoIds as $ipoId) {
            // 已中签，待支付（支付中） 未审核的新股申购
            $ipoOrders = IpoOrder::query()
                ->where('ipo_id', $ipoId)
                ->where('state', 1)
                ->whereIn('pay_state', [0, 2])
                ->where('check_state', 0)
                ->where('grant_state', 0)
                ->get();
            if ($ipoOrders->isEmpty()) {
                echo "暂无订单需要扣费\r\n";
                continue;
            }
            foreach ($ipoOrders as $item) {
                DB::beginTransaction();
                try {
                    $order = IpoOrder::query()->where('id', $item->id)->lockForUpdate()->first();
                    $ipo = Ipo::query()->find($order->ipo_id);
                    $userInfo = UsersInfo::where('user_id', $order->user_id)->lockForUpdate()->firstOrFail();
                    $balance = 'eur_balance';
                    $dongjie = 'eur_dongjie';
                    $before = $userInfo->$balance;

//                    $total = $order->zhongqianshu * $order->buy_price; // 縂需要繳納的本金
//                    $buyFees = buy_commission($total); // 手續費

                    $buy_price = $order->buy_price;
                    if (!in_array($ipo->country, ['de', 'sp'])) {
                        // 换算金额和费用
                        $buy_price = calculateEuroCost($order->buy_price, $order->exchange_rate, $order->user_spread);
                    }

                    // 计算用户当前余额可购买股数
                    $rate = $order->buy_in_fee_rate;
                    $oneStockFees = buy_commission($buy_price, $rate); // 一股手续费
                    $num = intval($before / ($buy_price + $oneStockFees));
                    if ($num <= 0) {
                        throw  new \Exception("{$order->order_sn}:{$ipo->name}|账户资金一股都不够扣");
                    }

                    // 计算剩余未支付的张数 zhongqianshu是股数 renjiaonum是张数
                    $syNum = $order->zhongqianshu - $order->renjiaonum;
                    echo "{$order->order_sn}:{$ipo->name}|剩余未支付的股数：{$syNum}\r\n";

                    // 用户余额能够全额支付剩余张数的情况
                    if ($num >= $syNum) {
                        $total = $syNum * $buy_price; // 縂需要繳納的本金
                        $buyFees = buy_commission($total, $rate); // 手續費
                        $allTotal = $total + $buyFees;
                        $renjiaonum = $syNum;
                        echo "{$order->order_sn}:{$ipo->name}|全部|用户余额能够全额支付剩余股数的情况：{$syNum},此次扣款金额为:{$allTotal}\r\n";
                    } else {
                        $renjiaonum = $num;
                        // 用户余额只够支付部分张数
                        $total = $num * $buy_price; // 可需要繳納的本金
                        $buyFees = buy_commission($total, $rate); // 手續費
                        $allTotal = $total + $buyFees;
                        echo "{$order->order_sn}:{$ipo->name}|部分|用户余额只够支付部分股数：{$num},此次扣款金额为:{$allTotal}\r\n";
                    }


                    $xurenjiao = $order->xurenjiao - $allTotal;
                    $payState = (int)$xurenjiao > 0 ? 2 : 1; // 2:支付中，1：已支付

                    $order->renjiaonum += $renjiaonum;
                    $order->yirenjiao += $allTotal;
                    $order->xurenjiao -= $allTotal;
                    $order->renjiaocishu += 1;
                    $order->pay_state = $payState;
                    $order->save();

                    // 调整用户金额
                    $userInfo->$balance -= round($allTotal, 3);
                    $userInfo->$dongjie += $total;
                    $userInfo->save();
                    // 添加资金流水
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $order->user_id,
                        'order_sn' => $order->order_sn,
                        'product_name' => $order->name,
                        'detailed' => "股票申购|代码|{$ipo->symbol}|名称|{$ipo->name}",
                        'type' => MoneyChange::TYPE_TRADE,
                        'before_money' => $before,
                        'money' => -$total,
                        'after_money' => $before - $total,
                    ]);

                    if ($buyFees > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $order->user_id,
                            'order_sn' => $order->order_sn,
                            'product_name' => $ipo->name,
                            'detailed' => "股票申购手续费|代码|{$ipo->symbol}|名称|{$ipo->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE,
                            'before_money' => $before - $total,
                            'money' => -$buyFees,
                            'after_money' => $before - $total - $buyFees,
                        ]);
                    }

                    // 用户消息
                    if ($payState == 1) {
                        $userMessage = [
                            'user_id' => $order->user_id,
                            'content' => "尊敬的会员|,|您申购的新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|已中签|,|中签数量|:|{$order->zhongqianshu}|股|,|已经扣除对应款项|,|新股上市后即可正常交易|!",
                        ];
                    } else {
                        $userMessage = [
                            'user_id' => $order->user_id,
                            'content' => "尊敬的会员|,|您申购的新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|已中签|,|中签数量|:|{$order->zhongqianshu}|股|,|但您的账户可用余额不足|,|已经扣除部分对应款项|:|{$allTotal}|还需|:|{$xurenjiao}|,|请尽快存款补足应交缴款|!",
                        ];
                    }
                    UserMessage::create($userMessage);
                    DB::commit();
                    echo "{$order->order_sn}:{$ipo->name}|扣款成功\r\n";
                } catch (\Exception $e) {
                    DB::rollBack();
                    echo "扣款失败:" . $e->getMessage() . "\r\n";
                }
            }
        }

        return true;
    }

    /**
     * 將已支付的訂單自動設置為審核通過
     */
    public static function autoCheckPass()
    {
        $today = date('Y-m-d 23:59:59');
        $ipos = Ipo::query()->where('chouqian_date', '<=', $today)->get();
        if ($ipos->isEmpty()) {
            echo "今日暂无新股发券\r\n";
            return true;
        }
        foreach ($ipos as $ipo) {
            $toMarketDate = strtotime($ipo->to_market_date . ' 09:30:00');
            $now = time();
            if ($now > $toMarketDate) {
                IpoOrder::query()
                    ->where('ipo_id', $ipo->id)
                    ->where('state', 1)
                    ->where('pay_state', 1)
                    ->where('check_state', 0)
                    ->update(['check_state' => 1]);
                echo "{$ipo->name}|{$ipo->symbol}的申购订单执行审核通过\r\n";
            } else {
                echo "{$ipo->name}|{$ipo->symbol}当前时间不处于发券时间\r\n";
            }
        }
        return true;
    }

    /**
     * 给用户退钱
     * @param IpoOrder $order
     * @return bool
     */
    protected
    static function returnMoney($order)
    {
        if ($order->renjiaonum == 0) {
            // 已认缴股数是0  不需要退钱，直返修改订单状态
            IpoOrder::query()->where('id', $order->id)->update(['grant_state' => 2]);
            return true;
        }
        $country = $order->ipo->country;
        DB::beginTransaction();
        try {
            $ipoOrder = IpoOrder::query()->where('id', $order->id)->lockForUpdate()->first();
            $ipo = Ipo::query()->where('id', $ipoOrder->ipo_id)->first();
            //
            $buy_price = $ipoOrder->buy_price;
            if (!in_array($ipo->country, ['de', 'sp'])) {
                // 换算金额和费用
                $buy_price = calculateEuroCost($ipoOrder->buy_price, $ipoOrder->exchange_rate, $ipoOrder->user_spread);
            }
            $returnMoney = round($ipoOrder->renjiaonum * $buy_price, 3);
            $rate = $ipoOrder->buy_in_fee_rate;
            $buyFees = buy_commission($returnMoney, $rate);


            $userInfo = UsersInfo::where('user_id', $ipoOrder->user_id)->lockForUpdate()->firstOrFail();
            $balance = 'eur_balance';
            $dongjie = 'eur_dongjie';
            $before = $userInfo->$balance;


            // 给用户添加
            $userInfo->$balance += ($buyFees + $returnMoney);
            $userInfo->$dongjie -= $returnMoney;
            $userInfo->save();

            // 添加资金流水
            MoneyChange::create([
                'money_type' => 'EUR',
                'user_id' => $ipoOrder->user_id,
                'order_sn' => $ipoOrder->order_sn,
                'product_name' => $ipoOrder->name,
                'detailed' => "申购费退还|代码|{$ipo->symbol}|名称|{$ipo->name}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => $returnMoney,
                'after_money' => $before + $returnMoney,
            ]);

            if ($buyFees > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $ipoOrder->user_id,
                    'order_sn' => $ipoOrder->order_sn,
                    'product_name' => $ipoOrder->name,
                    'detailed' => "申购手续费退还|代码|{$ipo->symbol}|名称|{$ipo->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE,
                    'before_money' => $before + $returnMoney,
                    'money' => $buyFees,
                    'after_money' => $before + $returnMoney + $buyFees,
                ]);
            }
            $ipoOrder->grant_state = 2;
            $ipoOrder->save();
            DB::commit();
            echo "{$order->order_sn}|审核拒绝，退款成功\r\n";
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            echo "{$order->order_sn}|退钱失败:" . $e->getMessage() . "\r\n";
            return true;
        }


    }
}
