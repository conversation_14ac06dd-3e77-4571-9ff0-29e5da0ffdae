<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Email Configuration for Better Deliverability
    |--------------------------------------------------------------------------
    |
    | This configuration file contains settings to improve email deliverability
    | and reduce the chance of emails being marked as spam.
    |
    */

    'smtp' => [
        'host' => env('MAIL_HOST', 'server.cloudcone.email'),
        'port' => env('MAIL_PORT', 587),
        'encryption' => env('MAIL_ENCRYPTION', 'tls'),
        'username' => env('MAIL_USERNAME', '<EMAIL>'),
        'password' => env('MAIL_PASSWORD', 'q@mwKqNBd4QpYLF'),
    ],

    'from' => [
        'address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'name' => env('MAIL_FROM_NAME', 'Vyntor'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Headers for Better Deliverability
    |--------------------------------------------------------------------------
    |
    | These headers help improve email deliverability and reduce spam scores.
    |
    */
    'headers' => [
        'x_mailer' => env('APP_NAME', 'Vyntor'),
        'x_priority' => '3', // Normal priority (1=High, 3=Normal, 5=Low)
        'list_unsubscribe' => '<mailto:<EMAIL>>',
        'x_auto_response_suppress' => 'OOF, DR, RN, NRN, AutoReply',
        'return_path' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
    ],

    /*
    |--------------------------------------------------------------------------
    | DNS Records Recommendations
    |--------------------------------------------------------------------------
    |
    | To improve email deliverability, add these DNS records to your domain:
    |
    | SPF Record (TXT):
    | v=spf1 include:_spf.google.com include:server.cloudcone.email ~all
    |
    | DMARC Record (TXT) for _dmarc.vyntor.com:
    | v=DMARC1; p=quarantine; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; fo=1
    |
    | DKIM Record:
    | Contact your email provider (CloudCone) to set up DKIM signing
    |
    | MX Record:
    | Priority 10: server.cloudcone.email
    |
    */



    /*
    |--------------------------------------------------------------------------
    | Email Templates Configuration
    |--------------------------------------------------------------------------
    */
    'templates' => [
        'verification' => [
            'subject' => [
                'register' => 'Email Verification Code',
                'reset_password' => 'Password Reset Verification Code',
            ],
            'purpose' => [
                'register' => 'register your account',
                'reset_password' => 'reset your password',
            ],
        ],
    ],


];
