<?php

namespace App\Services;

use http\Env;
use App\Jobs\SendVerificationEmail;
use App\Mail\VerificationCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class EmailService
{
    /**
     * 异步发送验证码邮件（推荐）
     *
     * @param string $email
     * @param string $code
     * @param string $type
     * @return bool
     */
    public static function sendVerificationCodeAsync($email, $code, $type = 'register')
    {
        try {
            SendVerificationEmail::dispatch($email, $code, $type);

            Log::info("Verification code email queued successfully", [
                'email' => $email,
                'type' => $type,
                'timestamp' => now()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to queue verification code email", [
                'email' => $email,
                'type' => $type,
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);

            // 如果队列失败，回退到同步发送
            return self::sendVerificationCode($email, $code, $type);
        }
    }

    /**
     * 同步发送验证码邮件
     *
     * @param string $email
     * @param string $code
     * @param string $type
     * @return bool
     */
    public static function sendVerificationCode($email, $code, $type = 'register')
    {
        try {
            $emailConfig = config('email');
            $subject = $emailConfig['templates']['verification']['subject'][$type];
            $purpose = $emailConfig['templates']['verification']['purpose'][$type];
            $appName = $emailConfig['from']['name'];

            // 使用CID内嵌邮件类发送
            Mail::to($email)->send(new VerificationCodeMail($code, $type, $subject, $purpose, $appName));

            Log::info("Verification code email sent successfully with CID logo", [
                'email' => $email,
                'type' => $type,
                'timestamp' => now()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to send verification code email", [
                'email' => $email,
                'type' => $type,
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);
            return false;
        }
    }

    /**
     * 配置邮件设置
     */
    private static function configureMailSettings()
    {
        $emailConfig = config('email');

        Config::set([
            'mail.default' => 'smtp',
            'mail.mailers.smtp.host' => $emailConfig['smtp']['host'],
            'mail.mailers.smtp.port' => $emailConfig['smtp']['port'],
            'mail.mailers.smtp.encryption' => $emailConfig['smtp']['encryption'],
            'mail.mailers.smtp.username' => $emailConfig['smtp']['username'],
            'mail.mailers.smtp.password' => $emailConfig['smtp']['password'],
            'mail.from.address' => $emailConfig['from']['address'],
            'mail.from.name' => $emailConfig['from']['name'],
        ]);
    }

    /**
     * 生成HTML邮件内容
     */
    private static function generateHtmlContent($subject, $purpose, $code, $appName)
    {
        // 获取优化的logo
        $logoHtml = self::getOptimizedLogoHtml($appName);

        return "
        <!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <meta http-equiv='X-UA-Compatible' content='IE=edge'>
            <title>{$subject}</title>
            <!--[if mso]>
            <noscript>
                <xml>
                    <o:OfficeDocumentSettings>
                        <o:PixelsPerInch>96</o:PixelsPerInch>
                    </o:OfficeDocumentSettings>
                </xml>
            </noscript>
            <![endif]-->
        </head>
        <body style='margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f4f4f4; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;'>
            <table role='presentation' style='width: 100%; border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;'>
                <tr>
                    <td style='padding: 20px 0;'>
                        <table role='presentation' style='width: 100%; max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;'>
                            <!-- Header -->
                            <tr>
                                <td style='padding: 40px 30px 30px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px 8px 0 0;'>
                                    {$logoHtml}
                                </td>
                            </tr>
                            <!-- Content -->
                            <tr>
                                <td style='padding: 40px 30px;'>
                                    <h2 style='margin: 0 0 20px; color: #333333; font-size: 24px; font-weight: 600; line-height: 1.2;'>{$subject}</h2>
                                    <p style='margin: 0 0 20px; color: #666666; font-size: 16px; line-height: 1.5;'>Dear User,</p>
                                    <p style='margin: 0 0 30px; color: #666666; font-size: 16px; line-height: 1.5;'>You are trying to {$purpose}. Please use the verification code below:</p>

                                    <!-- Verification Code Box -->
                                    <div style='background-color: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; padding: 30px; text-align: center; margin: 30px 0;'>
                                        <p style='margin: 0 0 10px; color: #666666; font-size: 14px; text-transform: uppercase; letter-spacing: 1px; font-weight: 600;'>Verification Code</p>
                                        <p style='margin: 0; font-family: \"Courier New\", Courier, monospace; font-size: 32px; font-weight: bold; color: #333333; letter-spacing: 8px;'>{$code}</p>
                                    </div>

                                    <p style='margin: 0 0 20px; color: #666666; font-size: 16px; line-height: 1.5;'>This verification code will expire in <strong>10 minutes</strong>. Please do not share this code with anyone.</p>
                                    <p style='margin: 0 0 20px; color: #666666; font-size: 16px; line-height: 1.5;'>If you did not request this verification code, please ignore this email.</p>

                                    <!-- Security Notice -->
                                    <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;'>
                                        <p style='margin: 0; color: #856404; font-size: 14px; line-height: 1.4;'><strong>Security Notice:</strong> We will never ask you to share your verification code via phone, email, or any other method.</p>
                                    </div>
                                </td>
                            </tr>
                            <!-- Footer -->
                            <tr>
                                <td style='padding: 30px; background-color: #f8f9fa; border-radius: 0 0 8px 8px; text-align: center;'>
                                    <p style='margin: 0 0 10px; color: #999999; font-size: 14px; line-height: 1.4;'>This is an automated message, please do not reply to this email.</p>
                                    <p style='margin: 0; color: #999999; font-size: 14px; line-height: 1.4;'>© " . date('Y') . " {$appName}. All rights reserved.</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </body>
        </html>";
    }

    /**
     * 生成纯文本邮件内容
     */
    private static function generateTextContent($subject, $purpose, $code, $appName)
    {
        return "{$subject}\n\n"
            . "Dear User,\n\n"
            . "You are trying to {$purpose}.\n\n"
            . "Verification Code: {$code}\n\n"
            . "This verification code will expire in 10 minutes. Please do not share this code with anyone.\n\n"
            . "If you did not request this verification code, please ignore this email.\n\n"
            . "Security Notice: We will never ask you to share your verification code via phone, email, or any other method.\n\n"
            . "This is an automated message, please do not reply to this email.\n\n"
            . "© " . date('Y') . " {$appName}. All rights reserved.";
    }
}
