# 数据库死锁问题解决方案

## 🔍 问题分析

### 死锁原因
1. **多进程并发更新**：API请求、WebSocket推送、定时任务同时更新同一产品
2. **缺乏并发控制**：没有使用数据库锁和缓存锁机制
3. **更新频率过高**：同一产品短时间内被多次更新

### 错误日志特征
```
General error: 1205 Lock wait timeout exceeded; try restarting transaction
```

## 🛠️ 解决方案

### 1. 新增安全更新服务
- **文件**: `app/Services/ProductPriceUpdateService.php`
- **功能**: 提供带锁机制的安全价格更新方法
- **特性**:
  - 缓存锁防止并发更新
  - 数据库行锁防止死锁
  - 最小更新间隔控制
  - 批量更新优化

### 2. 优化江山服务
- **文件**: `app/Services/JiangshanService.php`
- **改进**:
  - 单个产品更新使用事务和行锁
  - 批量更新使用安全事务
  - 新增优化版批量更新方法

### 3. 优化TwelveData服务
- **文件**: `app/Services/TwelvedataNewService.php`
- **改进**: 使用安全更新服务替代直接数据库操作

### 4. 数据库配置优化
- **文件**: `config/database.php`
- **优化**:
  - 设置锁等待超时时间为10秒
  - 优化连接参数
  - 使用缓冲查询

### 5. 定时清理任务
- **文件**: `app/Console/Commands/CleanupPriceLocks.php`
- **功能**: 清理过期的价格更新锁
- **调度**: 每5分钟执行一次

## 📋 使用方法

### 替换现有更新调用

**原来的调用**:
```php
$jiangshan = new JiangshanService();
$jiangshan->get_new_data_serices($product);
```

**推荐的调用**:
```php
$jiangshan = new JiangshanService();
$jiangshan->get_new_data_serices($product); // 已优化，可直接使用

// 或者批量更新使用优化版本
$jiangshan->get_new_data_serices_list_optimized($products, $country);
```

### 直接使用安全更新服务
```php
use App\Services\ProductPriceUpdateService;

// 单个产品更新
$success = ProductPriceUpdateService::safeUpdatePrice($product, [
    'price' => 146.98,
    'open' => 145.28,
    'high' => 147.82,
    'low' => 145.16
]);

// 批量更新
$updates = [
    ['product_id' => 1, 'data' => ['price' => 146.98, ...]],
    ['product_id' => 2, 'data' => ['price' => 152.30, ...]]
];
$results = ProductPriceUpdateService::batchSafeUpdate($updates);
```

## 🚀 部署步骤

### 1. 启用定时任务
```bash
# 在宝塔面板或crontab中添加
* * * * * cd /www/wwwroot/sk_etoro_v2 && php artisan schedule:run >> /dev/null 2>&1
```

### 2. 手动清理锁（如需要）
```bash
php artisan price:cleanup-locks
```

### 3. 监控日志
```bash
# 查看价格更新日志
tail -f storage/logs/laravel.log | grep "Product price"

# 查看死锁错误
tail -f storage/logs/laravel.log | grep "Lock wait timeout"
```

## 📊 性能优化效果

### 优化前
- 频繁死锁错误
- 价格更新失败
- 系统响应缓慢

### 优化后
- 消除死锁问题
- 提高更新成功率
- 减少数据库压力
- 支持高并发访问

## ⚠️ 注意事项

1. **缓存依赖**: 需要Redis缓存支持
2. **更新间隔**: 默认最小更新间隔为5秒
3. **锁超时**: 缓存锁默认30秒超时
4. **监控建议**: 定期检查价格更新日志

## 🔧 配置参数

可在 `ProductPriceUpdateService` 中调整：
- `MIN_UPDATE_INTERVAL`: 最小更新间隔（秒）
- `CACHE_LOCK_DURATION`: 缓存锁持续时间（秒）

## 📞 故障排查

### 如果仍有死锁
1. 检查是否有其他地方直接更新product表
2. 确认Redis缓存正常工作
3. 检查数据库连接池配置
4. 考虑增加更新间隔时间

### 性能监控
```bash
# 查看MySQL进程列表
SHOW PROCESSLIST;

# 查看锁等待情况
SHOW ENGINE INNODB STATUS;
```
