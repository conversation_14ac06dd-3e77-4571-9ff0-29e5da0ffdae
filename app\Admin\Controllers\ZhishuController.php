<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Zhishu;
use App\Models\Product as Model;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ZhishuController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Zhishu(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableActions();

            $country = request()->get('country');
            $model = $grid->model();
            if (!$country) {
                $model->where('country', 'de');
            }
            $model->orderBy('state', 'desc')
                ->orderBy('weight', 'desc');

            $grid->column('id')->sortable();
            $grid->column('country');
            $grid->column('name');
            $grid->column('symbol');
            $grid->column('price');
            $grid->column('open');
            $grid->column('high');
            $grid->column('low');
            $grid->column('close');
//            $grid->column('volume');
//            $grid->column('previous_close');
            $grid->column('change')->display(function ($change) {
                if ($this->percent_change > 0) {
                    return "<span style='color:#00a870'>{$change}⬆</span>";
                } elseif ($this->percent_change < 0) {
                    return "<span style='color:red'>{$change}⬇</span>";
                } else {
                    return $change;
                }
            });
            $grid->column('percent_change')->display(function ($percent_change) {
                if ($percent_change > 0) {
                    return "<span style='color:#00a870'>{$percent_change}⬆</span>";
                } elseif ($percent_change < 0) {
                    return "<span style='color:red'>{$percent_change}⬇</span>";
                } else {
                    return $percent_change;
                }
            });
//            $grid->column('average_volume');
            $grid->column('is_hot')->switch();
            $grid->column('state')->switch();
            $grid->column('weight')->editable(true);;
//            $grid->column('other_id');
//            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();
                $filter->expand(); // 让筛选默认展开
                $filter->equal('country')->radio([
                    'us' => '美国',
                    'de' => '德国',
                    'sp' => '西班牙',
                    'fr' => '法国',
                    'en' => '英国',
                    'jp' => '日本',
                    'my' => '马来西亚',
                ])->width(12)->default('de');
            });

            $grid->paginate(10);

            // 禁用分页选择器
            $grid->disablePerPages();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Zhishu(), function (Show $show) {
            $show->field('id');
            $show->field('country');
            $show->field('name');
            $show->field('symbol');
            $show->field('price');
            $show->field('open');
            $show->field('high');
            $show->field('low');
            $show->field('close');
            $show->field('volume');
            $show->field('previous_close');
            $show->field('change');
            $show->field('percent_change');
            $show->field('average_volume');
            $show->field('state');
            $show->field('is_hot');
            $show->field('weight');
            $show->field('other_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Zhishu(), function (Form $form) {
            $form->display('id');
            $form->text('country');
            $form->text('name');
            $form->text('symbol');
            $form->text('price');
            $form->text('open');
            $form->text('high');
            $form->text('low');
            $form->text('close');
            $form->text('volume');
            $form->text('previous_close');
            $form->text('change');
            $form->text('percent_change');
            $form->text('average_volume');
            $form->text('state');
            $form->text('is_hot');
            $form->text('weight');
            $form->text('other_id');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
