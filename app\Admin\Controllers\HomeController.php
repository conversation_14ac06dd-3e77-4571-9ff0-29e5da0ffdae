<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Admin\Metrics\Home;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Widgets\Card;


class HomeController extends Controller
{

    public function index(Content $content)
    {
        return $content
            ->header('Dashboard')
            ->description('')
            ->body(function (Row $row) {
                $row->column(4, function (Column $column) {
                    $column->row(new Home\TotalUsers());
                });
                $row->column(8, function (Column $column) {
                    $column->row(new Home\TodayEurRechargeWithdraw());
                    $column->row(new Home\TodayUsdRechargeWithdraw());
                });
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(6, new Home\AllEurRechargeWithdraw());
                        $row->column(6, new Home\AllUsdRechargeWithdraw());
                    });
                });
                $row->column(12, function (Column $column) {
                    $column->row(function (Row $row) {
                        $row->column(12, Card::make('会员注册', new Home\Chart\User7Day()));

                    });
                });
            });
    }

//    public function index(Content $content)
//    {
//        return $content
//            ->header('Dashboard')
//            ->description('Description...')
//            ->body(function (Row $row) {
//                $row->column(6, function (Column $column) {
//                    $column->row(Dashboard::title());
//                    $column->row(new Examples\Tickets());
//                });
//
//                $row->column(6, function (Column $column) {
//                    $column->row(function (Row $row) {
//                        $row->column(6, new Examples\NewUsers());
//                        $row->column(6, new Examples\NewDevices());
//                    });
//
//                    $column->row(new Examples\Sessions());
//                    $column->row(new Examples\ProductOrders());
//                });
//            });
//    }
}
