<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\BigTrade\Check;
use App\Admin\Renderable\BigTradeProductTable;
use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\BigTradeOrder;
use App\Models\BigTrade;
use App\Models\Product;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class BigTradeOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new BigTradeOrder(), function (Grid $grid) {

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user', 'product']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('order_sn');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('product.name', '股票名称');
            $grid->column('product.symbol', '股票代码');
            $grid->column('buy_in_price');
            $grid->column('buy_in_num');
            $grid->column('buy_in_market_val');
            $grid->column('buy_in_fee_rate')->display(function ($buy_in_fee_rate) {
                return "<span style='color: red'>{$buy_in_fee_rate}%</span>";
            });
            $grid->column('buy_in_fee');
            $grid->column('stamp_duty', '印花税');
            $grid->column('exchange_rate', '货币兑换汇率(买入时)');
            $grid->column('user_spread', '用户汇率价差');
            $grid->column('success_num');
            $grid->column('success_fee');
            $grid->column('success_in_time');
            $grid->column('success_market_val');
            $grid->column('state')->using([0 => '待审核', 1 => '审核通过', 2 => '审核拒绝'])->dot(
                [
                    1 => 'success',
                    2 => 'danger',
                ],
                'primary' // 第二个参数为默认值
            );
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();
                $filter->like('user.email', '用户');
                $filter->like('order_sn');
                $filter->like('product.name', '产品名称');
                $filter->like('product.symbol', '产品名代码');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();
                $actions->append(new Check());
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new BigTradeOrder(), function (Show $show) {
            $show->field('id');
            $show->field('order_sn');
            $show->field('user_id');
            $show->field('product_id');
            $show->field('buy_in_price');
            $show->field('buy_in_num');
            $show->field('buy_in_market_val');
            $show->field('buy_in_fee_rate');
            $show->field('buy_in_fee');
            $show->field('success_num');
            $show->field('success_fee');
            $show->field('success_in_time');
            $show->field('success_market_val');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new BigTradeOrder(), function (Form $form) {
            $form->display('id');
//            $form->text('order_sn');
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()->rules('required');
            $form->selectTable('big_trade_id', '大宗交易股票')
                ->from(BigTradeProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(BigTrade::class, 'id', 'product.name')->required(); // 设置编辑数据显示
//            $form->text('buy_in_price')->readonly();
            $form->number('buy_in_num')->required()->rules('required|integer|min:1');
//            $form->text('buy_in_market_val')->readonly();
//            $form->text('buy_in_fee')->readonly();
//            $form->text('success_num')->readonly();
//            $form->text('success_fee')->readonly();
//            $form->text('success_in_time')->readonly();
//            $form->text('success_market_val')->readonly();
            $form->radio('state', '操作')->options(['1' => '通过'])
                ->default('1');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
