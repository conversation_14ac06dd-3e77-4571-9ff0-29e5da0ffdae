<?php

namespace App\Admin\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\BigTrade;

class BigTradeProductTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new BigTrade(), function (Grid $grid) {

            $model = $grid->model()->with('product')->where('state', 1);

            $model->orderBy('id', 'desc');

            $grid->column('id');
            $grid->column('product.name', '名称');
            $grid->column('product.symbol', '代码');

            $grid->quickSearch(['id', 'product.name', 'product.symbol']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->where('name', function ($query) {
                    $query->whereHas('product', function ($q) {
                        $q->where('name', 'like', "%{$this->input}%");
                    });
                }, '产品名称')->width(4);

                $filter->where('symbol', function ($query) {
                    $query->whereHas('product', function ($q) {
                        $q->where('symbol', 'like', "%{$this->input}%");
                    });
                }, '产品代码')->width(4);
            });
        });
    }
}
