<?php

namespace App\Admin\Actions\Grid\IpoOrder;

use App\Admin\Actions\Grid\User\Authenticatable;
use App\Admin\Actions\Grid\User\HasPermissions;
use App\Admin\Actions\Grid\User\Model;
use App\Admin\Forms\IpoOrder\CheckForm;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Modal;

class Check extends RowAction
{
    /**
     * @return string
     */
    protected $title = '审核';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Modal
     */
    public function render()
    {

        $form = CheckForm::make()->payload(['id' => $this->getKey()]);

        return Modal::make()
            ->lg()
            ->title('审核')
            ->body($form)
            ->button('<a class="btn btn-sm btn-info" style="color: #f4f4f4">审核</a>');

    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        // return ['你确定要删除此行内容吗？', '弹窗内容'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }


}
