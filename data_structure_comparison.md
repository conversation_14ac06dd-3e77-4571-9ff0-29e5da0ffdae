# 📊 订单列表API数据结构对比

## 🔍 **修改前的数据结构**

### 原始返回格式（简化版）
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "orders": [
            {
                "id": 123,
                "amount": 1000.00,           // ❌ 原字段
                "price": 100.50,             // ❌ 原字段  
                "status": 1,
                "buy_time": 1701590400,
                "created_at": "2024-12-03T10:00:00Z",
                "order_type": "self",
                "product_id": 456,
                "trade_id": 0,
                
                // 显示信息
                "display_name": "苹果公司",
                "display_symbol": "AAPL", 
                "display_icon": "/images/aapl.png",
                "display_type": "product",
                
                // 产品信息（简化）
                "product_info": {
                    "id": 456,
                    "name": "苹果公司",
                    "symbol": "AAPL",
                    "icon": "/images/aapl.png"
                }
            }
        ],
        "total": 45,                         // ❌ 简化统计
        "self_count": 30,
        "follow_count": 15
    }
}
```

## 🚀 **修改后的数据结构**

### 新的完整返回格式
```json
{
    "code": 200,
    "message": "success", 
    "data": {
        "orders": [
            {
                "id": 123,
                "order_sn": "ORD20241203001",    // ✅ 新增：订单号
                "order_type": "self",
                "trade_id": 0,
                "product_id": 456,
                
                // ✅ 新增：完整的基础交易信息
                "direction_data": 1,
                "direction_text": "买涨",
                "buy_type": 1,
                "buy_type_text": "市价",
                "buy_price": 100.50,
                "buy_quantity": 10,
                "buy_cost": 1005.00,
                "buy_fees": 5.03,
                "lever": 1,
                "status": 1,
                "status_text": "持仓中",
                "currency": "eur",
                "source": 1,
                
                // ✅ 新增：实时盈亏信息
                "current_price": 102.30,         // 实时价格
                "profit_amount": 18.00,          // 实时盈亏
                "profit_percentage": 1.79,       // 盈亏百分比
                "market_cap": 1023.00,           // 市值
                
                // ✅ 新增：止盈止损信息
                "take_profit": 50.00,
                "stop_loss": 20.00,
                "auto_close_info": {             // 自动平仓状态
                    "can_take_profit": false,
                    "can_stop_loss": false,
                    "take_profit_distance": 32.00,
                    "stop_loss_distance": -38.00,
                    "risk_level": "normal"
                },
                
                // ✅ 新增：完整的平仓信息
                "sell_price": 0,
                "sell_quantity": 0,
                "sell_money": 0,
                "sell_fees": 0,
                "see_fee": 0,
                "sell_type": 0,
                
                // ✅ 新增：标识字段
                "is_lock": 0,
                "is_open_buy": 0,
                "is_open_sell": 0,
                
                // 时间信息
                "buy_time": 1701590400,
                "sell_time": null,
                "created_at": "2024-12-03T10:00:00Z",
                
                // 显示信息（保持）
                "display_type": "product",
                "display_name": "苹果公司",
                "display_symbol": "AAPL",
                "display_icon": "/images/aapl.png",
                
                // ✅ 增强：产品信息
                "product_info": {
                    "id": 456,
                    "name": "苹果公司",
                    "symbol": "AAPL",
                    "icon": "/images/aapl.png",
                    "currency": "usd",           // 新增
                    "pro_type": 1,               // 新增
                    "country": "us",             // 新增
                    "other_id": "12345"          // 新增
                }
            }
        ],
        // ✅ 增强：统计信息
        "statistics": {
            "total_count": 45,
            "self_count": 30,
            "follow_count": 15
        },
        "server_time": "2024-12-03T12:30:45Z"    // ✅ 新增：服务器时间
    }
}
```

## 📈 **主要变化对比**

### ✅ **新增字段**

| 分类 | 新增字段 | 说明 |
|------|----------|------|
| **订单基础** | `order_sn` | 订单号 |
| | `direction_data/direction_text` | 买涨/买跌方向 |
| | `buy_type/buy_type_text` | 市价/限价类型 |
| | `buy_price/buy_quantity/buy_cost` | 详细买入信息 |
| | `buy_fees/lever/source` | 手续费、杠杆、来源 |
| | `status_text` | 状态文本描述 |
| **实时数据** | `current_price` | 实时价格 |
| | `profit_amount` | 实时盈亏 |
| | `profit_percentage` | 盈亏百分比 |
| | `market_cap` | 市值 |
| **止盈止损** | `take_profit/stop_loss` | 止盈止损设置 |
| | `auto_close_info` | 自动平仓状态对象 |
| **平仓信息** | `sell_*` 系列字段 | 完整平仓数据 |
| **标识字段** | `is_lock/is_open_*` | 订单状态标识 |
| **产品增强** | `currency/pro_type/country/other_id` | 产品详细信息 |
| **系统信息** | `server_time` | 服务器时间戳 |

### ❌ **移除字段**

| 原字段 | 说明 | 替代方案 |
|--------|------|----------|
| `amount` | 模糊的金额字段 | 使用`buy_cost`(买入成本)和`market_cap`(市值) |
| `price` | 模糊的价格字段 | 使用`buy_price`(买入价)和`current_price`(现价) |
| `total` | 简单计数 | 使用`statistics.total_count` |
| `pagination` | 分页信息 | 移除分页，返回所有数据 |

### 🔄 **字段重构**

| 原字段 | 新字段 | 变化说明 |
|--------|--------|----------|
| `data.total` | `data.statistics.total_count` | 统计信息结构化 |
| `data.self_count` | `data.statistics.self_count` | 移入statistics对象 |
| `data.follow_count` | `data.statistics.follow_count` | 移入statistics对象 |

## 🎯 **核心改进**

### 1. **数据完整性**
- ✅ 从简化的4个字段扩展到40+个完整字段
- ✅ 涵盖订单全生命周期的所有关键信息

### 2. **实时性**
- ✅ 实时价格更新（`current_price`）
- ✅ 实时盈亏计算（`profit_amount`）
- ✅ 实时止盈止损状态（`auto_close_info`）

### 3. **业务逻辑**
- ✅ 与AutoOrderDeal完全一致的盈亏计算
- ✅ 完整的止盈止损逻辑
- ✅ 风险等级评估

### 4. **前端友好**
- ✅ 提供文本描述字段（`*_text`）
- ✅ 结构化的显示信息
- ✅ 服务器时间同步

## 🚨 **前端适配建议**

### 必须修改的字段映射
```javascript
// 原字段 → 新字段
order.amount → order.buy_cost (买入成本) 或 order.market_cap (当前市值)
order.price → order.buy_price (买入价) 或 order.current_price (现价)
data.total → data.statistics.total_count
```

### 新增功能可选使用
```javascript
// 实时盈亏显示
order.profit_amount + order.profit_percentage

// 止盈止损状态
order.auto_close_info.risk_level

// 详细交易信息
order.direction_text + order.buy_type_text
```
