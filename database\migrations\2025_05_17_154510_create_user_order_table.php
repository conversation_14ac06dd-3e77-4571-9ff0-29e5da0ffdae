<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_order', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->unsignedInteger('user_id')->default(0)->index()->comment('用户id');
            $table->integer('trade_id')->default(0)->index()->comment('交易员id，0=自主下单或者后台建仓，非0=跟单交易员id');
            $table->unsignedInteger('product_type')->default(1)->index()->comment('产品类型 1=股票 2=...');
            $table->unsignedInteger('product_id')->default(1)->index()->comment('产品id');

            $table->string('order_sn', 50)->default('')->index()->comment('订单号');
            $table->tinyInteger('direction_data')->default(1)->comment('买入方向，1：买涨 2：买跌');
            $table->tinyInteger('buy_type')->default(1)->comment('买入类型:1=市价,2=限价');
            $table->unsignedDecimal('buy_price', 15, 3)->default(0.00)->comment('买入价格');
            $table->decimal('buy_quantity',15, 3)->default(1.000)->comment('买入数量 股数');
            $table->unsignedDecimal('buy_cost', 15, 3)->default(0.00)->comment('买入成本');
            $table->integer('lever')->default(1)->comment('买入杠杆');
            $table->tinyInteger('status')->default(1)->index()->comment('订单状态:1=持仓中,2=已平仓,3=平仓中,4=挂单中,5=已撤单');
            $table->unsignedDecimal('buy_fees', 15, 3)->default(0.00)->comment('买入手续费');
            $table->unsignedDecimal('buy_fees_rate', 15, 3)->default(0.00)->comment('买入手续费费率');
            $table->unsignedInteger('buy_time')->default(0)->comment('实际买入成交时间');

            $table->unsignedDecimal('market_cap', 15, 3)->default(0.00)->comment('当前市值');

            $table->unsignedDecimal('margin_call', 15, 3)->default(0.00)->comment('追加保证金');
            $table->unsignedDecimal('overnight_fee', 15, 3)->default(0.00)->comment('隔夜费');

            $table->decimal('profit_amount', 15, 3)->default(0.00)->comment('盈亏金额');
            $table->unsignedDecimal('take_profit', 15, 3)->default(0.00)->comment('止盈金额');
            $table->unsignedDecimal('stop_loss', 15, 3)->default(0.00)->comment('止损金额');

            $table->tinyInteger('sell_type')->default(0)->comment('卖出类型:1=主动卖出,2=强制平仓');
            $table->unsignedDecimal('sell_price', 15, 3)->default(0.00)->comment('卖出价格');
            $table->unsignedDecimal('sell_quantity', 15, 3)->default(0.00)->comment('卖出数量');
            $table->unsignedDecimal('sell_money', 15, 3)->default(0.00)->comment('卖出金额');
            $table->unsignedDecimal('sell_fees', 15, 3)->default(0.00)->comment('卖出手续费');
            $table->unsignedDecimal('see_fee', 15, 3)->default(0.00)->comment('证交税');

            $table->unsignedDecimal('stamp_duty', 15, 3)->default(0.00)->comment('西班牙印花税');
            $table->unsignedDecimal('exchange_rate', 15, 5)->default(0.00)->comment('货币兑换汇率(买入时)');
            $table->unsignedDecimal('user_spread', 15, 5)->default(0.00000)->comment('用户汇率价差');
            $table->unsignedDecimal('sell_exchange_rate', 15, 5)->default(0.00)->comment('货币兑换汇率(卖出时)');

            $table->unsignedInteger('sell_time')->default(0)->comment('实际卖出成交时间');

            $table->tinyInteger('is_lock')->default(0)->comment('是否锁定:1=不锁定,2=锁定');
            $table->tinyInteger('is_open_buy')->default(0)->comment('是否开盘买入 0=否 1=是');
            $table->tinyInteger('is_open_sell')->default(0)->comment('是否开盘卖出 0=否 1=是');
            $table->tinyInteger('source')->default(1)->comment('来源：1：普通买入,2:新股，3：日内交易，4：大宗交易，5：红利');
            $table->char('currency', 10)->default('eur')->comment('使用的是那种货币入：usd,eur等');

            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}user_order auto_increment=1 comment '用户持仓表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_order');
    }
}
