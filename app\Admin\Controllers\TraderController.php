<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\Trader\CreateOrder;
use App\Admin\Repositories\Trader;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;

class TraderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Trader(), function (Grid $grid) {
            $grid->model()
                ->where('admin_id', Admin::user()->id)
                ->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('nickname');
            $grid->column('avatar')->image('', 100, 100);
            $grid->column('en_introduction','交易员简介（英语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->en_introduction);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('es_introduction','交易员简介（西班牙语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->es_introduction);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('de_introduction','交易员简介（德语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->de_introduction);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('level')->using([1 => '明星', 2 => '精英', 3 => '冠军']);
            $grid->column('follower');
            $grid->column('fans');
            $grid->column('weight');

            $grid->column('state')->switch();
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
//                $actions->disableDelete(); // 禁用系统的删除功能，走自己的删除，只有持仓中或挂单中，才能删除订单
                // append一个操作
                $actions->append(new CreateOrder()); // 强制平仓

            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Trader(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('nickname');
            $show->field('avatar')->image();
            $show->field('en_introduction','交易员简介（英语）');
            $show->field('es_introduction','交易员简介（西班牙语）');
            $show->field('de_introduction','交易员简介（德语）');
            $show->field('level');
            $show->field('weight');
            $show->field('follower');
            $show->field('fans');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Trader(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required()->rules('required', ['required' => '请输入名字']);
            $form->text('nickname')->required()->rules('required', ['required' => '请输入名字']);
            $form->image('avatar', '头像')->required()
                ->autoUpload()
                ->rules('required|max:6000', ['required' => '请上传头像']);

            $form->editor('en_introduction','交易员简介（英语）')->required()->rules('required', ['required' => '请输入简介（英文）']);
            $form->editor('es_introduction','交易员简介（西班牙语）')->required()->rules('required', ['required' => '请输入简介（西班牙语）']);
            $form->editor('de_introduction','交易员简介（德语）')->required()->rules('required', ['required' => '请输入简介（德语）']);
            $form->select('level')->options([1 => '明星', 2 => '精英', 3 => '冠军'])->required()->rules('required', ['required' => '请输入简介']);
            $form->number('follower')->rules('required', ['required' => '请输入跟随着'])->default(0);
            $form->number('fans')->rules('required', ['required' => '请输入粉丝数'])->default(0);
            $form->number('weight')->rules('required', ['required' => '请输入权重'])->default(0);

            $form->switch('state');

            $form->display('created_at');
            $form->display('updated_at');
            $form->hidden('admin_id')->default(Admin::user()->id);
        });
    }
}
