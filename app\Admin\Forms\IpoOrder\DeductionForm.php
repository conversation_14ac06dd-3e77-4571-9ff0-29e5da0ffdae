<?php

namespace App\Admin\Forms\IpoOrder;

use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\MoneyChange;
use App\Models\UserMessage;
use App\Models\Users;
use App\Models\UsersInfo;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;


class DeductionForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $koukuangushu = $input['koukuangushu'];


        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if ($koukuangushu && !is_numeric($koukuangushu)) {
            return $this->response()->error('扣款股数只能是数字');
        }


        DB::beginTransaction();
        try {
            $ipoOrder = IpoOrder::where('id', $id)->lockForUpdate()->firstOrFail();
            $ipo = Ipo::query()->where('id', $ipoOrder->ipo_id)->firstOrFail();
            if (!$ipoOrder) {
                throw  new \Exception('数据不存在');
            }
            if ($ipoOrder->state != 1) {
                throw  new \Exception('当前申购订单未设置中签，不能扣费');
            }
            if ($ipoOrder->pay_state == 1) {
                throw  new \Exception('当前申购订单已支付，无需重复扣费');
            }
            if ($ipoOrder->check_state != 0) {
                throw  new \Exception('当前申购订单已审核通过，无法扣费');
            }

            $userInfo = UsersInfo::where('user_id', $ipoOrder->user_id)->lockForUpdate()->firstOrFail();

            $balance = 'eur_balance';
            $dongjie = 'eur_dongjie';

            $before = $userInfo->$balance;
            $rate = $ipoOrder->buy_in_fee_rate;
            // 有提交扣款股数，按提交的来
            if ($koukuangushu) {
                $buy_price = $ipoOrder->buy_price;
                if (!in_array($ipo->country, ['de', 'sp'])) {
                    // 换算金额和费用
                    $buy_price = calculateEuroCost($ipoOrder->buy_price, $ipoOrder->exchange_rate, $ipoOrder->user_spread);
                }
                if ($koukuangushu > ($ipoOrder->zhongqianshu - $ipoOrder->renjiaonum)) {
                    throw  new \Exception('扣款股数不能超出应认缴股数');
                }
                $total = $koukuangushu * $buy_price;
                $buyFees = buy_commission($total, $rate); // 手續費
                $allTotal = $total + $buyFees;

                if ($userInfo->$balance < $allTotal) {
                    throw  new \Exception('当前用户余额不足扣除' . $koukuangushu . '股的申购费用，无法扣费');
                }

                $renjiaonum = $koukuangushu;
            } else {
                $buy_price = $ipoOrder->buy_price;
                if (!in_array($ipo->country, ['de', 'sp'])) {
                    // 换算金额和费用
                    $buy_price = calculateEuroCost($ipoOrder->buy_price, $ipoOrder->exchange_rate, $ipoOrder->user_spread);
                }

                // 计算用户的余额能够扣除的股票张数
                $oneFee = buy_commission($buy_price, $rate);
                $num = floor($userInfo->$balance / ($buy_price + $oneFee));
                if ($num <= 0) {
                    throw  new \Exception('当前申购用户余额不足扣除1股的申购费用，无法扣费');
                }
                // 计算剩余未支付的股数 zhongqianshu是股数 renjiaonum是张数
                $syNum = $ipoOrder->zhongqianshu - $ipoOrder->renjiaonum;

                // 用户余额能够全额支付剩余张数的情况
                if ($num >= $syNum) {
                    $renjiaonum = $syNum;
                    $total = $syNum * $buy_price; // 计算总需支付金额
                    $buyFees = buy_commission($total, $rate); // 手續費
                    $allTotal = $total + $buyFees;
                } else {
                    $renjiaonum = $num;
                    // 用户余额只够支付部分张数
                    $total = $num * $buy_price;
                    $buyFees = buy_commission($total, $rate); // 手續費
                    $allTotal = $total + $buyFees;
                }
            }

            $xurenjiao = $ipoOrder->xurenjiao - $allTotal;
            $payState = (int)$xurenjiao > 0 ? 2 : 1; // 2:支付中，1：已支付

            $ipoOrder->renjiaonum += $renjiaonum;
            $ipoOrder->yirenjiao += $allTotal;
            $ipoOrder->xurenjiao -= $allTotal;
            $ipoOrder->renjiaocishu += 1;
            $ipoOrder->pay_state = $payState;
            $ipoOrder->save();

            // 扣用户金额
            $userInfo->$balance -= round($allTotal, 3);
            $userInfo->$dongjie += $total;

            $userInfo->save();

            // 添加资金流水
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $ipoOrder->user_id,
                'order_sn' => $ipoOrder->order_sn,
                'product_name' => $ipoOrder->name,
                'detailed' => "股票申购|代码|{$ipo->symbol}|名称|{$ipo->name}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => -$total,
                'after_money' => $before - $total,
            ]);

            if ($buyFees > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => "EUR",
                    'user_id' => $ipoOrder->user_id,
                    'order_sn' => $ipoOrder->order_sn,
                    'product_name' => $ipo->name,
                    'detailed' => "股票申购手续费|代码|{$ipo->symbol}|名称|{$ipo->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE,
                    'before_money' => $before - $total,
                    'money' => -$buyFees,
                    'after_money' => $before - $total - $buyFees,
                ]);
            }

            // 给用户发送信息
            if ($payState) {
                $message = [
                    'user_id' => $ipoOrder->user_id,
                    'content' => "尊敬的会员|,|您申购的新股|{$ipo->name}|代码|{$ipo->symbol}|已中签|,|中签数量是|:|{$ipoOrder->zhongqianshu}|股|,|已经扣除对应款项|,|新股上市后即可正常交易|!"
                ];
            } else {
                $message = [
                    'user_id' => $ipoOrder->user_id,
                    'content' => "尊敬的会员|,|您申购的新股|{$ipo->name}|代码|{$ipo->symbol}|已中签|,|中签数量是|:|{$ipoOrder->zhongqianshu}|股|,|但您的账户余额不足，已经扣除部分款项|:|{$totalCost}|,|还需要|:|{$xurenjiao}|,|请尽快存款补足应缴款|!"
                ];
            }
            UserMessage::create($message);

            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');
        $this->text('order_sn', '订单号')->readOnly();
        $this->text('user')->readOnly();
        $this->text('name', '股票名称')->readOnly();
        $this->text('buy_price')->readOnly();
        $this->text('zhongqianshu', '中签股数')->readOnly();
        $this->text('yingrenjiao', '应认缴金额')->readOnly();
        $this->text('renjiaonum', '已认缴股数')->readOnly();
        $this->text('yirenjiao', '已认缴金额')->readOnly();
        $this->text('xurenjiao', '需认缴金额')->readOnly();

        $this->text('koukuangushu', '扣款股数')->help('不输入，则根据账户金额可购买股数扣');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        $data = IpoOrder::find($id);

        return [
            'order_sn' => $data->order_sn,
            'user' => $data->user->email,
            'name' => $data->ipo->name,
            'buy_price' => $data->buy_price,
            'zhongqianshu' => $data->zhongqianshu,
            'yirenjiao' => $data->yirenjiao,
            'yingrenjiao' => $data->yingrenjiao,
            'renjiaonum' => $data->renjiaonum,
            'xurenjiao' => $data->xurenjiao,
//            'success_num' => $data->buy_in_num,
//            'created_at' => $data->created_at,
        ];
    }
}
