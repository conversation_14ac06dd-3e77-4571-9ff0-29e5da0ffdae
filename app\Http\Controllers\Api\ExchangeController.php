<?php

namespace App\Http\Controllers\Api;


use App\Models\AreaConfig;
use App\Models\ExchangeOrder;
use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\Users;
use App\Models\UsersInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * 日内交易
 */
class ExchangeController extends ApiBaseController
{

    /**
     * 获取可转换的货币
     */
    public function getCurrencyList(Request $request)
    {
        $res = AreaConfig::where('state', 1)
            ->orderBy('state', 'desc')->orderBy('weight', 'desc')
            ->pluck('symbol', 'area_code');

        return $this->returnMsg(200, __('api.请求成功'), $res);
    }

    /**
     * 获取转换汇率
     */
    public function getExchangeRate(Request $request)
    {
        $requestData = $request->all();

        $res = ExchangeRate::query()
            ->where('from_currency', $requestData['from'])
            ->where('to_currency', $requestData['to'])
            ->value('rate');

        if (!$res) {
            return $this->returnMsg(504, __('api.汇率不存在'), $res);
        }

        return $this->returnMsg(200, __('api.请求成功'), $res);

    }

    /**
     * 转换
     */
    public function submit(Request $request)
    {

        $validatedData = $request->validate([
            'from' => ['required'],
            'to' => ['required'],
            'from_money' => ['required'],
        ]);

        $requestData = $request->all();
        $from = $requestData['from'];
        $to = $requestData['to'];
        $fromMoney = $requestData['from_money'];

        if ($from == $to) {
            return $this->returnMsg(504, __('api.兑换币种和目标币种不能相同'));
        }

        // 获取费率
        $rate = ExchangeRate::query()->where('from_currency', $from)
            ->where('to_currency', $to)
            ->value('rate');
        if (!$rate) {
            return $this->returnMsg(504, __('api.汇率不存在'));
        }

        $userId = $request->user()->id;
        $user = Users::find($userId);
        if ($user->auth_status != 2) {
            return $this->returnMsg(504, __('api.用户未完成实名认证，请先完成认证'));
        }

        if (!is_numeric($fromMoney)) {
            return $this->returnMsg(504, __('api.兑换的金额不能是非数字'));
        }

        if ($fromMoney <= 0) {
            return $this->returnMsg(504, __('api.兑换的金额不能小于等于0'));
        }

        $toMoney = round($fromMoney * $rate, 2); // 兑换到的金额


        DB::beginTransaction();
        try {
            $userInfo = UsersInfo::query()->where('user_id', $user->id)->lockForUpdate()->first();

            $fromBalanceField = strtolower($from) . '_balance';
            $toBalanceField = strtolower($to) . '_balance';

            $beforFromBalanc = $userInfo->$fromBalanceField;
            $beforToBalanc = $userInfo->$toBalanceField;

            if ($beforFromBalanc < $fromMoney) {
                return $this->returnMsg(504, __('api.账户金额不足'));
            }

            $userInfo->$fromBalanceField -= $fromMoney; // 扣除资金 兑换出
            $userInfo->$toBalanceField += $toMoney; // 添加资金 兑换到的金额
            $userInfo->save();

            // 生成订单号
            $out_trade_no = generateUniqueOrderNumber();
            // 添加转换记录
            ExchangeOrder::create([
                'order_sn' => $out_trade_no,
                'user_id' => $user->id,
                'from_currency' => $from,
                'to_currency' => $to,
                'from_money' => $fromMoney,
                'rate' => $rate,
                'to_money' => $toMoney,
            ]);

            // 添加流水资金
            // 转换出
            MoneyChange::create([
                'money_type' => $from,
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_name' => '',
                'detailed' => "资金兑换|{$from} to {$to}|出",
                'type' => MoneyChange::TYPE_EXCHANGE_OUT,
                'before_money' => $beforFromBalanc,
                'money' => -$fromMoney,
                'after_money' => $beforFromBalanc - $fromMoney,
            ]);

            // 转换入
            MoneyChange::create([
                'money_type' => $to,
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_name' => '',
                'detailed' => "资金兑换|{$from} to {$to}|进",
                'type' => MoneyChange::TYPE_EXCHANGE_IN,
                'before_money' => $beforToBalanc,
                'money' => $toMoney,
                'after_money' => $beforToBalanc + $toMoney,
            ]);

            DB::commit();
            return $this->returnMsg(200, __('api.提交成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(502, __('api.提交失败') . ":" . $e->getMessage());
        }
    }

    /**
     * 日内交易订单
     */
    public function list(Request $request)
    {
        $user = $request->user();

        $res = ExchangeOrder::query()
            ->where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->select(['id', 'order_sn', 'from_currency', 'to_currency', 'from_money', 'rate', 'to_money', 'created_at'])
            ->get();

        return $this->returnMsg(502, __('api.请求成功'), $res);
    }

}
