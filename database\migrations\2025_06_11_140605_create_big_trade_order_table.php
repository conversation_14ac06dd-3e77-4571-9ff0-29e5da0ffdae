<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBigTradeOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('big_trade_order', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->string('order_sn', 70)->index()->comment('订单号');
            $table->integer('user_id')->index()->comment('userid');
            $table->integer('product_id')->index()->comment('股票id');
            $table->unsignedDecimal('buy_in_price', 15, 3)->comment('申购价格');
            $table->integer('buy_in_num')->comment('申购数量（股）');
            $table->integer('buy_in_market_val')->comment('申购市值');
            $table->unsignedDecimal('buy_in_fee', 15, 3)->comment('申购手续费');
            $table->unsignedDecimal('buy_in_fee_rate', 15, 4)->comment('申购手续费费率');
            $table->integer('success_num')->comment('成交数量（股），转持仓的数量');
            $table->unsignedDecimal('success_fee', 15, 3)->comment('转持仓时，需要的手续费');
            $table->timestamp('success_in_time')->nullable()->comment('转持仓的时间,也就是审核通过时间');
            $table->unsignedDecimal('success_market_val', 15, 3)->nullable()->comment('转持仓市值');
            $table->tinyInteger('state')->default(0)->comment('状态:0:待审核，1：审核通过,2:审核拒绝');
            $table->unsignedDecimal('stamp_duty', 15, 3)->default(0.00)->comment('西班牙印花税');
            $table->unsignedDecimal('exchange_rate', 15, 4)->default(0.00)->comment('货币兑换汇率(买入时)');
            $table->unsignedDecimal('user_spread', 15, 5)->default(0.00000)->comment('用户汇率价差');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->index()->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });

        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}big_trade_order auto_increment=1 comment '大宗交易订单订单'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('big_trade_order');
    }
}
