<?php

namespace App\Http\Controllers\Api;


use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\UserMessage;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class IpoController extends ApiBaseController
{
    public function get_message()
    {
        return [
            'ipo_id.required' => __('api.新股ID不能为空'),
            'ipo_id.integer' => __('api.新股ID必须为整数'),
            'ipo_id.exists' => __('api.新股ID不存在或状态不合法'),
            'quantity.required' => __('api.申购数量不能为空'),
            'quantity.integer' => __('api.申购数量必须为整数'),
            'quantity.min' => __('api.申购数量不能小于1'),
        ];
    }

    public function listIpo(Request $request)
    {
        $params = $request->all();
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 10;

        // 获取IPO列表
        $ipoList = Ipo::query()
            ->where('state', 1) // 只获取状态为1的IPO
            ->orderBy('weight', 'desc') // 按日期降序排列
            ->paginate($limit, ['*'], 'page', $page);

        return $this->returnMsg(200, __('api.获取成功'), $ipoList);
    }

    // 申购新股
    public function addOrderIpo(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $rules = [
            'ipo_id' => 'required|integer|exists:ipo,id,state,1',
            'quantity' => 'required|integer|min:1',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        $params = $request->all();

        try {
            $ipo = Ipo::find($params['ipo_id']);

            $now = time();
            $butStart = strtotime($ipo->star_buy_date . '00:00:00');
            $butEnd = strtotime($ipo->end_buy_date . '23:59:59');

            if ($now > $butEnd || $now < $butStart) {
                return $this->returnMsg(500, __('api.不在申购期内'));
            }

            $rate = get_buy_fee_rate($user->id); // 申请的时候就要先绑定费率，后续根据这个费率来操作

            $data = [
                'user_id' => $user->id,
                'ipo_id' => $params['ipo_id'],
                'order_sn' => generateUniqueOrderNumber(), // 生成唯一订单号
                'buy_num' => $params['quantity'],
                'buy_price' => $ipo->price, // 使用IPO的发行价
                'buy_time' => now(), // 申购时间
                'xurenjiao' => 0, //
                'yingrenjiao' => 0, //
                'yirenjiao' => 0, //
                'renjiaocishu' => 0, //
                'renjiaonum' => 0, //
                'buy_in_fee_rate' => $rate, //
            ];
            IpoOrder::query()->create($data);

            // 给用户添加信息
            $now = date('d.m.Y H:i:s');
            $message = [
                'user_id' => $user->id,
                'content' => "尊敬的会员|,|您在|{$now}|,|申购了新股|[|{$ipo->name}|],|代码|:|{$ipo->symbol}|,|中签公布日将通知您中签结果|!"
            ];
            UserMessage::create($message);
            DB::commit();
            return $this->returnMsg(200, __('api.申购成功'));
        } catch (\Exception $e) {
            return $this->returnMsg(500, __('api.申购失败') . $e->getMessage());
        }


    }

    // 申购记录
    public function orderListIpo(Request $request)
    {
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 10;

        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $now = Carbon::now();

        $orders = IpoOrder::query()
            ->with('ipo')
            ->where('user_id', $user->id)
            ->where('is_show', 1)
            ->orderBy('buy_time', 'desc')
            ->paginate($limit, ['*'], 'page', $page);

        // 遍历每一条，抽签未到点的，强制将中奖数置 0
        $orders->getCollection()->transform(function (IpoOrder $order) use ($now) {
            $cutoff = Carbon::parse($order->ipo->chouqian_date . ' 09:00:00');
            if ($now->lt($cutoff)) {
                $order->zhongqianshu = 0;
            }
            return $order;
        });

        return $this->returnMsg(200, __('api.请求成功'), $orders);
    }
}

