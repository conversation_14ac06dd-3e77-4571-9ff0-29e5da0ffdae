<?php

namespace App\Http\Controllers\Api;

use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\Trader;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use App\Services\TwelvedataNewService;
use App\Services\JiangshanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class UserOrderController extends ApiBaseController
{
    // 缓存策略：只缓存静态数据，价格实时获取
    const STATIC_DATA_CACHE_TTL = 300; // 静态数据缓存5分钟
    const BATCH_SIZE = 50;
    public function get_message()
    {
        return [
            'product_id.required' => __('api.产品ID不能为空'),
            'product_id.integer' => __('api.产品ID必须为整数'),
            'product_id.exists' => __('api.产品ID不存在或状态不合法'),
            'direction_data.required' => __('api.买入方向不能为空'),
            'direction_data.integer' => __('api.买入方向必须为整数'),
            'direction_data.in' => __('api.买入方向不合法'),
            'buy_type.required' => __('api.买入类型不能为空'),
            'buy_type.integer' => __('api.买入类型必须为整数'),
            'buy_type.in' => __('api.买入类型不合法'),
            'buy_price.required' => __('api.买入价格不能为空'),
            'buy_price.numeric' => __('api.买入价格必须为数字'),
            'buy_price.min' => __('api.买入价格不能小于0'),
            'buy_quantity.required' => __('api.买入数量不能为空'),
            'buy_quantity.integer' => __('api.买入数量必须为整数'),
            'buy_quantity.min' => __('api.买入数量不能小于1'),
            'lever.required' => __('api.买入杠杆不能为空'),
            'lever.integer' => __('api.买入杠杆必须为整数'),
        ];
    }

    /**
     * 下单
     */
    public function addOrder(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        if ($user->trade_status != 1) return $this->returnMsg(403, __('api.用户交易状态异常，请联系客服'));
        if ($user->lever_status != 1) return $this->returnMsg(403, __('api.用户未开启杠杆交易，请先开启'));
        if ($user->auth_status != 2) return $this->returnMsg(403, __('api.用户未完成实名认证，请先完成认证'));

        $rules = [
            'product_id' => 'required|integer|exists:product,id,deleted_at,NULL,state,1',
            'direction_data' => 'required|integer|in:1,2',
            'buy_type' => 'required|integer|in:1,2',
            'buy_price' => 'required|numeric|min:0',
            'buy_quantity' => 'required|numeric',
            'lever' => 'required|integer|min:1|max:100',
            'take_profit' => 'nullable|numeric|min:0',
            'stop_loss' => 'nullable|numeric|min:0',
        ];
        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        $params = $request->all();

        $product = Product::query()
            ->where('id', $params['product_id'])
            ->firstOrFail();

        if ($product['pro_type'] == 5) {
            if ($params['buy_quantity'] < 0.001){
                return $this->returnMsg(422, __('api.买入数量不能小于0.001'));
            }
        } else {
            if ($params['buy_quantity'] < 1){
                return $this->returnMsg(422, __('api.买入数量不能小于1'));
            }
        }

        try {
            $order = \App\Services\OrderService::buy($user, $params);
            return $this->returnMsg(200,__('api.请求成功'), $order->order_sn);
        } catch (\Exception $e) {
            return $this->returnMsg(422,  $e->getMessage());
        }
    }

    /**
     * 高性能实时订单列表（无分页）
     */
    public function orderList(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        $status = $request->input('status');

        // 1. 获取订单静态数据（可缓存，无分页）
        $staticData = $this->getOrderStaticData($user, $status);

        // 2. 实时获取价格并计算盈亏（不缓存）
        $realTimeData = $this->enrichWithRealTimeData($staticData['orders']);

        // 3. 计算包含实时数据的统计信息
        $enhancedStatistics = $this->calculateEnhancedStatistics($realTimeData);

        return $this->returnMsg(200, __('api.请求成功'), [
            'orders' => $realTimeData,
            'statistics' => $enhancedStatistics,
            'server_time' => now()->toISOString(),
        ]);
    }

    /**
     * 获取交易员给用户做的所有操作详情（实时版本）
     */
    public function getTraderOrders(Request $request, $traderId)
    {
        $user = auth()->user();
        $currency = strtolower($request->get('currency', 'eur'));

        // 获取交易员信息
        $trader = Trader::select('id', 'name', 'nickname', 'avatar', 'en_introduction', 'es_introduction', 'de_introduction', 'level')
            ->find($traderId);

        if (!$trader) {
            return $this->returnMsg(404, __('api.交易员不存在'));
        }

        // 获取该交易员给用户做的所有订单
        $orders = UserOrder::query()
            ->with(['product:id,name,symbol,price,pro_type,country,other_id'])
            ->where('user_id', $user->id)
            ->where('trade_id', $traderId)
            ->where('currency', $currency)
            ->orderByDesc('buy_time')
            ->get();

        // 更新行情
        $this->updateProductPricesForOrders($orders);

        // 格式化订单数据
        $formattedOrders = $orders->map(function($order) {
            $profitLoss = $this->calculateOrderProfit($order);
            $marketCap = $this->calculateOrderMarketCap($order);

            return [
                'id' => $order->id,
                'order_sn' => $order->order_sn,
                'product_id' => $order->product_id,
                'product_name' => $order->product->name ?? __('api.未知产品'),
                'product_symbol' => $order->product->symbol ?? '',
                'current_price' => (float)($order->product->price ?? 0),

                'direction_data' => $order->direction_data,
                'direction_text' => $order->direction_data == 1 ? __('api.买涨') : __('api.买跌'),
                'buy_type' => $order->buy_type,
                'buy_type_text' => $order->buy_type == 1 ? __('api.市价') : __('api.限价'),
                'buy_price' => (float)$order->buy_price,
                'buy_quantity' => (float)$order->buy_quantity,
                'buy_cost' => (float)$order->buy_cost,
                'buy_fees' => (float)$order->buy_fees,
                'lever' => (int)$order->lever,
                'status' => (int)$order->status,
                'status_text' => $this->getStatusText($order->status),
                'source' => (int)$order->source,

                'profit_amount' => $profitLoss,
                'market_cap' => $marketCap,
                'profit_percentage' => $this->calculateProfitPercentage($profitLoss, $order->buy_cost),
                'take_profit' => (float)$order->take_profit,
                'stop_loss' => (float)$order->stop_loss,

                'sell_price' => (float)$order->sell_price,
                'sell_money' => (float)$order->sell_money,
                'sell_fees' => (float)$order->sell_fees,
                'see_fee' => (float)$order->see_fee,

                'buy_time' => $order->buy_time,
                'sell_time' => $order->sell_time,
                'created_at' => $order->created_at,
                'currency' => $order->currency,

                'auto_close_info' => $order->status == 1 ? $this->calculateAutoCloseStatus([
                    'profit_amount' => $profitLoss,
                    'take_profit' => $order->take_profit,
                    'stop_loss' => $order->stop_loss,
                ]) : null,
            ];
        });

        // 统计信息
        $stats = [
            'total_orders' => $formattedOrders->count(),
            'holding_orders' => $formattedOrders->where('status', 1)->count(),
            'closed_orders' => $formattedOrders->where('status', 2)->count(),
            'pending_orders' => $formattedOrders->where('status', 4)->count(),
            'total_cost' => $formattedOrders->sum('buy_cost'),
            'total_profit' => $formattedOrders->sum('profit_amount'),
            'total_market_cap' => $formattedOrders->where('status', 1)->sum('market_cap'),
            'buy_orders' => $formattedOrders->where('direction_data', 1)->count(),
            'sell_orders' => $formattedOrders->where('direction_data', 2)->count(),
            'win_rate' => $this->calculateWinRate($formattedOrders),
        ];

        return $this->returnMsg(200, __('api.请求成功'), [
            'trader_info' => [
                'id' => $trader->id,
                'name' => $trader->name,
                'nickname' => $trader->nickname,
                'avatar' => $trader->avatar,
                'en_introduction' => $trader->en_introduction,
                'es_introduction' => $trader->es_introduction,
                'de_introduction' => $trader->de_introduction,
                'level' => $trader->level,
            ],
            'statistics' => $stats,
            'orders' => $formattedOrders,
        ]);
    }

    /**
     * 订单详情
     */
    public function orderView(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        $orderId = $request->input('order_id');
        if (!$orderId || !is_numeric($orderId)) return $this->returnMsg(422, __('api.订单ID不合法'));

        $order = UserOrder::with([
            'product' => function ($query) {
                $query->select('id', 'name', 'pro_type', 'symbol', 'currency', 'exchange', 'logo', 'is_market_open', 'price');
            },
            'trader' => function ($query) {
                $query->select('id', 'name', 'avatar');
            }
        ])
            ->where('user_id', $user->id)
            ->where('id', $orderId)
            ->first();

        if (!$order) return $this->returnMsg(404, __('api.订单不存在'));

        $product = Product::find($order->product_id);
        if ($product) {
            // 只对持仓中的订单获取实时价格和计算实时数据
            if ($order->status == 1) {
                // 持仓中订单：获取实时价格并计算
                TwelvedataNewService::quoteByOne($product);
                $product->refresh();
                $order->current_price = $product->price;
                $order->profit_amount = $order->direction_data == 1
                    ? round(($product->price - $order->buy_price) * $order->buy_quantity, 2)
                    : round(($order->buy_price - $product->price) * $order->buy_quantity, 2);
                $order->hit_take_profit = ($order->take_profit > 0 && $order->profit_amount >= $order->take_profit) ? 1 : 0;
                $order->hit_stop_loss = ($order->stop_loss > 0 && $order->profit_amount <= -$order->stop_loss) ? 1 : 0;
                $order->market_cap = round($product->price * $order->buy_quantity, 2);
                $order->is_market_open = $product->is_market_open;
            } else {
                // 已平仓订单：使用历史数据，不更新价格
                $order->current_price = $order->sell_price ?: $order->buy_price;
                $order->market_cap = $order->sell_price * $order->buy_quantity;
                $order->is_market_open = $product->is_market_open; // 产品开盘状态可以显示
                // profit_amount 使用数据库中的最终值，不重新计算
            }
        }

        return $this->returnMsg(200, __('api.请求成功'), $order);
    }

    /**
     * 主动平仓
     */
    public function orderSell(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        $orderId = $request->input('order_id');
        if (!$orderId || !is_numeric($orderId)) return $this->returnMsg(422, __('api.订单ID不合法'));

        try {
            $order = \App\Services\OrderService::sell($user->id, $orderId, 1);
            return $this->returnMsg(200, __('api.请求成功'), $order->order_sn);
        } catch (\Exception $e) {
            return $this->returnMsg(422,  $e->getMessage());
        }
    }

    /**
     * 撤销订单（只允许挂单/持仓）
     */
    public function orderCancel(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        $orderId = $request->input('order_id');
        if (!$orderId || !is_numeric($orderId)) return $this->returnMsg(422, __('api.订单ID不合法'));

        try {
            $order = \App\Services\OrderService::cancel($user->id, $orderId);
            return $this->returnMsg(200, __('api.请求成功'), $order->order_sn);
        } catch (\Exception $e) {
            return $this->returnMsg(422,  $e->getMessage());
        }
    }

    /**
     * 获取订单静态数据（可缓存的部分，无分页）
     */
    private function getOrderStaticData($user, $status)
    {
        // 优化查询：只查询必要的静态字段
        $baseQuery = UserOrder::query()
            ->select([
                'id', 'order_sn', 'trade_id', 'product_id', 'direction_data', 'buy_type',
                'buy_price', 'buy_quantity', 'buy_cost', 'buy_fees', 'lever', 'status',
                'currency', 'source', 'take_profit', 'stop_loss', 'sell_price',
                'sell_quantity', 'sell_money', 'sell_fees', 'see_fee', 'sell_type',
                'profit_amount', 'buy_time', 'sell_time', 'created_at',
                'is_lock', 'is_open_buy', 'is_open_sell'
            ])
            ->where('user_id', $user->id)
            ->when($status, function ($query) use ($status) {
                return $query->where('status', $status);
            });

        // 分别查询自主和跟单订单
        $selfOrders = (clone $baseQuery)
            ->where('trade_id', 0)
            ->with(['product:id,name,symbol,currency,pro_type,country,other_id'])
            ->orderByDesc('buy_time')
            ->get();

        // 格式化静态数据
        $formattedSelfOrders = $selfOrders->map(function ($order) {
            return $this->formatStaticOrderData($order);
        });

        return [
            'orders' => $formattedSelfOrders,
        ];
    }

    /**
     * 实时获取价格并计算盈亏
     */
    private function enrichWithRealTimeData($orders)
    {
        if (empty($orders)) {
            return $orders;
        }

        $orders = collect($orders);

        // 获取所有需要实时价格的产品ID（只包含持仓中的订单）
        $holdingOrders = $orders->whereIn('status', [1, 4]);
        $productIds = $holdingOrders->pluck('product_id')->unique()->values()->all();

        if (empty($productIds)) {
            return $orders->toArray();
        }

        // 实时获取最新价格（与AutoOrderDeal逻辑一致）
        $realTimePrices = $this->getRealTimePrices($productIds);

        // 更新持仓订单的实时数据
        return $orders->map(function($order) use ($realTimePrices) {
            if ($order['status'] == 1 || $order['status'] == 4) {
                // 持仓中的订单需要实时计算
                $productId = $order['product_id'];
                $currentPrice = $realTimePrices[$productId] ?? $order['buy_price'];

                // 更新实时价格
                $order['current_price'] = $currentPrice;

                // 实时计算盈亏（与AutoOrderDeal第81-83行逻辑一致）
                $order['profit_amount'] = $this->calculateRealTimeProfit(
                    $order['direction_data'],
                    $order['buy_price'],
                    $currentPrice,
                    $order['buy_quantity']
                );

                // 实时计算市值
                $order['market_cap'] = round($currentPrice * $order['buy_quantity'], 2);

                // 检查止盈止损并自动平仓
                $shouldClose = $this->checkAndAutoClose($order, $currentPrice);
                if ($shouldClose) {
                    // 订单已被自动平仓，重新获取订单数据
                    $updatedOrder = UserOrder::find($order['id']);
                    if ($updatedOrder && $updatedOrder->status == 2) {
                        // 转换为已平仓订单格式
                        $order['status'] = 2;
                        $order['status_text'] = '已平仓';
                        $order['current_price'] = $updatedOrder->sell_price;
                        $order['market_cap'] = $updatedOrder->sell_money;
                        $order['profit_amount'] = $updatedOrder->profit_amount;
                        $order['sell_price'] = $updatedOrder->sell_price;
                        $order['sell_money'] = $updatedOrder->sell_money;
                        $order['sell_time'] = $updatedOrder->sell_time;
                        $order['auto_close_info'] = null;
                    }
                } else {
                    // 实时计算止盈止损状态
                    $order['auto_close_info'] = $this->calculateAutoCloseStatus($order);
                }

                // 计算盈亏百分比
                $order['profit_percentage'] = $this->calculateProfitPercentage(
                    $order['profit_amount'],
                    $order['buy_cost']
                );

            } else {
                // 已平仓订单使用历史数据
                $order['current_price'] = $order['sell_price'] ?: $order['buy_price'];
                $order['market_cap'] = $order['sell_money'] ?: $order['buy_cost'];
                $order['auto_close_info'] = null;
                $order['profit_percentage'] = $this->calculateProfitPercentage(
                    $order['profit_amount'],
                    $order['buy_cost']
                );
            }

            return $order;
        })->toArray();
    }

    /**
     * 实时获取股票价格（基于AutoOrderDeal的正确逻辑）
     */
    private function getRealTimePrices($productIds)
    {
        $prices = [];

        // 获取产品信息
        $products = Product::whereIn('id', $productIds)
            ->select('id', 'pro_type', 'country', 'other_id', 'price')
            ->get();

        // 按产品类型分组处理（修正AutoOrderDeal的错误逻辑）
        $stockProducts = $products->where('pro_type', 1);
        $otherProducts = $products->where('pro_type', '!=', 1);

        // 处理股票产品（pro_type = 1）
        if ($stockProducts->isNotEmpty()) {
            try {
                // 按国家分组处理股票（因为不同国家使用不同的API）
                $stocksByCountry = $stockProducts->groupBy('country');

                foreach ($stocksByCountry as $country => $countryProducts) {
                    $jiangshan = new JiangshanService();
                    // 调用批量更新方法，会直接更新数据库的price字段
                    $jiangshan->get_new_data_serices_list($countryProducts, $country);
                }

                // 重新从数据库获取更新后的价格
                $updatedStockProducts = Product::whereIn('id', $stockProducts->pluck('id'))
                    ->select('id', 'price')
                    ->get();

                foreach ($updatedStockProducts as $product) {
                    $prices[$product->id] = (float)$product->price;
                }

            } catch (\Exception $e) {
                \Log::error('Stock price update failed: ' . $e->getMessage());
                // 降级到数据库现有价格
                foreach ($stockProducts as $product) {
                    $prices[$product->id] = (float)($product->price ?? 0);
                }
            }
        }

        // 处理其他产品（外汇、加密货币等）
        if ($otherProducts->isNotEmpty()) {
            try {
                // 批量更新其他产品价格
//                TwelvedataNewService::quoteByBatch($otherProducts);

                // 重新从数据库获取更新后的价格
                $updatedOtherProducts = Product::whereIn('id', $otherProducts->pluck('id'))
                    ->select('id', 'price')
                    ->get();

                foreach ($updatedOtherProducts as $product) {
                    $prices[$product->id] = (float)$product->price;
                }

            } catch (\Exception $e) {
                \Log::error('Other products price update failed: ' . $e->getMessage());
                // 降级到数据库现有价格
                foreach ($otherProducts as $product) {
                    $prices[$product->id] = (float)($product->price ?? 0);
                }
            }
        }

        return $prices;
    }

    /**
     * 实时计算盈亏（与AutoOrderDeal第81-83行逻辑完全一致）
     */
    private function calculateRealTimeProfit($direction, $buyPrice, $currentPrice, $quantity)
    {
        if ($direction == 1) {
            // 买涨：盈亏 = (现价 - 买入价) * 数量
            return round(($currentPrice - $buyPrice) * $quantity, 2);
        } else {
            // 买跌：盈亏 = (买入价 - 现价) * 数量
            return round(($buyPrice - $currentPrice) * $quantity, 2);
        }
    }

    /**
     * 计算盈亏百分比
     */
    private function calculateProfitPercentage($profitAmount, $buyCost)
    {
        if ($buyCost <= 0) return 0;
        return round(($profitAmount / $buyCost) * 100, 2);
    }

    /**
     * 计算止盈止损状态（基于产品价格）
     */
    private function calculateAutoCloseStatus($order)
    {
        $currentPrice = $order['current_price'] ?? $order['buy_price'];
        $buyPrice = $order['buy_price'];
        $takeProfitPrice = $order['take_profit'];
        $stopLossPrice = $order['stop_loss'];
        $direction = $order['direction_data']; // 1=买涨, 2=买跌

        $info = [
            'can_take_profit' => false,
            'can_stop_loss' => false,
            'take_profit_price' => $takeProfitPrice,
            'stop_loss_price' => $stopLossPrice,
            'price_distance_to_take_profit' => 0,
            'price_distance_to_stop_loss' => 0,
            'risk_level' => 'normal',
        ];

        // 买涨订单的止盈止损逻辑
        if ($direction == 1) {
            // 买涨：止盈价格应该高于买入价，止损价格应该低于买入价
            if ($takeProfitPrice > 0 && $takeProfitPrice > $buyPrice) {
                $info['can_take_profit'] = $currentPrice >= $takeProfitPrice;
                $info['price_distance_to_take_profit'] = round($takeProfitPrice - $currentPrice, 4);
            }

            if ($stopLossPrice > 0 && $stopLossPrice < $buyPrice) {
                $info['can_stop_loss'] = $currentPrice <= $stopLossPrice;
                $info['price_distance_to_stop_loss'] = round($currentPrice - $stopLossPrice, 4);

                // 风险等级评估（基于价格距离）
                $priceRange = $buyPrice - $stopLossPrice;
                $currentDistance = $currentPrice - $stopLossPrice;
                if ($priceRange > 0) {
                    $riskRatio = 1 - ($currentDistance / $priceRange);
                    if ($riskRatio >= 0.8) {
                        $info['risk_level'] = 'danger';
                    } elseif ($riskRatio >= 0.5) {
                        $info['risk_level'] = 'warning';
                    }
                }
            }
        }
        // 买跌订单的止盈止损逻辑
        else if ($direction == 2) {
            // 买跌：止盈价格应该低于买入价，止损价格应该高于买入价
            if ($takeProfitPrice > 0 && $takeProfitPrice < $buyPrice) {
                $info['can_take_profit'] = $currentPrice <= $takeProfitPrice;
                $info['price_distance_to_take_profit'] = round($currentPrice - $takeProfitPrice, 4);
            }

            if ($stopLossPrice > 0 && $stopLossPrice > $buyPrice) {
                $info['can_stop_loss'] = $currentPrice >= $stopLossPrice;
                $info['price_distance_to_stop_loss'] = round($stopLossPrice - $currentPrice, 4);

                // 风险等级评估（基于价格距离）
                $priceRange = $stopLossPrice - $buyPrice;
                $currentDistance = $stopLossPrice - $currentPrice;
                if ($priceRange > 0) {
                    $riskRatio = 1 - ($currentDistance / $priceRange);
                    if ($riskRatio >= 0.8) {
                        $info['risk_level'] = 'danger';
                    } elseif ($riskRatio >= 0.5) {
                        $info['risk_level'] = 'warning';
                    }
                }
            }
        }

        return $info;
    }

    /**
     * 格式化静态订单数据
     */
    private function formatStaticOrderData($order)
    {
        $baseData = [
            'id' => $order->id,
            'order_sn' => $order->order_sn,
            'trade_id' => $order->trade_id,
            'product_id' => $order->product_id,

            // 基础交易信息（静态）
            'direction_data' => $order->direction_data,
            'buy_type' => $order->buy_type,
            'buy_price' => (float)$order->buy_price,
            'buy_quantity' => (float)$order->buy_quantity,
            'buy_cost' => (float)$order->buy_cost,
            'buy_fees' => (float)$order->buy_fees,
            'lever' => (int)$order->lever,
            'status' => (int)$order->status,
            'currency' => $order->currency,
            'source' => (int)$order->source,

            // 止盈止损（静态）
            'take_profit' => (float)$order->take_profit,
            'stop_loss' => (float)$order->stop_loss,

            // 平仓信息（静态）
            'sell_price' => (float)$order->sell_price,
            'sell_quantity' => (float)$order->sell_quantity,
            'sell_money' => (float)$order->sell_money,
            'sell_fees' => (float)$order->sell_fees,
            'see_fee' => (float)$order->see_fee,
            'sell_type' => (int)$order->sell_type,

            // 时间信息（静态）
            'buy_time' => $order->buy_time,
            'sell_time' => $order->sell_time,
            'created_at' => $order->created_at,

            // 标识字段（静态）
            'is_lock' => (int)$order->is_lock,
            'is_open_buy' => (int)$order->is_open_buy,
            'is_open_sell' => (int)$order->is_open_sell,

            // 历史盈亏（已平仓订单的最终盈亏）
            'profit_amount' => $order->status == 2 ? (float)$order->profit_amount : 0,

            // 损益率（已平仓订单的最终损益率）
            'profit_percentage' => $order->status == 2 ? $this->calculateProfitPercentage((float)$order->profit_amount, (float)$order->buy_cost) : 0,

            // 市值（已平仓订单使用平仓金额，其他状态为0，由实时数据更新）
            'market_cap' => $order->status == 2 ? $order->sell_price * $order->buy_quantity : 0,

            // 状态文本（静态）
            'status_text' => $this->getStatusText($order->status),
            'direction_text' => $order->direction_data == 1 ? __('api.买涨') : __('api.买跌'),
            'buy_type_text' => $order->buy_type == 1 ? __('api.市价') : __('api.限价'),
        ];

        // 根据订单类型添加显示信息
        return array_merge($baseData, [
            'display_type' => 'product',
            'display_name' => $order->product->name ?? __('api.未知产品'),
            'display_symbol' => $order->product->symbol ?? '',

            'product_info' => [
                'id' => $order->product_id,
                'name' => $order->product->name ?? __('api.未知产品'),
                'symbol' => $order->product->symbol ?? '',
                'currency' => $order->product->currency ?? '',
                'pro_type' => $order->product->pro_type ?? 1,
                'country' => $order->product->country ?? '',
                'other_id' => $order->product->other_id ?? '',
            ]
        ]);
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            1 => '持仓中',
            2 => '已平仓',
            3 => '平仓中',
            4 => '挂单中',
            5 => '已撤单',
        ];

        return $statusMap[$status] ?? __('api.未知状态');
    }

    /**
     * 为订单更新产品价格
     */
    private function updateProductPricesForOrders($orders)
    {
        if ($orders->isEmpty()) return;

        $productIds = $orders->pluck('product_id')->unique()->values()->all();
        $this->getRealTimePrices($productIds);
    }

    /**
     * 计算单个订单的盈亏
     */
    private function calculateOrderProfit($order)
    {
        // 如果已平仓，返回实际盈亏
        if ($order->status == 2) {
            return (float)$order->profit_amount;
        }

        // 如果是持仓中，计算当前盈亏
        if ($order->status == 1 && $order->product) {
            return $this->calculateRealTimeProfit(
                $order->direction_data,
                $order->buy_price,
                $order->product->price,
                $order->buy_quantity
            );
        }

        return 0.0;
    }

    /**
     * 计算单个订单的市值
     */
    private function calculateOrderMarketCap($order)
    {
        if ($order->status == 2) {
            // 已平仓：返回平仓金额
            return (float)$order->sell_money;
        }

        if ($order->status == 1 && $order->product) {
            // 持仓中：当前价格 * 数量
            return round((float)$order->product->price * (float)$order->buy_quantity, 2);
        }

        if ($order->status == 4) {
            // 挂单中：买入价格 * 数量
            return round((float)$order->buy_price * (float)$order->buy_quantity, 2);
        }

        return (float)$order->buy_cost;
    }

    /**
     * 计算胜率
     */
    private function calculateWinRate($orders)
    {
        $closedOrders = $orders->where('status', 2);
        if ($closedOrders->isEmpty()) {
            return 0;
        }

        $winOrders = $closedOrders->where('profit_amount', '>', 0)->count();
        return round(($winOrders / $closedOrders->count()) * 100, 2);
    }

    /**
     * 检查止盈止损并自动平仓
     */
    private function checkAndAutoClose($order, $currentPrice)
    {
        $direction = $order['direction_data'];
        $buyPrice = $order['buy_price'];
        $takeProfitPrice = $order['take_profit'];
        $stopLossPrice = $order['stop_loss'];

        $shouldClose = false;
        $closeType = 0;

        if ($direction == 1) {
            // 买涨：止盈价格高于买入价，止损价格低于买入价
            if ($takeProfitPrice > 0 && $takeProfitPrice > $buyPrice && $currentPrice >= $takeProfitPrice) {
                $shouldClose = true;
                $closeType = 3; // 止盈
            }
            if ($stopLossPrice > 0 && $stopLossPrice < $buyPrice && $currentPrice <= $stopLossPrice) {
                $shouldClose = true;
                $closeType = 4; // 止损
            }
        } else if ($direction == 2) {
            // 买跌：止盈价格低于买入价，止损价格高于买入价
            if ($takeProfitPrice > 0 && $takeProfitPrice < $buyPrice && $currentPrice <= $takeProfitPrice) {
                $shouldClose = true;
                $closeType = 3; // 止盈
            }
            if ($stopLossPrice > 0 && $stopLossPrice > $buyPrice && $currentPrice >= $stopLossPrice) {
                $shouldClose = true;
                $closeType = 4; // 止损
            }
        }

        if ($shouldClose) {
            try {
                // 自主订单使用OrderService
                \App\Services\OrderService::sell($order['user_id'], $order['id'], $closeType);
                return true;
            } catch (\Exception $e) {
                // 平仓失败，记录日志但不影响列表显示
                \Log::error('自动平仓失败', [
                    'order_id' => $order['id'],
//                    'user_id' => $order['user_id'],
                    'trade_id' => $order['trade_id'],
                    'close_type' => $closeType,
                    'current_price' => $currentPrice,
                    'error' => $e->getMessage()
                ]);
                return false;
            }
        }

        return false;
    }

    /**
     * todo: 作废
     * 跟单订单平仓
     */
    public function closeTraderOrder(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        $orderId = $request->input('order_id');
        if (!$orderId || !is_numeric($orderId)) {
            return $this->returnMsg(422, __('api.订单ID不合法'));
        }

        try {
            // 验证订单是否属于当前用户且为跟单订单
            $order = UserOrder::where('id', $orderId)
                ->where('user_id', $user->id)
                ->where('trade_id', '>', 0) // 确保是跟单订单
                ->first();

            if (!$order) {
                return $this->returnMsg(404, __('api.跟单订单不存在'));
            }

            if ($order->status != 1) {
                return $this->returnMsg(422, __('api.订单状态异常，无法平仓'));
            }

            // 调用跟单平仓服务
            $closedOrder = \App\Services\TraderOrderService::closeTraderOrder($orderId, 1);

            return $this->returnMsg(200, __('api.平仓成功'), [
                'order_id' => $closedOrder->id,
                'sell_price' => $closedOrder->sell_price,
                'sell_money' => $closedOrder->sell_money,
                'profit_amount' => $closedOrder->profit_amount,
                'sell_time' => $closedOrder->sell_time,
            ]);

        } catch (\Exception $e) {
            return $this->returnMsg(500,  $e->getMessage());
        }
    }

    /**
     * 计算增强的统计信息（包含实时数据）
     */
    private function calculateEnhancedStatistics($orders)
    {
        $orders = collect($orders);

        $product = Product::find($orders->first()->product_id);

        if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {

        }

        // 总投资额（所有订单的买入成本）
        $totalInvestment = $orders->sum('buy_cost');

        // 总盈亏金额
        $totalProfitAmount = $orders->sum('profit_amount');

        // 总市值（持仓中订单的当前市值 + 已平仓订单的平仓金额）
        $totalMarketValue = $orders->sum('market_cap');

        // 盈亏百分比（总盈亏 / 总投资额 * 100）
        $profitPercentage = $totalInvestment > 0 ? round(($totalProfitAmount / $totalInvestment) * 100, 2) : 0;

        // 按状态分类统计
        $holdingOrders = $orders->where('status', 1); // 持仓中
        $closedOrders = $orders->where('status', 2);  // 已平仓
        $pendingOrders = $orders->where('status', 4); // 挂单中
        $cancelledOrders = $orders->where('status', 5); // 已撤单

        // 持仓统计
        $holdingInvestment = $holdingOrders->sum('buy_cost');
        $holdingProfitAmount = $holdingOrders->sum('profit_amount');
        $holdingMarketValue = $holdingOrders->sum('market_cap');
        $holdingProfitPercentage = $holdingInvestment > 0 ? round(($holdingProfitAmount / $holdingInvestment) * 100, 2) : 0;

        // 已平仓统计
        $closedInvestment = $closedOrders->sum('buy_cost');
        $closedProfitAmount = $closedOrders->sum('profit_amount');
        $closedMarketValue = $closedOrders->sum('market_cap');
        $closedProfitPercentage = $closedInvestment > 0 ? round(($closedProfitAmount / $closedInvestment) * 100, 2) : 0;

        // 盈利订单统计
        $profitOrders = $orders->where('profit_amount', '>', 0);
        $lossOrders = $orders->where('profit_amount', '<', 0);
        $breakEvenOrders = $orders->where('profit_amount', '=', 0);

        // 胜率计算（只计算已平仓订单）
        $closedOrdersCount = $closedOrders->count();
        $winRate = 0;
        if ($closedOrdersCount > 0) {
            $winOrders = $closedOrders->where('profit_amount', '>', 0)->count();
            $winRate = round(($winOrders / $closedOrdersCount) * 100, 2);
        }

        // 合并统计信息
        $statistics = [
            // 总体统计
            'total_investment' => round($totalInvestment, 3),
            'total_profit_amount' => round($totalProfitAmount, 3),
            'total_market_value' => round($totalMarketValue, 3),
            'total_profit_percentage' => $profitPercentage,

            // 持仓统计
            'holding_count' => $holdingOrders->count(),
            'holding_investment' => round($holdingInvestment, 3),
            'holding_profit_amount' => round($holdingProfitAmount, 3),
            'holding_market_value' => round($holdingMarketValue, 3),
            'holding_profit_percentage' => $holdingProfitPercentage,

            // 已平仓统计
            'closed_count' => $closedOrders->count(),
            'closed_investment' => round($closedInvestment, 3),
            'closed_profit_amount' => round($closedProfitAmount, 3),
            'closed_market_value' => round($closedMarketValue, 3),
            'closed_profit_percentage' => $closedProfitPercentage,

            // 其他状态统计
            'pending_count' => $pendingOrders->count(),
            'cancelled_count' => $cancelledOrders->count(),

            // 盈亏分布
            'profit_orders_count' => $profitOrders->count(),
            'loss_orders_count' => $lossOrders->count(),
            'break_even_orders_count' => $breakEvenOrders->count(),

            // 胜率
            'win_rate' => $winRate,

            // 最大盈利和亏损
            'max_profit' => round($orders->max('profit_amount') ?: 0, 3),
            'max_loss' => round($orders->min('profit_amount') ?: 0, 3),
        ];

        return $statistics;
    }
}
