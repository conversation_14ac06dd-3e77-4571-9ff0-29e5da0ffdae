<?php

namespace App\Admin\Repositories;

use App\Models\DayTradingOrder as Model;
use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\UserOrder;
use App\Models\UsersInfo;
use App\Models\Product;
use App\Services\JiangshanService;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Facades\DB;

class DayTradingOrder extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function store(Form $form)
    {
        $result = null;


        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();
            $buyInPrice = $updates['buy_in_price'];
            $buyInNum = $updates['buy_in_num'];
            $succeInTime = $updates['success_in_time'];
            $productId = $updates['product_id'];
            $userId = $updates['user_id'];
            $lever = $updates['lever'];

            $userInfo = UsersInfo::query()->where('user_id', $userId)->lockForUpdate()->first();

            $buy_cost_currency = $buy_cost = ($buyInPrice * $buyInNum) / $lever;
            // 获取对应的费率，没有配置就获取全局的
            $rate = get_buy_fee_rate($userId);
            // todo: 计算手续费
            $buy_fees_currency = $buy_fees = buy_commission($buy_cost, $rate); // 手续费
            $updates['buy_in_market_val'] = $buy_cost;
            $updates['buy_in_fee'] = $buy_fees;
            $updates['buy_in_fee_rate'] = $rate;
            $updates['success_num'] = $buyInNum;
            $updates['success_fee'] = $buy_fees;
            $updates['success_market_val'] = $buy_cost;

            // 生成订单号
            $out_trade_no = generateUniqueOrderNumber();
            $updates['order_sn'] = $out_trade_no;


            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }

            $product = Product::findOrFail($productId);

            // todo: 西班牙市值超过10亿欧元的股票需要加多一个印花税
            // 印花税：0.2%
            $stamp_duty = 0;
            if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
                $stamp_duty = round($buy_cost * 0.002, 2); // 印花税
            }

            $user = \App\Models\Users::query()->where('id', $userId)->first();

            $exchange_rate = 1;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                if (in_array($product->pro_type, [2, 3, 4, 5])) {
                    // todo: 调用接口获取一次实时的接口

                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }else{
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }

                // 本金
                $buy_cost = calculateEuroCost($buy_cost, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                // 手续费
                $buy_fees = calculateEuroCost($buy_fees, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                // 印花税
                $stamp_duty = calculateEuroCost($stamp_duty, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                $exchange_rate = $exchangeRate->rate;
            }

            $updates['stamp_duty'] = $stamp_duty;
            $updates['exchange_rate'] = $exchange_rate;
            $updates['user_spread'] = $user->spread > 0 ? $user->spread : 0.00036;

            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }

            // 判断用户是否有足够的资金进行买入 product表的currency代表的是用户的资金类型
            $before = $userInfo->eur_balance;

            // 买入本金 = 买入成本 + 手续费 + 印花税
            $freeze_total = $buy_cost + $buy_fees + $stamp_duty;
            if ($userInfo->eur_balance < $freeze_total) {
                throw new \Exception(__('用户资金不足，无法进行买入'));
            }

            // 扣减，冻结金额
            $userInfo->eur_balance = $before - $freeze_total;
            $userInfo->eur_dongjie += $buy_cost;
            $userInfo->save();

            $result = $model->save();

            // 添加资金流水
            MoneyChange::create([
                'money_type' => 'EUR',
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_name' => $product->name,
                'detailed' => "日内交易申购|代码|{$product->symbol}|名称|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE_DAY_TRADING,
                'before_money' => $before,
                'money' => -$buy_cost,
                'after_money' => $before - $buy_cost,
            ]);

            if ($buy_fees > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $userId,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "日内交易手续费|代码|{$product->symbol}|名称|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE_DAY_TRADING,
                    'before_money' => $before - $buy_cost,
                    'money' => -$buy_fees,
                    'after_money' => $before - $buy_cost - $buy_fees,
                ]);
            }

            if ($stamp_duty > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $userId,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "日内交易印花税|代码|{$product->symbol}|名称|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE_DIVIDEND,
                    'before_money' => $before - $buy_cost - $buy_fees,
                    'money' => -$stamp_duty,
                    'after_money' => $before - $buy_cost - $buy_fees - $stamp_duty,
                ]);
            }

            // 拉取最新行情
            $jiangshan = new JiangshanService();
            $product = $jiangshan->get_new_data_serices($product);

            // 添加持仓
            UserOrder::create([
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_id' => $product->id,
                'product_type' => $product->pro_type,
                'direction_data' => 1, // 买涨
                'buy_type' => 2, // 限价
                'buy_price' => $buyInPrice,
                'buy_quantity' => $buyInNum,
                'lever' => $lever,
                'is_open_buy' => 0,
                'status' => 1,
                'buy_cost' => $buy_cost_currency, // 买入成本
                'take_profit' => 0,
                'stop_loss' => 0,
                'buy_time' => strtotime($succeInTime),
                'buy_fees' => $buy_fees_currency, // 手续费
                'buy_fees_rate' => $rate, // 手续费费率
                'market_cap' => $product->price * $buyInNum, // 当前市值
                'profit_amount' => ($product->price - $buyInPrice) * $buyInNum, // 赢亏
                'currency' => $product->currency, // 使用货币
                'user_spread' => $user->user_spread > 0 ? $user->user_spread : 0.00036, // 用户汇率价差
                'stamp_duty' => $stamp_duty ?? 0, // 西班牙印花税
                'exchange_rate' => $exchange_rate, // 货币兑换汇率(买入时)
            ]);


            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
