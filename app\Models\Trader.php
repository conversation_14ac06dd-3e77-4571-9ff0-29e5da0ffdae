<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class Trader extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'trader';

    protected $fillable = [
        'name',
        'nickname',
        'avatar',
        'en_introduction',
        'es_introduction',
        'de_introduction',
        'level',
        'email',
    ];
}
