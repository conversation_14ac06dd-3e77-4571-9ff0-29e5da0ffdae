<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Helpers\IpHelper;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\RechargeController;
use App\Http\Controllers\Api\UserCardController;
use App\Http\Controllers\Api\WithdrawController;
use App\Http\Controllers\Api\UserOrderController;
use App\Http\Controllers\Api\DividendController;
use App\Http\Controllers\Api\DayTradingController;
use App\Http\Controllers\Api\BigTradeController;
use App\Http\Controllers\Api\ExchangeController;
use App\Http\Controllers\Api\ZhishuController;
use App\Http\Controllers\Api\TraderController;
use \App\Http\Controllers\Api\IpoController;
use \App\Http\Controllers\Api\UserMessageController;
use \App\Http\Controllers\Api\NewsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 认证相关路由
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/email/send-code', [AuthController::class, 'sendEmailVerificationCode']);
Route::post('/password/forgot', [AuthController::class, 'forgotPassword']);
Route::post('/password/reset', [AuthController::class, 'resetPassword']);
Route::post('/refresh-token', [AuthController::class, 'refreshToken']); // 刷新令牌不需要认证

// 需要认证的路由
Route::middleware(['apiauth:sanctum'])->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'getUserInfo']);
    Route::get('/user_verify', [AuthController::class, 'getUserVerify']);

    // 我的关注列表
    Route::get('/my_attention', [UserController::class, 'get_attention']);

    // 我关注的投资者 （未实现）
    Route::get('/my_investors', [UserController::class, 'get_my_investors']);

    Route::post('/user_verify', [UserController::class, 'userVerify']); // 用户验证

    // 涨跌幅排行
    Route::get('/product/rank', [ProductController::class, 'rankProduct']);

    // recommendations 市场分析
    Route::get('/product/recommendations', [ProductController::class, 'recommendationsProduct']);

    // =========股票详情=============
    // 股票详情 -- 返回有关公司的一般信息。
    Route::get('/product/profile', [ProductController::class, 'profileProduct']);

    // 股票详情 -- 统计数据
    Route::get('/product/statistics', [ProductController::class, 'statisticsProduct']);

    // 股票详情 -- 股票收益统计
    Route::get('/product/earnings', [ProductController::class, 'earningsProduct']);

    // 股票详情 -- 分析板块1（盈利预测）
    Route::get('/product/estimate', [ProductController::class, 'estimateProduct']);

    // 股票详情 -- 分析板块2（收入估算）
    Route::get('/product/revenue', [ProductController::class, 'revenueProduct']);

    // 股票详情 -- 分析板块3（每股收益趋势）
    Route::get('/product/eps_trend', [ProductController::class, 'epsTrendProduct']);

    // 股票详情 -- 分析板块4（每股收益修订）
    Route::get('/product/eps_revisions', [ProductController::class, 'epsRevisionsProduct']);

    // 股票详情 -- 分析板块5（增长预测）
    Route::get('/product/growth_estimates', [ProductController::class, 'growthEstimatesProduct']);

    // 股票详情 -- 分析板块6（分析师评级）
    Route::get('/product/analyst_ratings_light', [ProductController::class, 'analystRatingsLightProduct']);

    // 股票详情 -- 历史价格
    Route::get('/product/historical_price', [ProductController::class, 'historicalPriceProduct']);

    // 股票详情 -- 损益表（年度、季度）
    Route::get('/product/income_statement', [ProductController::class, 'incomeStatementProduct']);

    // 股票详情 -- 资产负债表（年度、季度）
    Route::get('/product/balance_sheet', [ProductController::class, 'balanceSheetProduct']);

    // 股票详情 -- 现金流（年度、季度）
    Route::get('/product/cash_flow', [ProductController::class, 'cashFlowProduct']);

    // 股票详情 -- 持有人
    Route::get('/product/holders', [ProductController::class, 'holdersProduct']);
    // =========股票详情 end=============


    // =========充值=============
    Route::get('/recharge/list', [RechargeController::class, 'list']); // 充值申请记录
    Route::get('/recharge/bank', [RechargeController::class, 'bank']);
    Route::get('/recharge/usdt', [RechargeController::class, 'usdt']);
    Route::post('/recharge/submit', [RechargeController::class, 'submitRecharge']);
    // =========充值 end=============

    // =========用户银行卡=============
    Route::get('/mycard', [UserCardController::class, 'myCard']);
    Route::post('/bind_card', [UserCardController::class, 'bindCard']);
    Route::post('/delete_card', [UserCardController::class, 'deleteCard']);
    // =========用户银行卡 end=============

    // =========提现=============
    Route::get('/withdraw/list', [WithdrawController::class, 'list']); // 充值申请记录
    Route::post('/withdraw/submit', [WithdrawController::class, 'submitWithdraw']);
    // =========提现 end=============

    // =========资金转换=============
    Route::get('/exchange/currency', [ExchangeController::class, 'getCurrencyList']); // 获取可转换的货币
    Route::post('/exchange/rate', [ExchangeController::class, 'getExchangeRate']); // 获取兑换汇率
    Route::post('/exchange/submit', [ExchangeController::class, 'submit']); // 提交资金转换
    Route::get('/exchange/list', [ExchangeController::class, 'list']); // 兑换记录
    // =========提现 end=============

    // =========股票功能=============
    Route::get('/dividend/list', [DividendController::class, 'dividendList']); // 红利股票列表
    Route::post('/dividend/submit', [DividendController::class, 'submitDividend']); // 购买红利股票
    Route::get('/dividend/order', [DividendController::class, 'order']); // 红利订单


    Route::get('/day_trading/list', [DayTradingController::class, 'dayTradingList']); // 日内加以股票列表
    Route::post('/day_trading/submit', [DayTradingController::class, 'submitDayTarding']); // 购买日内交易股票
    Route::get('/day_trading/order', [DayTradingController::class, 'order']); // 日内订单

    Route::get('/big_trade/list', [BigTradeController::class, 'bigTradeList']); // 大宗交易股票列表
    Route::post('/big_trade/submit', [BigTradeController::class, 'submitBigTrade']); // 购买大宗交易股票
    Route::get('/big_trade/order', [BigTradeController::class, 'order']); // 大宗交易订单

    // =========股票功能 end=============

    // =========下单=============
    Route::post('/user_order/add', [UserOrderController::class, 'addOrder']); // 购买股票
    Route::get('/user_order/list', [UserOrderController::class, 'orderList']); // 订单记录

    // Route::post('/user_order/trader_orders', [UserOrderController::class, 'getTraderOrders']); // 获取交易员建仓的订单列表


    Route::get('/user_order/view', [UserOrderController::class, 'orderView']); // 订单详情
    Route::post('/user_order/sell', [UserOrderController::class, 'orderSell']); // 平仓记录
    Route::post('/user_order/cancel', [UserOrderController::class, 'orderCancel']); // 平仓记录

    // Route::post('/user_order/close_trader_order', [UserOrderController::class, 'closeTraderOrder']); // 跟单订单平仓
    // =========提现 end=============

    // =========投资者模块 开始=============
    Route::get('/trader/list', [TraderController::class, 'traderList']); // 交易员表
    Route::get('/trader/view', [TraderController::class, 'traderView']); // 交易员详情
    Route::get('/trader/info', [TraderController::class, 'traderInfo']); // 交易员统计资料表(做图表统计用)
    Route::get('/trader/group', [TraderController::class, 'traderGroup']); // 交易员投资组合(做投资列表展示用)
    Route::get('/trader/article', [TraderController::class, 'traderArticle']); // 交易员文章表
    Route::get('/trader/index_article', [TraderController::class, 'index_article']); // 交易员文章表

    Route::get('/trader/user_attention', [TraderController::class, 'userAttention']); // 用户已关注的交易员列表【新增】
    Route::get('/trader/user_attention_view', [TraderController::class, 'userAttentionView']); // 用户已关注的交易员列表【新增】

    Route::post('/trader/attention', [TraderController::class, 'traderAttention']); // 执行关注交易员
    Route::get('/trader/money_change', [TraderController::class, 'traderMoneyChange']); // 投资者资金流水
    Route::post('/trader/cancel_attention', [TraderController::class, 'cancelAttention']); // 取消关注交易员
    Route::post('/trader/attention_money', [TraderController::class, 'attentionMoney']); // 追加减少投资金额
    // =========投资者模块 结束=============


    // =========个人中心 开始=============
    Route::get('/user/balance', [UserController::class, 'getUserBalance']); // 获取用户余额
    Route::post('/user/update_login_password', [UserController::class, 'updateLoginPassword']); // 修改登录密码
    Route::post('/user/update_pay_password', [UserController::class, 'updatePayPassword']); // 修改支付密码
    Route::post('/user/auth_realname', [UserController::class, 'authRealname']); // 提交实名认证
    Route::get('/user/money_change', [UserController::class, 'moneyChange']); // 资金流水
    Route::get('/user/auth_info', [UserController::class, 'authInfo']); // 获取实名认证的状态
    Route::post('/user/update_selfinfo', [UserController::class, 'updateSelfinfo']); // 个人信息修改
    // =========个人中心 结束=============

    // =========新股ipo 开始=============
    Route::get('/ipo/list', [IpoController::class, 'listIpo']); // 新股ipo列表
    Route::post('/ipo/add_order', [IpoController::class, 'addOrderIpo']); // 新股ipo抽签
    Route::get('/ipo/order_list', [IpoController::class, 'orderListIpo']); // 新股ipo订单列表
    // =========新股ipo 结束=============

    // =========用户消息 开始=============
    Route::get('/user/message_count', [UserMessageController::class, 'countMessage']); // 用户未读消息数量
    Route::get('/user/message_list', [UserMessageController::class, 'listMessage']); // 用户消息列表
    Route::get('/user/message_view', [UserMessageController::class, 'viewMessage']); // 用户消息详情
    Route::post('/user/all_read', [UserMessageController::class, 'allRead']); // 用户一键已读
    Route::get('/user/notice_list', [UserMessageController::class, 'listNotice']); // 用户未读公告列表
    // =========用户消息 结束=============

    // =========客服地址=============
    Route::get('/customer_service', [UserController::class, 'customerService']);
});

Route::get('/product/search', [ProductController::class, 'search']); // 产品搜索
Route::get('/product/suggestions', [ProductController::class, 'suggestions']); // 搜索建议
Route::get('/product/hot', [ProductController::class, 'hotProduct']); // 热门产品
Route::get('/product/list', [ProductController::class, 'listProduct']); // 产品列表
Route::get('/product/stock/list', [ProductController::class, 'listStockProduct']); // 股票产品列表
Route::post('/product/info', [ProductController::class, 'detailProduct']); // 产品详情
Route::post('/product/kline', [ProductController::class, 'klineProduct']); // 产品详情

Route::get('/index/list', [ZhishuController::class, 'list']); // 指数列表
Route::post('/index/detail', [ZhishuController::class, 'detail']); // 指数详情
Route::post('/index/kline', [ZhishuController::class, 'kline']); // 指数k线

Route::get('/news/list', [NewsController::class, 'index']); // 新闻列表
Route::get('/news/detail/{id}', [NewsController::class, 'detail']); // 新闻详情


// 其他公共接口
// 上传文件接口
Route::post('/upload', [UserController::class, 'uploadFile']);








// IP测试路由（临时用于调试）
Route::get('/test-ip', function (Request $request) {
    return response()->json([
        'real_ip' => IpHelper::getRealIp($request),
        'debug_info' => IpHelper::getDebugInfo($request),
        'ip_info' => IpHelper::getIpInfo(IpHelper::getRealIp($request)),
    ]);
});
