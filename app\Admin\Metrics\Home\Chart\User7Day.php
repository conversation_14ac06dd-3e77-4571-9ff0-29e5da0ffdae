<?php

namespace App\Admin\Metrics\Home\Chart;

use App\Models\Recharge as ModelsRecharge;
use App\Models\Users;
use App\Models\Withdraw as ModelsWithdraw;
use App\Services\AdminUserService;
use App\Services\UserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\ApexCharts\Chart;

class User7Day extends Chart
{
    public function __construct($containerSelector = null, $options = [])
    {
        parent::__construct($containerSelector, $options);

        $this->setUpOptions();
    }

    /**
     * 初始化图表配置
     */
    protected function setUpOptions()
    {


        $this->options([
            'chart' => [
                'type' => 'area',
                'height' => 430
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
            'stroke' => [
                'curve' => 'smooth'
            ],
            'xaxis' => [
                'categories' => [],
            ],
        ]);
    }

    /**
     * 处理图表数据
     */
    protected function buildData()
    {
        for ($i = 7; $i >= 0; $i--) {
            $date[] = date('Y-m-d', strtotime("-" . $i . "days"));
            $date_x[] = date('m-d', strtotime("-" . $i . "days"));
        }

        $registerSum = []; // 注册
        // 获取当前登录的管理员账号
        $currentAdminUser = Admin::user();
        $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);
        foreach ($date as $k => $v) {
            $query = Users::where('created_at', '>=', $v . ' 00:00:00')->where('created_at', '<=', $v . ' 23:59:59');
            if (!empty($adminIds)) {
                $query->whereIn('parent_id', $adminIds);
            }

            $registerSum[$k] = $query->count();
        }
        // 执行你的数据查询逻辑
        $data = [
            [
                'name' => '注册人数',
                'data' => array_values($registerSum),
            ],
        ];
        $categories = $date_x;

        $this->withData($data);
        $this->withCategories($categories);
    }

    /**
     * 设置图表数据
     *
     * @param array $data
     *
     * @return $this
     */
    public function withData(array $data)
    {
        return $this->option('series', $data);
    }

    /**
     * 设置图表类别.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withCategories(array $data)
    {
        return $this->option('xaxis.categories', $data);
    }

    /**
     * 渲染图表
     *
     * @return string
     */
    public function render()
    {
        $this->buildData();

        return parent::render();
    }
}
