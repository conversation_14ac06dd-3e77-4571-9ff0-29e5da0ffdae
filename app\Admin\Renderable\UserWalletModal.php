<?php

namespace App\Admin\Renderable;

use App\Models\UsersInfo as Model;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;

class UserWalletModal extends LazyRenderable
{
    public function render()
    {
        // 获取ID
        $id = $this->id;

        // 获取其他自定义参数
        $type = $this->post_type;

        $res = Model::where('user_id', $id)
            ->select([
                'usd_balance',
                'usd_dongjie',
                'eur_dongjie',
                'eur_balance',
                'jpy_balance',
                'jpy_dongjie',
                'gbp_balance',
                'gbp_dongjie',
                'myr_balance',
                'myr_dongjie',
            ])
            ->first();
        if (!$res) {
            return '无数据';
        }

        $currencies = [];
        foreach ($res->toArray() as $key => $value) {
            if (preg_match('/^([a-z]+)_(balance|dongjie)$/', $key, $matches)) {
                $currency = $matches[1];
                $type = $matches[2];
                $currencies[$currency][$type] = $value;
            }
        }
        $resData = [];
        $currenciesShow = [
            'usd' => '美元',
            'eur' => '欧元',
            'gbp' => '英镑',
            'jpy' => '日元',
            'myr' => '马来林吉特',
        ];
        foreach ($currencies as $currency => $data) {
            $resData[] = [
                $currenciesShow[$currency],
                $data['balance'],
                $data['dongjie'],
            ];
        }

        $titles = [
            '类型',
            '余额',
            '冻结金额',
        ];

        return Table::make($titles, $resData);
    }
}
