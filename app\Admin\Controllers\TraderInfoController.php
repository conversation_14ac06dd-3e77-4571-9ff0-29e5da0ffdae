<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\TraderTable;
use App\Admin\Repositories\TraderInfo;
use App\Models\Trader;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TraderInfoController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TraderInfo(), function (Grid $grid) {
            $adminId = Admin::user()->id;

            $model = $grid->model()
                ->with(['trader'])
                ->whereHas('trader', function ($query) use ($adminId) {
                    $query->where('admin_id', $adminId);
                });
            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('trader.name', '交易员');
            $grid->column('trader.avatar')->image('', 100, 100);

            $grid->column('year');
            $grid->column('month');
            $grid->column('val');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TraderInfo(), function (Show $show) {
            $show->field('id');
            $show->field('trader_id');
            $show->field('year');
            $show->field('month');
            $show->field('val');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TraderInfo(), function (Form $form) {
            $form->display('id');
            $form->selectTable('trader_id')
                ->from(TraderTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Trader::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择交易员']);
            $form->date('year')->format('YYYY');
            $form->date('month')->format('MM');
            $form->text('val');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
