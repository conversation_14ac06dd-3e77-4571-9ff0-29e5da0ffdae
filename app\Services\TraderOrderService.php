<?php

namespace App\Services;

use App\Models\UserOrder;
use App\Models\Users;
use App\Models\UsersInfo;
use App\Models\Product;
use App\Models\TraderFollower;
use App\Models\TraderFollowerMoneyChange;
use App\Models\ExchangeRate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TraderOrderService
{
    /**
     * 跟单交易员订单平仓
     *
     * @param int $orderId 订单ID
     * @param int $closeType 平仓类型 1=主动, 2=强制, 3=止盈, 4=止损, 5=开盘自动
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * * @throws \Exception
     */
    public static function closeTraderOrder($orderId, $closeType = 1)
    {
        DB::beginTransaction();
        try {
            // 获取订单信息
            $order = UserOrder::with(['product', 'user'])
                ->where('id', $orderId)
                ->where('trade_id', '>', 0) // 确保是跟单订单
                ->lockForUpdate()
                ->first();

            if (!$order) {
                throw new \Exception(__('api.跟单订单不存在'));
            }

            if ($order->status != 1) {
                throw new \Exception(__('api.订单状态异常，无法平仓'));
            }

            if ($order->is_lock == 1) {
                throw new \Exception(__('api.订单已锁定，请联系客服'));
            }

            // 获取产品信息
            $product = $order->product;
            if (!$product) {
                throw new \Exception(__('api.产品不存在'));
            }

            // 获取当前价格
            $currentPrice = self::getCurrentPrice($product);

            // 计算盈亏
            $profitLoss = $order->direction_data == 1
                ? round(($currentPrice - $order->buy_price) * $order->buy_quantity, 2)
                : round(($order->buy_price - $currentPrice) * $order->buy_quantity, 2);

            // todo: 计算卖出金额（本金+盈亏金额）
            $sellMoney_currency = $sellMoney = $order->buy_cost + $profitLoss;

            $rate = get_buy_fee_rate($order->user_id);

            // todo: 计算卖出手续费，不再需要证交税，西班牙的印花税只有买入时候收取
            $sxf_currency = $sxf = sell_commission($sellMoney, $rate);

            // 如果当前产品不是欧元结算，则需要去计算欧元成本
            $eur_dongjie = $order->buy_cost;
            $sell_exchange_rate = 1;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                if (in_array($product->pro_type, [2, 3, 4, 5])) {
                    // todo: 调用接口获取一次实时的接口

                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }else{
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }
                $users = Users::where('id', $order->user_id)->first();

                // 要卖出的金额
                // 实时 EUR/USD 汇率,
                // 价差值
                $sellMoney = calculateEuroCost($sellMoney, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);
                $sxf = calculateEuroCost($sxf, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);

                $sell_exchange_rate = $exchangeRate->rate * (1 - $users->spread);

                // 计算卖出时候用户冻结余额应该减去多少，需要按照买入时候的汇率来计算
                $eur_dongjie = calculateEuroCost($order->buy_cost, $order->exchange_rate, $order->user_spread > 0 ? $order->user_spread : 0.00036);
            }

            // 获取跟单关系
            $follower = TraderFollower::where('user_id', $order->user_id)
                ->where('trader_id', $order->trade_id)
                ->lockForUpdate()
                ->first();

            $before = $follower->bet_money;

            // 更新跟单关系：返还本金+盈亏到bet_money，减少used_money
            $follower->bet_money += $sellMoney - $sxf;
            $follower->used_money -= $eur_dongjie;
            $follower->save();

            // 更新订单状态
            $order->update([
                'status' => 2, // 已平仓
                'sell_price' => $currentPrice,
                'sell_quantity' => $order->buy_quantity,
                'sell_money' => $sellMoney_currency,
                'profit_amount' => $profitLoss,
                'sell_fees' => $sxf_currency,
                'see_fee' => 0,
                'sell_exchange_rate' => $sell_exchange_rate,
                'sell_time' => time(),
                'sell_type' => 1,
                'market_cap' => $currentPrice * $order->buy_quantity,
            ]);

            // 记录跟单资金流水 - 平仓返还
            $protype = [1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '数字货币'];
            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $order->user_id,
                'trader_id' => $order->trade_id,
                'order_sn' => $order->order_sn,
                'product_name' => $order->product->name,
                'detailed' => "跟单平仓|{$protype[$order->product->pro_type]}|,|产品名称|:|{$order->product->name}|,|代码|:{$order->product->symbol}|",
                'before_money' => $before,
                'money' => $sellMoney,
                'after_money' => $before + $sellMoney,
            ]);

            // 记录跟单资金流水 - 减少已用金额
            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $order->user_id,
                'trader_id' => $order->trade_id,
                'order_sn' => $order->order_sn,
                'product_name' => $order->product->name,
                'detailed' => "跟单平仓手续费|{$protype[$order->product->pro_type]}|,|产品名称|:|{$order->product->name}|,|代码|:{$order->product->symbol}",
                'before_money' => $before + $sellMoney,
                'money' => -$sxf,
                'after_money' => $before + $sellMoney - $sxf,
            ]);

            DB::commit();
            return $order;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取当前价格
     */
    private static function getCurrentPrice($product)
    {
        if ($product->pro_type == 1) {
            // 股票使用江山服务
            $jiangshan = new \App\Services\JiangshanService();
            $data = $jiangshan->get_new_data_serices($product);
            return $data['price'] ?? $product->price;
        } else {
            // 其他产品使用TwelveData
            $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
            return $quote['price'] ?? $product->price;
        }
    }

    /**
     * 撤销跟单订单（挂单状态）
     */
    public static function cancelTraderOrder($orderId)
    {
        DB::beginTransaction();
        try {
            $order = UserOrder::where('id', $orderId)
                ->where('trade_id', '>', 0)
                ->where('status', 4) // 挂单中
                ->lockForUpdate()
                ->first();

            if (!$order) {
                throw new \Exception(__('api.挂单订单不存在'));
            }

            // 更新订单状态为已撤单
            $order->update(['status' => 5]);

            $product = Product::query()
                ->where('id', $order->product_id)
                ->where('deleted_at', null)
                ->where('state', 1)
                ->first();

            // 返还资金到跟单关系
            $follower = TraderFollower::where('user_id', $order->user_id)
                ->where('trader_id', $order->trade_id)
                ->lockForUpdate()
                ->first();

            if ($follower) {
                $buy_cost = $order->buy_cost;
                $buy_fees = $order->buy_fees;
                $stamp_duty = $order->stamp_duty;

                if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                    $buy_cost = calculateEuroCost($buy_cost, $order->exchange_rate, $order->user_spread);
                    $buy_fees = calculateEuroCost($buy_fees, $order->exchange_rate, $order->user_spread);
                    $stamp_duty = calculateEuroCost($stamp_duty, $order->exchange_rate, $order->user_spread);
                }
                $totalCost = $buy_cost + $buy_fees + $stamp_duty;

                $beforeDongjie = $follower->bet_money; // 冻结前的金额

                $follower->bet_money += $totalCost;
                $follower->used_money -= $buy_cost;
                $follower->save();

                // 记录资金流水
                TraderFollowerMoneyChange::create([
                    'money_type' => "EUR",
                    'user_id' => $order->user_id,
                    'trader_id' => $order->trade_id,
                    'order_sn' => $order->order_sn,
                    'product_name' => $order->product->name,
                    'detailed' => "跟单撤单|返还本金",
                    'before_money' => $beforeDongjie,
                    'money' => $buy_cost,
                    'after_money' => $beforeDongjie + $buy_cost,
                ]);

                // 记录资金变动 - 手续费
                if ($buy_fees > 0) {
                    TraderFollowerMoneyChange::create([
                        'money_type' => "EUR",
                        'user_id' => $order->user_id,
                        'trader_id' => $order->trade_id,
                        'order_sn' => $order->order_sn,
                        'product_name' => $order->product->name,
                        'detailed' => "跟单撤单|返回手续费",
                        'before_money' => $beforeDongjie + $buy_cost,
                        'money' => $buy_fees,
                        'after_money' => $beforeDongjie + $buy_cost + $buy_fees,
                    ]);
                }

                // 记录资金变动 - 手续费
                if ($stamp_duty > 0) {
                    TraderFollowerMoneyChange::create([
                        'money_type' => "EUR",
                        'user_id' => $order->user_id,
                        'trader_id' => $order->trade_id,
                        'order_sn' => $order->order_sn,
                        'product_name' => $order->product->name,
                        'detailed' => "跟单撤单|返回印花税",
                        'before_money' => $beforeDongjie + $buy_cost + $buy_fees,
                        'money' => $stamp_duty,
                        'after_money' => $beforeDongjie + $buy_cost + $buy_fees + $stamp_duty,
                    ]);
                }
            }

            DB::commit();
            return $order;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
