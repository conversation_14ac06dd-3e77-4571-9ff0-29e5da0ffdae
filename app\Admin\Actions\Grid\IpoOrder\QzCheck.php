<?php

namespace App\Admin\Actions\Grid\IpoOrder;

use App\Models\IpoOrder;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class QzCheck extends RowAction
{
    /**
     * @return string
     */
    protected $title = '强制审核';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        $model = IpoOrder::findOrFail($id);

        if ($model->state != 1) {
            return $this->response()->error('当前订单不是已中签状态，无法强制审核通过')->refresh();
        }

        if ($model->pay_state != 2) {
            return $this->response()->error('当前订单不是支付中状态，不允许强制审核通过')->refresh();
        }

        if ($model->check_state != 0) {
            return $this->response()->error('当前订单不是已中待审核状态，无法强制审核通过')->refresh();
        }

        if ($model->renjiaonum == $model->zhongqianshu) {
            return $this->response()->error('当前订单已买够中签数量，请去到审核中通过')->refresh();
        }
        $model->check_state = 1;
        $model->pay_state = 1;
        $model->zhongqianshu = $model->renjiaonum;
        $model->xurenjiao = 0.00;
        $model->yingrenjiao = $model->yirenjiao;

        $model->save();

        return $this->response()
            ->success('强制审核成功')
            ->refresh();

    }

    /**
     * 设置HTML标签的属性
     *
     * @return void
     */
    protected function setupHtmlAttributes()
    {
        // 添加class
        $this->addHtmlClass('btn bt-sm btn-danger');

        // 保存弹窗的ID
        $this->setHtmlAttribute('data-target', '#' . $this->getKey());

        parent::setupHtmlAttributes();
    }


    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确定强制审核通过', ''];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
