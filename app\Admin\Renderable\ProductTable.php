<?php

namespace App\Admin\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Product;

class ProductTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new Product(), function (Grid $grid) {

            $model = $grid->model();
            $model->where('pro_type', 1)->where('state', 1);

//            $grid->column('pro_type', '类型')->using([1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '数字货币']);
            $grid->column('name');
            $grid->column('symbol', '股票代码');
            $grid->column('price', '价格');
            $grid->column('exchange', '交易所');
            $grid->column('country', '国家')->display(function ($country) {
                $countryArr = Product::$PRO_STOCK_COUNTRY;
                if ($country && isset($countryArr[$country])) {
                    return $countryArr[$country];
                }
                return $country;
            });

            $grid->quickSearch(['id', 'name', 'symbol']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand(); // 让筛选默认展开
                $filter->equal('country', '国家')->select(Product::$PRO_STOCK_COUNTRY)
                    ->load('exchange', admin_url('/product_stock_country_exchange'))
                    ->width(3);
                // 交易所下拉（初始为空，选了国家后自动加载）
                $filter->equal('exchange', '交易所')->select()->width(3);
                $filter->like('name')->width(4);
                $filter->like('symbol', '股票代碼')->width(4);
            });
        });
    }
}
