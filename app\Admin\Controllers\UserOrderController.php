<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\AllProductTable;
use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\UserOrder;
use App\Models\Product;
use App\Models\Users;
use App\Models\Trader;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Grid\UserOrder\Sell;
use App\Admin\Actions\Grid\UserOrder\Cancel;
use App\Admin\Actions\Grid\UserOrder\DeleteOrder;

class UserOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserOrder(), function (Grid $grid) {
            $grid->disableEditButton();
            $grid->paginate(10);

            // 禁用分页选择器
            $grid->disablePerPages();

            $model = $grid->model()->with(['user', 'product', 'trader']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('currency');
            $grid->column('user.email', '账户');
            $grid->column('product_type')->using([1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '加密货币']);
            $grid->column('product.name', '产品名称');
            $grid->column('product.symbol', '产品代码');
            $grid->column('trader_name', '跟单员')->display(function () {
                if ($this->trade_id == 0) {
                    return '-';
                }
                return $this->trader ? $this->trader->name : '未知';
            });
            $grid->column('direction_data')->using([1 => '涨', 2 => '跌'])->badge([
                1 => 'success',
                2 => 'danger'
            ]);
            $grid->column('order_sn');

            $grid->column('buy_type')->using([1 => '市价', 2 => '限价']);
            $grid->column('buy_price');
            $grid->column('product.price', '当前价');
            $grid->column('buy_quantity');
            $grid->column('buy_cost');
            $grid->column('lever');
            $grid->column('status')->using([1 => '持仓中', 2 => '已平仓', 3 => '平仓中', 4 => '挂单中', 5 => '已撤单'])->badge([
                1 => 'success',
                2 => '#a3a9a6',
                3 => '#b6821d',
                4 => '#2a55cc',
                5 => '#ad1d59',
            ]);
            $grid->column('buy_fees');
            $grid->column('buy_time')->display(function ($buy_time) {
                return date('Y-m-d H:i:s', $buy_time);
            });
//            $grid->column('market_cap');
//            $grid->column('margin_call');
//            $grid->column('overnight_fee');
//            $grid->column('profit_amount');
//            $grid->column('take_profit');
//            $grid->column('stop_loss');
//            $grid->column('sell_type')->using([0 => '', 1 => '主动卖出', 2 => '强制平仓']);
//            $grid->column('sell_price');
//            $grid->column('sell_quantity');
//            $grid->column('sell_money');
//            $grid->column('sell_fees');
//            $grid->column('sell_time');
//            $grid->column('is_open_buy')->using([0 => '否', 1 => '是']);
//            $grid->column('is_open_sell')->using([0 => '否', 1 => '是']);
//            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->column('扩展')->expand(function (Grid\Displayers\Expand $expand) {
                $expand->button('更多');
                $isOpenBuy = $this->is_open_buy ? '是' : '否';
                $isOpenSell = $this->is_open_sell ? '是' : '否';
                $sellType = [0 => '', 1 => '主动卖出', 2 => '强制平仓'];
                $profitAdmountHtml = '';
                if ($this->profit_amount > 0) {
                    $profitAdmountHtml = "<span style='color: #15e315'>{$this->profit_amount}</span>";
                } elseif ($this->profit_amount < 0) {
                    $profitAdmountHtml = "<span style='color: #ca0a27'>{$this->profit_amount}</span>";
                } else {
                    $profitAdmountHtml = "{$this->profit_amount}";
                }
                return <<<HTML
    <div style="padding: 5px 10px;">
        <table class="table table-bordered" style="margin-bottom:0;">
            <tbody>
                <tr>
                    <th>买入费率</th><td>{$this->buy_fees_rate}%</td>
                    <th>卖出价格</th><td>{$this->sell_price}</td>
                </tr>
                <tr>
                    <th>当前市值</th><td>{$this->market_cap}</td>
                    <th>卖出数量</th><td>{$this->sell_quantity}</td>
                    <th style="width:150px;">追加保证金</th><td>{$this->margin_call}</td>
                </tr>
                <tr>
                   <th>盈亏金额</th><td>{$profitAdmountHtml}</td>
                    <th>卖出手续费</th><td>{$this->sell_fees}</td>
                    <!--<th>开盘买入</th><td>{$isOpenBuy}</td> -->
                    <th>隔夜费</th><td>{$this->overnight_fee}</td>
                </tr>
                <tr>
                    <th>卖出金额</th><td>{$this->sell_money}</td>
                    <th>卖出成交时间</th><td>{$this->sell_time}</td>

                </tr>
                <tr>
                    <th>止盈金额</th><td>{$this->take_profit}</td>
                    <th>止损金额</th><td>{$this->stop_loss}</td>
                </tr>
               <tr>
                    <th>货币兑换汇率(买入时)</th><td>{$this->exchange_rate}</td>
                    <th>货币兑换汇率(卖出时)</th><td>{$this->sell_exchange_rate}</td>
               </tr>
               <tr>
                    <th>西班牙印花税</th><td>{$this->stamp_duty}</td>
                    <th>用户汇率价差</th><td>{$this->user_spread}</td>
                </tr>
            </tbody>
        </table>
    </div>
HTML;
            });
            $grid->column('is_lock')->switch();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete(); // 禁用系统的删除功能，走自己的删除，只有持仓中或挂单中，才能删除订单
                // append一个操作
                if ($this->status == 1) {
                    $actions->append(new Sell()); // 强制平仓
                } elseif ($this->status == 4) {
                    $actions->append(new Cancel());
                }
                if ($this->status == 1 || $this->status == 4) {
                    $actions->append(new DeleteOrder()); // 只有持仓中或挂单中，才能删除订单
                }
            });


            $grid->filter(function (Grid\Filter $filter) {
                // 更改为 panel 布局
                $filter->panel();
                $filter->like('user.email', '用户');
                $filter->like('order_sn');
                $filter->like('product.name', '产品名称');
                $filter->like('product.symbol', '产品名代码');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        $repository = new UserOrder(['user', 'product']);
        return Show::make($id, $repository, function (Show $show) {
            $show->disableEditButton();
            $show->disableDeleteButton();
            $show->field('id');
            $show->field('user.email', '用户');
            $show->field('product_type')->using([1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '加密货币']);
            $show->field('product.name', '产品');
            $show->field('product.symbol', '产品代码');
            $show->field('order_sn');
            $show->field('direction_data')->using([1 => '涨', 2 => '跌']);
            $show->field('buy_type')->using([1 => '市价', 2 => '限价']);
            $show->field('buy_price');
            $show->field('buy_quantity');
            $show->field('buy_cost');
            $show->field('lever');
            $show->field('status')->using([1 => '持仓中', 2 => '已平仓', 3 => '平仓中', 4 => '挂单中', 5 => '已撤单']);
            $show->field('buy_fees');
            $show->field('buy_time')->as(function ($buy_time) {
                return date('Y-m-d H:i:s', $buy_time);
            });
            $show->field('market_cap');
            $show->field('margin_call');
            $show->field('overnight_fee');
            $show->field('profit_amount');
            $show->field('take_profit');
            $show->field('stop_loss');
            $show->field('sell_type');
            $show->field('sell_price');
            $show->field('sell_quantity');
            $show->field('sell_money');
            $show->field('sell_fees');
            $show->field('sell_time')->as(function ($sell_time) {
                if ($sell_time) {
                    return date('Y-m-d H:i:s', $sell_time);
                } else {
                    return '无';
                }
            });
            $show->field('is_lock')->using([0 => '否', 1 => '是']);
            $show->field('is_open_buy')->using([0 => '否', 1 => '是']);
            $show->field('is_open_sell')->using([0 => '否', 1 => '是']);
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserOrder(), function (Form $form) {
            $form->disableEditingCheck();
            $form->display('id');
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择用户']);
            $form->selectTable('product_id')
                ->from(AllProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Product::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择用户']);
            $form->radio('direction_data')->options(['1' => '买涨', '2' => '买跌'])->default('1');
//            $form->text('buy_type');
            $form->text('buy_price')->required()->rules('required|numeric|min:0.01', ['required' => '请输入买入价格', 'numeric' => '请输入数字', 'min' => '不能低于0']);
            $form->text('buy_quantity')->required()->rules('required|numeric|min:0.001', ['required' => '请输入买入股数', 'numeric' => '请输入整数', 'min' => '不能低于0.001']);
            $form->text('lever')->default(1)->rules('required|integer|min:1|max:100', ['required' => '请输入杠杆', 'numeric' => '请输入数字', 'min' => '不能低于1', 'max' => '不能高于100'])->required();
            $form->text('buy_fees')->readOnly();
            $form->text('buy_cost')->readOnly();
            $form->datetime('buy_time')->default(date('Y-m-d H:i:s'));
            $form->text('take_profit');
            $form->text('stop_loss');
            $form->switch('is_lock')->default(0);

            // 加载动态计算JS
            $form->html($this->ajaxCalcScript());

        });
    }

    protected function ajaxCalcScript()
    {
        $url = admin_url('user-order/calc-fees'); // 你的接口路径

        return <<<HTML
<script>
Dcat.ready(function () {
    function calcCostAndFees() {
        var buy_price = parseFloat($('input[name="buy_price"]').val());
        var buy_quantity = parseFloat($('input[name="buy_quantity"]').val());
        var lever = parseFloat($('input[name="lever"]').val());
        var user_id = $('input[name="user_id"]').val();

        if (isNaN(buy_price) || isNaN(buy_quantity) || isNaN(lever) || isNaN(user_id) || buy_price <=0 || buy_quantity <=0 || lever <=0 || !user_id) {
            $('input[name="buy_cost"]').val('');
            $('input[name="buy_fees"]').val('');
            return;
        }

        var cost = (buy_price * buy_quantity) / lever;
        $('input[name="buy_cost"]').val(cost.toFixed(2));

        // 请求后台获取手续费
        $.ajax({
            url: '{$url}',
            type: 'POST',
            data: {
                _token: Dcat.token,
                user_id: user_id,
                buy_price: buy_price,
                buy_quantity: buy_quantity,
                lever: lever
            },
            success: function (res) {
                if (res.status) {
                    $('input[name="buy_fees"]').val(res.data.buy_fees);
                } else {
                    Dcat.error(res.message);
                    $('input[name="buy_fees"]').val('');
                }
            }
        });
    }

    $('input[name="buy_price"], input[name="buy_quantity"], input[name="lever"]').on('input', function () {
        calcCostAndFees();
    });
});
</script>
HTML;
    }

    public function calcFees()
    {
        $buy_price = request('buy_price');
        $buy_quantity = request('buy_quantity');
        $lever = request('lever');
        $user_id = request('user_id');

        if (!$buy_price || !$buy_quantity || !$lever || !$user_id) {
            return response()->json(['status' => false, 'message' => '参数错误']);
        }

        // 示例手续费计算逻辑（按成本 0.5% 计算，你可替换成实际逻辑）
        $cost = ($buy_price * $buy_quantity) / $lever;

        if ($cost <= 0) return response()->json(['status' => false, 'message' => '买入成本必须大于0']);
        $rate = get_buy_fee_rate($user_id);
        $buy_fees = buy_commission($cost, $rate);

        return response()->json([
            'status' => true,
            'data' => [
                'buy_fees' => $buy_fees,
                'buy_fees_rate' => $rate,
            ]
        ]);
    }


}
