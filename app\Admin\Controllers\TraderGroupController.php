<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\AllProductTable;
use App\Admin\Renderable\TraderTable;
use App\Admin\Repositories\TraderGroup;
use App\Models\Product;
use App\Models\Trader;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TraderGroupController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TraderGroup(), function (Grid $grid) {
            $adminIn = Admin::user()->id;
            $model = $grid->model()
                ->with(['trader', 'product'])
                ->whereHas('trader', function ($query) use ($adminIn) {
                    $query->where('admin_id', $adminIn);
                });
            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('trader.name', '交易员');
            $grid->column('product.name', '产品');
            $grid->column('product.name', '产品代码');
            $grid->column('funds_ratio');
            $grid->column('profit_ratio');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TraderGroup(), function (Show $show) {
            $show->field('id');
            $show->field('trader_id');
            $show->field('product_id');
            $show->field('funds_ratio');
            $show->field('profit_ratio');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TraderGroup(), function (Form $form) {
            $form->display('id');
            $form->selectTable('trader_id')
                ->from(TraderTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Trader::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择交易员']);
            $form->selectTable('product_id')
                ->from(AllProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Product::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择产品']);
            $form->text('funds_ratio')->required();
            $form->text('profit_ratio')->required();

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
