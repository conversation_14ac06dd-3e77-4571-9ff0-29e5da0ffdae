<?php

namespace App\Admin\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Product;

class AllProductTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new Product(), function (Grid $grid) {

            $model = $grid->model();
            $model->where('state', 1)->orderBy('id', 'desc');

//            $grid->column('id');
//            $grid->column('pro_type', '类型')->using([1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '数字货币']);
            $grid->column('pro_type', '类型')->using([1 => '股票', 2 => '大宗商品', 4 => '外汇', 5 => '数字货币']);
            $grid->column('name');
            $grid->column('symbol', '代码');
            $grid->column('price', '当前价');
            $grid->column('exchange', '交易所');
            $grid->column('country', '国家')->display(function ($country) {
                $countryArr = Product::$PRO_STOCK_COUNTRY;
                if ($country && isset($countryArr[$country])) {
                    return $countryArr[$country];
                }
                return $country;
            });

            $grid->quickSearch(['id', 'name', 'symbol']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand(); // 让筛选默认展开
//                $filter->equal('pro_type', '类型')->select([1 => '股票', 2 => '大宗商品', 3 => 'ETF', 4 => '外汇', 5 => '数字货币'])->width(4);
                $filter->equal('pro_type', '类型')->select([1 => '股票', 2 => '大宗商品', 4 => '外汇', 5 => '数字货币'])->width(4);
                $filter->like('name')->width(4);
                $filter->equal('symbol', '代码')->width(4);
            });
        });
    }
}
