<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\AdminPosition as AdminPositionModel;
use App\Services\AdminUserService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Auth\Permission;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Http\Repositories\Administrator;
use App\Models\AdminUser as AdministratorModel;
use Dcat\Admin\Show;
use Dcat\Admin\Support\Helper;
use Dcat\Admin\Widgets\Tree;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\DB;

class AdminUserController extends AdminController
{
    public function title()
    {
        return trans('admin.administrator');
    }

    protected function grid()
    {
        return Grid::make(Administrator::with(['roles', 'position']), function (Grid $grid) {

            $model = $grid->model();

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereIn('id', $adminIds);
            }

            $grid->column('id', 'ID')->sortable();
            $grid->column('invite_code', '邀请码');
            $grid->column('position.name', '职位');
            $grid->column('username');
            $grid->column('name');

            if (config('admin.permission.enable')) {
                $grid->column('roles')->pluck('name')->label('primary', 3);

                $permissionModel = config('admin.database.permissions_model');
                $roleModel = config('admin.database.roles_model');
                $nodes = (new $permissionModel())->allNodes();
                $grid->column('permissions', '权限')
                    ->if(function () {
                        return !$this->roles->isEmpty();
                    })
                    ->showTreeInDialog(function (Grid\Displayers\DialogTree $tree) use (&$nodes, $roleModel) {
                        $tree->nodes($nodes);

                        foreach (array_column($this->roles->toArray(), 'slug') as $slug) {
                            if ($roleModel::isAdministrator($slug)) {
                                $tree->checkAll();
                            }
                        }
                    })
                    ->else()
                    ->display('');
            }

            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->quickSearch(['id', 'name', 'username']);

            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->showColumnSelector();
            $grid->disableEditButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->getKey() == AdministratorModel::DEFAULT_ID) {
                    $actions->disableDelete();
                }
            });
        });
    }

    protected function detail($id)
    {
        return Show::make($id, Administrator::with(['roles']), function (Show $show) {
            $show->field('id');
            $show->field('username');
            $show->field('name');

            $show->field('avatar', __('admin.avatar'))->image();

            if (config('admin.permission.enable')) {
                $show->field('roles')->as(function ($roles) {
                    if (!$roles) {
                        return;
                    }

                    return collect($roles)->pluck('name');
                })->label();

                $show->field('permissions')->unescape()->as(function () {
                    $roles = $this->roles->toArray();

                    $permissionModel = config('admin.database.permissions_model');
                    $roleModel = config('admin.database.roles_model');
                    $permissionModel = new $permissionModel();
                    $nodes = $permissionModel->allNodes();

                    $tree = Tree::make($nodes);

                    $isAdministrator = false;
                    foreach (array_column($roles, 'slug') as $slug) {
                        if ($roleModel::isAdministrator($slug)) {
                            $tree->checkAll();
                            $isAdministrator = true;
                        }
                    }

                    if (!$isAdministrator) {
                        $keyName = $permissionModel->getKeyName();
                        $tree->check(
                            $roleModel::getPermissionId(array_column($roles, $keyName))->flatten()
                        );
                    }

                    return $tree->render();
                });
            }

            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    public function form()
    {
        return Form::make(Administrator::with(['roles']), function (Form $form) {
            $userTable = config('admin.database.users_table');

            $connection = config('admin.database.connection');

            $id = $form->getKey();

            $form->display('id', 'ID');

            $form->text('username', trans('admin.username'))
                ->required()
                ->creationRules(['required', "unique:{$connection}.{$userTable}"])
                ->updateRules(['required', "unique:{$connection}.{$userTable},username,$id"]);
            $form->text('name', trans('admin.name'))->required();
            if ($id != 1) {
                $form->text('invite_code', '邀请码')->required()->append('<button type="button" class="btn btn-sm btn-primary generate-invite-code-btn">生成</button>');

                Admin::script(<<<JS
    $(document).off('click', '.generate-invite-code-btn').on('click', '.generate-invite-code-btn', function () {
        let input = $(this).closest('.input-group').find('input[name="invite_code"]');
        let code = Math.random().toString(36).substr(2, 8).toUpperCase();
        input.val(code);
    });
JS
                );
            }


            $form->image('avatar', trans('admin.avatar'))->autoUpload();

            if ($id) {
                $form->password('password', trans('admin.password'))
                    ->minLength(5)
                    ->maxLength(20)
                    ->customFormat(function () {
                        return '';
                    });
            } else {
                $form->password('password', trans('admin.password'))
                    ->required()
                    ->minLength(5)
                    ->maxLength(20);
            }

            $form->password('password_confirmation', trans('admin.password_confirmation'))->same('password');

            $form->ignore(['password_confirmation']);

            if (config('admin.permission.enable')) {
//                $form->multipleSelect('roles', trans('admin.roles'))
                $form->multipleSelect('roles', '权限')
                    ->options(function () {
                        $roleModel = config('admin.database.roles_model');

                        return $roleModel::all()->pluck('name', 'id');
                    })
                    ->customFormat(function ($v) {
                        return array_column($v, 'id');
                    });
            }

            $positionModel = AdminPositionModel::class;

            $form->select('position_id', '职位')->options(function () use ($positionModel) {
                return $positionModel::selectOptions();
            })->saving(function ($v) {
                return (int)$v;
            });;

//            $form->display('created_at', trans('admin.created_at'));
//            $form->display('updated_at', trans('admin.updated_at'));

            if ($id == AdministratorModel::DEFAULT_ID) {
                $form->disableDeleteButton();
            }
        })->saving(function (Form $form) {

            // 获取当前邀请码
            $inviteCode = $form->invite_code;

            // 是否为超级管理员（可以自定义逻辑）
            $isSuperAdmin = ($form->getKey() == AdministratorModel::DEFAULT_ID);

            // 非超级管理员必须填写邀请码
            if (!$isSuperAdmin && empty($inviteCode)) {
                throw new \Exception('邀请码不能为空（超级管理员除外）');
            }

            // 验证唯一性（如果填写了）
            if (!empty($inviteCode)) {
                $exists = DB::table('admin_users')
                    ->where('invite_code', $inviteCode)
                    ->where('id', '!=', $form->getKey())
                    ->exists();

                if ($exists) {
                    throw new \Exception('邀请码已存在，请更换');
                }
            }

            if ($form->password && $form->model()->get('password') != $form->password) {
                $form->password = bcrypt($form->password);
            }

            if (!$form->password) {
                $form->deleteInput('password');
            }
        });
    }

    public function destroy($id)
    {
        if (in_array(AdministratorModel::DEFAULT_ID, Helper::array($id))) {
            Permission::error();
        }

        return parent::destroy($id);
    }

    protected function getAllSubPositionIds($positionId)
    {
        $all = AdminPosition::all();
        $results = [];

        $walk = function ($id) use (&$walk, &$results, $all) {
            foreach ($all as $pos) {
                if ($pos->parent_id == $id) {
                    $results[] = $pos->id;
                    $walk($pos->id);
                }
            }
        };

        $walk($positionId);

        return $results;
    }

    protected function buildPositionTreeOptions($positions, $parentId = null, $prefix = '')
    {
        $result = [];

        foreach ($positions->where('parent_id', $parentId) as $pos) {
            $result[$pos->id] = $prefix . $pos->name;
            $result += $this->buildPositionTreeOptions($positions, $pos->id, $prefix . '├─ ');
        }

        return $result;
    }
}
