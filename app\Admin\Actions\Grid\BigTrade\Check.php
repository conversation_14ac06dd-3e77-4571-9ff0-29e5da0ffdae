<?php

namespace App\Admin\Actions\Grid\BigTrade;

use App\Admin\Actions\Grid\Withdraw\Authenticatable;
use App\Admin\Actions\Grid\Withdraw\HasPermissions;
use App\Admin\Actions\Grid\Withdraw\Model;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Modal;
use App\Admin\Forms\BigTradeOrderCheck;

class Check extends RowAction
{
    /**
     * @return string
     */
    protected $title = '审核';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Modal
     */
    public function render()
    {
        $form = BigTradeOrderCheck::make()->payload(['id' => $this->getKey()]);

        return Modal::make()
            ->lg()
            ->title('审核')
            ->body($form)
            ->button('审核');

    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        // return ['你确定要删除此行内容吗？', '弹窗内容'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }


}
