<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Ipo;
use App\Models\Product;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class IpoController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Ipo(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('symbol');
            $grid->column('name');
            $grid->column('exchange');
//            $grid->column('shares');
            $grid->column('price');
            $grid->column('star_buy_date');
            $grid->column('end_buy_date');
            $grid->column('to_market_date');
            $grid->column('chouqian_date');
            $grid->column('state')->switch();
//            $grid->column('weight');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Ipo(), function (Show $show) {
            $show->field('id');
            $show->field('symbol');
            $show->field('name');
            $show->field('exchange');
            $show->field('mic_code');
            $show->field('currency');
            $show->field('offer_price');
            $show->field('shares');
            $show->field('price');
            $show->field('star_buy_date');
            $show->field('end_buy_date');
            $show->field('to_market_date');
            $show->field('chouqian_date');
            $show->field('state');
            $show->field('weight');
//            $show->field('tbpro_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Ipo(), function (Form $form) {
            $form->display('id');
            $form->column(6, function (Form $form) {
                $form->text('symbol')->required()->rules('required');
                $form->text('name')->required()->rules('required');
                $form->select('country', '国家')
                    ->options(Product::$PRO_STOCK_COUNTRY)
                    ->load('exchange', admin_url('/product_stock_country_exchange'));
                // 交易所下拉（初始为空，选了国家后自动加载）
                $form->select('exchange', '交易所')->required()->rules('required');
//            $form->text('mic_code');
//            $form->text('currency');
//                $form->text('offer_price')->required()->rules('required')->help('随意添加价格');
//                $form->number('shares')->required()->rules('required')->help('随意填写数字');
            });

            $form->column(6, function (Form $form) {
                $form->currency('price')->required()->rules('required');
                $form->date('star_buy_date')->required()->rules('required');
                $form->date('end_buy_date')->required()->rules('required');
                $form->date('chouqian_date')->required()->rules('required');
                $form->date('to_market_date')->required()->rules('required');
                $form->switch('state');
                $form->number('weight')->default(0);
//            $form->text('tbpro_id');
            });
        });
    }
}
