<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use Illuminate\Console\Command;

class SendTestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:send-test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test verification email';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email format.');
            return 1;
        }

        $this->info("Sending test email to: {$email}");
        
        $code = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        $result = EmailService::sendVerificationCode($email, $code, 'register');
        
        if ($result) {
            $this->info("✅ Email sent successfully!");
            $this->info("Verification code: {$code}");
        } else {
            $this->error("❌ Failed to send email. Check logs for details.");
        }
        
        return $result ? 0 : 1;
    }
}
