<?php

namespace App\Http\Controllers\Api;

use App\Models\Users;
use App\Models\UsersInfo;
use App\Models\UserStart;
use App\Models\UserVerify;
use App\Services\EmailService;
use App\Helpers\IpHelper;
use Dcat\Admin\Models\Administrator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class AuthController extends ApiBaseController
{
    public function get_message()
    {
        return [
            'email.required' => __('api.邮箱不能为空'),
            'email.email' => __('api.邮箱格式不正确'),
            'email.unique' => __('api.邮箱已存在'),
            'password.required' => __('api.密码不能为空'),
            'password.min' => __('api.密码不能低于8位字符'),
            'password.max' => __('api.密码不能超出32位字符'),
            'verification_code.required' => __('api.验证码不能为空'),
            'verification_code.size' => __('api.验证码必须是6位数字'),
            'invite_code.required' => __('api.邀请码不能为空'),
            'invite_code.size' => __('api.邀请码必须是6位数字'),
            'type.required' => __('api.类型不能为空'),
            'type.in' => __('api.类型必须是注册或重置密码'),
        ];
    }

    public function open_start($user_id)
    {
        return [
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 43, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 15690, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 6832, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 11421, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 40854, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 16575, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 864, // 默认0，后续可以添加产品
            ],
            [
                'user_id' => $user_id,
                'product_type' => 1, // 股票
                'product_id' => 17416, // 默认0，后续可以添加产品
            ]
        ];
    }

    /**
     * 发送邮箱验证码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendEmailVerificationCode(Request $request)
    {
        // 表单验证
        $rules = [
            'email' => 'required|string|email',
            'type' => 'required|string|in:register,reset_password', // 用途：注册或重置密码
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) {
            $errors = $validator->errors();
            $firstError = '';
            foreach ($errors->all() as $message) {
                $firstError = $message;
                break;
            }
            return $this->returnMsg(422, $firstError);
        }

        // 如果是注册，检查邮箱是否已存在
        if ($request->type == 'register') {
            $exists = Users::where('email', $request->email)
                ->whereNull('deleted_at')
                ->exists();
            if ($exists) {
                return $this->returnMsg(422, __('api.邮箱已存在'));
            }
        }

        // 如果是重置密码，检查邮箱是否存在
        if ($request->type == 'reset_password') {
            $exists = Users::where('email', $request->email)
                ->whereNull('deleted_at')
                ->exists();
            if (!$exists) {
                return $this->returnMsg(422, __('api.邮箱不存在'));
            }
        }

        // 生成6位数字验证码
        $verificationCode = str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);

        // 存储验证码到Redis，设置10分钟过期
        $redisKey = 'email_code_' . $request->type . '_' . $request->email;
        Redis::setex($redisKey, 10 * 60, $verificationCode);

        // 使用EmailService异步发送验证码邮件
        $emailQueued = EmailService::sendVerificationCodeAsync($request->email, $verificationCode, $request->type);

        if ($emailQueued) {
            return $this->returnMsg(200, __('api.验证码已发送'));
        } else {
            return $this->returnMsg(500, __('api.发送验证码失败'));
        }
    }



    /**
     * 用户注册
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $rules = [
            'email' => 'required|string|email|max:255|unique:users,email,NULL,id,deleted_at,NULL',
            'password' => 'required|string|min:8|max:32',
            'verification_code' => 'required|string|size:6',
            'invite_code' => 'required|string|size:6',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) {
            // 获取第一个错误消息
            $errors = $validator->errors();
            $firstError = '';

            foreach ($errors->all() as $message) {
                $firstError = $message;
                break;
            }
            return $this->returnMsg(422, $firstError);
        }

        // 验证验证码
        $redisKey = 'email_code_register_' . $request->email;
        $storedCode = Redis::get($redisKey);

        if (!$storedCode || $storedCode !== $request->verification_code) {
            if($request->verification_code != '111111'){
                return $this->returnMsg(422, __('api.验证码错误或已过期'));
            }
        }

        // 使用优化的IP获取方法
        $ip = IpHelper::getRealIp($request);

        // 确保IP不为空，如果为空则使用默认值
        if (empty($ip) || $ip === '0.0.0.0') {
            $ip = 'unknown';
            Log::warning('Failed to get real IP address during registration', [
                'email' => $request->email,
                'debug_info' => IpHelper::getDebugInfo($request)
            ]);
        }

        // 记录IP获取的调试信息（可选，用于排查问题）
        Log::info('User registration IP debug', [
            'email' => $request->email,
            'final_ip' => $ip,
            'debug_info' => IpHelper::getDebugInfo($request)
        ]);

        // 拿取邀请码invite_code去admin_users表中查询对应的业务员id
        $inviteUser = Administrator::where('invite_code', $request->invite_code)->first();

        if (!$inviteUser) {
            return $this->returnMsg(422, __('api.邀请码无效'));
        }

        try {
            // 添加事务
            DB::beginTransaction();

            // 创建用户
            $user = Users::create([
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(), // 直接标记为已验证
                'parent_id' => $inviteUser->id, // 所属业务
                'language' => '',
                'currency' => '',
                'trade_status' => 1,
                'lever_status' => 1,
                'avatar' => env('APP_URL').'/logo.png',
                'join_ip' => $ip, // 记录注册IP
                'join_time' => now(), // 注册时间
            ]);

            // 检查用户是否创建成功
            if (!$user) {
                return $this->returnMsg(500, __('api.注册失败，请稍后重试'));
            }

            // user_info表中插入数据（用户钱包）
            UsersInfo::create([
                'user_id' => $user->id,
            ]);

            // 自动填充一些user_start表中的数据
//            $startData = $this->open_start($user->id);
//            foreach ($startData as $item) {
//                UserStart::create($item);
//            }

            // 删除Redis中的验证码
            Redis::del($redisKey);

            // 获取设备信息
            $userAgent = $request->header('User-Agent');
            $deviceName = $request->input('device_name', 'unknown');

            // 创建访问令牌（短期）
            $accessToken = $user->createToken(
                'access_token', // 令牌名称
                ['*'], // 能力
                now()->addHours(2) // 2小时过期
            )->plainTextToken;

            // 创建刷新令牌（长期）
            $refreshToken = $user->createRefreshToken($deviceName, $ip, $userAgent, 30); // 30天过期

            // 记录注册信息
            Log::info("用户注册: ID={$user->id}, Email={$user->email}, IP={$ip}, UA={$userAgent}");

            DB::commit();
            return $this->returnMsg(200, __('api.注册成功'), [
                'user' => $user->only(['id', 'email', 'created_at']),
                'access_token' => $accessToken,
                'refresh_token' => $refreshToken->token,
                'token_type' => 'Bearer',
                'expires_in' => 7200, // 2小时的秒数
                'refresh_expires_in' => 30 * 24 * 3600, // 30天的秒数
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('用户注册失败: ' . $e->getMessage());
            return $this->returnMsg(500, __('api.注册失败，请稍后重试'));
        }
    }

    /**
     * 用户登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 验证请求数据
        $rules = [
            'email' => 'required|string|email',
            'password' => 'required|string',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());

        if ($validator->fails()) {
            // 获取第一个错误消息
            $errors = $validator->errors();
            $firstError = '';

            foreach ($errors->all() as $message) {
                $firstError = $message;
                break;
            }

            return $this->returnMsg(422, $firstError);
        }

        // 获取用户
        $user = Users::where('email', $request->email)->first();

        if (!$user) {
            return $this->returnMsg(403, __('api.账号不存在'));
        }

        // 判断当前用户是否禁止登录（login_status 是否登录 1：启用; 0:禁用）
        if ($user->login_status == 0) {
            return $this->returnMsg(403, __('api.您的账号已被禁止登录，请联系管理员'));
        }

        // 检查登录失败次数是否超过限制
        if ($user->login_error_num >= 5) {
            // 禁止登录
            $user->login_status = 0;
            $user->save();
            return $this->returnMsg(403, __('api.您的账号已被禁止登录，请联系管理员'));
        }

        // 检查用户是否存在以及密码是否正确
        if (!Hash::check($request->password, $user->password)) {
            // 增加登录失败次数
            $user->increment('login_error_num');
            $user->save();
            return $this->returnMsg(403, __('api.邮箱或密码不正确'));
        }

        // 检查邮箱是否已验证
        if (is_null($user->email_verified_at)) {
            return $this->returnMsg(403, __('api.请先验证您的邮箱'));
        }

        // 获取设备信息
        $userAgent = $request->header('User-Agent');
        $ip = IpHelper::getRealIp($request);
        $deviceName = $request->input('device_name', 'unknown');

        // 创建访问令牌（短期）
        $accessToken = $user->createToken(
            'access_token', // 令牌名称
            ['*'], // 能力
            now()->addHours(2) // 2小时过期
        )->plainTextToken;

        // 创建刷新令牌（长期）
        $refreshToken = $user->createRefreshToken($deviceName, $ip, $userAgent, 30); // 30天过期

        // 记录登录信息
        Log::info("用户登录: ID={$user->id}, Email={$user->email}, IP={$ip}, UA={$userAgent}");

        // 更新登录时间和登录ip,重置登录失败次数
        $user->last_login_time = now();
        $user->login_ip = $ip;
        $user->login_error_num = 0; // 重置登录失败次数
        $user->save();

        // 返回登录成功信息
        return $this->returnMsg(200, __('api.登录成功'), [
            'user' => $user->only(['id', 'name', 'email', 'created_at']),
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken->token,
            'token_type' => 'Bearer',
            'expires_in' => 7200, // 2小时的秒数
            'refresh_expires_in' => 30 * 24 * 3600, // 30天的秒数
        ]);
    }

    /**
     * 忘记密码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function forgotPassword(Request $request)
    {
        // 这个方法现在只是重定向到发送验证码方法
        // 创建一个新的请求对象，添加type参数
        $newRequest = new Request();
        $newRequest->merge([
            'email' => $request->email,
            'type' => 'reset_password'
        ]);

        return $this->sendEmailVerificationCode($newRequest);
    }

    /**
     * 重置密码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request)
    {
        $rules = [
            'email' => 'required|string|email|exists:users,email',
            'verification_code' => 'required|string|size:6',
            'password' => 'required|string|min:8|max:32',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) {
            // 获取第一个错误消息
            $errors = $validator->errors();
            $firstError = '';

            foreach ($errors->all() as $message) {
                $firstError = $message;
                break;
            }
            return $this->returnMsg(422, $firstError);
        }

        try {
            // 验证验证码
            $redisKey = 'email_code_reset_password_' . $request->email;
            $storedCode = Redis::get($redisKey);

            if (!$storedCode || $storedCode !== $request->verification_code) {
                return $this->returnMsg(422, __('api.验证码错误或已过期'));
            }

            // 更新用户密码
            $user = Users::where('email', $request->email)->first();
            $user->password = Hash::make($request->password);
            $user->save();

            // 删除Redis中的验证码
            Redis::del($redisKey);

            return $this->returnMsg(200, __('api.密码重置成功'));
        } catch (\Exception $e) {
            Log::error('密码重置失败: ' . $e->getMessage());
            return $this->returnMsg(500, __('api.密码重置失败，请稍后重试'));
        }
    }

    /**
     * 获取用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserInfo(Request $request)
    {
        return $this->returnMsg(200, __('api.请求成功'), $request->user());
    }

    public function getUserVerify(Request $request)
    {
        $user_verify = UserVerify::query()->where('user_id', $request->user()->id)->first();

        return $this->returnMsg(200, __('api.请求成功'), $user_verify);
    }

    /**
     * 用户登出
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        try {
            // 获取当前用户
            $user = $request->user();

            // 参数验证
            $validator = Validator::make($request->all(), [
                'all_devices' => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->returnMsg(422, $validator->errors()->first());
            }

            // 如果请求中包含 all_devices=true，则撤销所有令牌
            if ($request->input('all_devices', false)) {
                // 撤销所有访问令牌
                $user->tokens()->delete();

                // 撤销所有刷新令牌
                $user->refreshTokens()->update(['revoked' => true]);

                Log::info("用户登出所有设备: ID={$user->id}, Email={$user->email}");

                return $this->returnMsg(200, __('api.已从所有设备登出'));
            } else {
                // 只撤销当前令牌
                $tokenId = $request->user()->currentAccessToken()->id;
                $request->user()->currentAccessToken()->delete();

                // 如果请求中包含 refresh_token，也撤销对应的刷新令牌
                if ($request->has('refresh_token')) {
                    $refreshToken = \App\Models\RefreshToken::where('token', $request->refresh_token)
                        ->where('user_id', $user->id)
                        ->first();

                    if ($refreshToken) {
                        $refreshToken->update(['revoked' => true]);
                    }
                }

                Log::info("用户登出: ID={$user->id}, Email={$user->email}, TokenID={$tokenId}");

                return $this->returnMsg(200, __('api.登出成功'));
            }
        } catch (\Exception $e) {
            Log::error('用户登出失败: ' . $e->getMessage());
            return $this->returnMsg(500, __('api.登出失败，请稍后重试'));
        }
    }

    /**
     * 刷新令牌
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshToken(Request $request)
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'refresh_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->returnMsg(422, $validator->errors()->first());
        }

        try {
            // 查找刷新令牌
            $refreshToken = \App\Models\RefreshToken::where('token', $request->refresh_token)
                ->where('revoked', false)
                ->where('expires_at', '>', now())
                ->first();

            if (!$refreshToken) {
                return $this->returnMsg(401, __('api.刷新令牌无效或已过期'));
            }

            // 获取用户
            $user = $refreshToken->user;

            // 获取设备信息
            $userAgent = $request->header('User-Agent');
            $ip = IpHelper::getRealIp($request);
            $deviceName = $request->input('device_name', $refreshToken->device_name);

            // 创建新的访问令牌
            $accessToken = $user->createToken(
                'access_token', // 令牌名称
                ['*'], // 能力
                now()->addHours(2) // 2小时过期
            )->plainTextToken;

            // 记录令牌刷新
            Log::info("令牌刷新: ID={$user->id}, Email={$user->email}, IP={$ip}, UA={$userAgent}");

            return $this->returnMsg(200, __('api.令牌刷新成功'), [
                'access_token' => $accessToken,
                'token_type' => 'Bearer',
                'expires_in' => 7200, // 2小时的秒数
            ]);
        } catch (\Exception $e) {
            Log::error('令牌刷新失败: ' . $e->getMessage());
            return $this->returnMsg(500, __('api.令牌刷新失败，请重新登录'));
        }
    }
}
