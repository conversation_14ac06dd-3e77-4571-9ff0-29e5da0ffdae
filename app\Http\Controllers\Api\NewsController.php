<?php

namespace App\Http\Controllers\Api;


use App\Models\News;
use Illuminate\Http\Request;

class NewsController extends ApiBaseController
{


    public function index(Request $request)
    {
        $type = $request->input('type', 'de'); // 默认的德国
        $list = News::query()->where('type', $type)
            ->select(['id', 'title', 'news_add_time', 'type', 'image'])
            ->orderBy('news_add_time', 'desc')
            ->paginate(10);

        $list->getCollection()->transform(function ($item) {

            $item->image = env('APP_URL') . '/uploads' . $item->image;
            return $item;

        });

        return $this->returnMsg(200, __('api.请求成功'), $list);
    }

    public function detail(Request $request, $id)
    {
        $data = News::query()->select(['id', 'type',  'title', 'news_add_time', 'image', 'content'])->find($id);
        $data->image = env('APP_URL') . '/uploads' . $data->image;
        return $this->returnMsg(200, __('api.请求成功'), $data);

    }
}

