<?php

namespace App\Http\Controllers\Api;


use App\Models\UserCard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;


class UserCardController extends ApiBaseController
{
    public function get_message()
    {
        return [
            'type.required' => __('api.请选择绑定类型'),
            'type.in' => __('api.绑定类型错误'),
            'pay_pass.required' => __('api.请输入支付密码'),
            'bank.required' => __('api.请输入银行名称'),
            'bank_owner.required' => __('api.请输入银行开户人'),
            'bank_card_no.required' => __('api.请输入银行卡号'),
            'network.required' => __('api.请输入网络类型'),
            'usdt_address.required' => __('api.请输入USDT地址'),
        ];
    }
    /**
     * 绑卡
     */
    public function bindCard(Request $request)
    {

        $rules = [
            'type' => 'required|in:1,2',
//            'pay_pass' => 'required',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        $this->validate($request, $rules);

        $data = $request->all();

        if ($data['type'] == 1) {
            $rules = [
                'bank' => 'required',
                'bank_owner' => 'required',
                'bank_card_no' => 'required',
            ];
        } else {
            $rules = [
                'network' => 'required',
                'usdt_address' => 'required',
            ];
        }

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        $user = $request->user();

//        if (!$user->pay_password) {
//            return $this->returnMsg(504, __('api.请先设置支付密码'));
//        }
//
//        // 密码校验
//        if (!Hash::check($data['pay_pass'], $user->pay_password)) {
//            return $this->returnMsg(504, __('api.支付密码错误'));
//        }

        if ($data['type'] == 1) {
            $pat = '/^\d{10,30}$/';
            if (!preg_match($pat, $data['bank_card_no'])) {
                return $this->returnMsg(504, __('api.银行卡号位数') . ':10-30');
            }

            $cardExists = UserCard::query()->where('bank_card_no', $data['bank_card_no'])->exists();
            if ($cardExists) {
                return $this->returnMsg(504, __('api.银行卡号已经被绑定'));

            }
            unset($data['network']);
            unset($data['usdt_address']);

        } else {

            if (!preg_match('/^[A-Za-z0-9]+$/', $data['usdt_address'])) {
                return $this->returnMsg(207, [], __('api.地址格式错误'));
            }

            $usdtExists = UserCard::query()->where('usdt_address', $data['usdt_address'])->exists();
            if ($usdtExists) {
                return $this->returnMsg(504, __('api.地址已被绑定'));
            }
            unset($data['bank']);
            unset($data['bank_owner']);
            unset($data['bank_card_no']);
        }

        $count = UserCard::query()->where('user_id', $user->id)->count();

        if ($count > 5) return $this->returnMsg(503, [], __('api.最多只能绑定5张银行卡'));


        $data['user_id'] = $user->id;
        $res = UserCard::create($data);

        if ($res) {
            return $this->returnMsg(200, __('api.请求成功'));
        } else {
            return $this->returnMsg(503, __('api.提交失败'));
        }

    }

    /**
     * 获取我绑定的卡
     */
    public function myCard(Request $request)
    {

        $data = UserCard::query()
            ->where('user_id', $request->user()->id)
            ->select(['id', 'type', 'bank', 'bank_owner', 'bank_card_no', 'network', 'usdt_address', 'user_id'])
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $data);
    }

    /**
     * 删除绑定的卡
     */
    public function deleteCard(Request $request)
    {
        $rules = [
            'id' => 'required|integer|exists:user_card,id',
        ];


        $this->validate($request, $rules);

        $userCard = UserCard::query()->find($request->input('id'));

        if ($userCard->user_id != $request->user()->id) {
            return $this->returnMsg(403, [], __('api.没有权限删除此卡'));
        }



        if (UserCard::destroy($request->input('id'))) {
            return $this->returnMsg(200, __('api.请求成功'));
        } else {
            return $this->returnMsg(503, __('api.提交失败'));
        }
    }

}
