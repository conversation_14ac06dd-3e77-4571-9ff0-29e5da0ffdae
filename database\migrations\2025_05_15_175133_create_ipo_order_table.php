<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIpoOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ipo_order', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('ipo_id')->index()->comment('新股id');
            $table->unsignedInteger('user_id')->index()->comment('用户id');
            $table->decimal('buy_in_fee_rate', 15, 4)->comment('手续费率');
            $table->string('order_sn', 50)->index()->comment('订单号');
            $table->integer('buy_num')->comment('申购数(股)');
            $table->decimal('buy_price', 15, 3)->comment('申购价格');
            $table->integer('zhongqianshu')->default(0)->comment('中签数(股)');
            $table->decimal('xurenjiao', 15, 3)->comment('申购需认缴');
            $table->decimal('yingrenjiao', 15, 3)->comment('中签应认缴');
            $table->decimal('yirenjiao', 15, 3)->default(0)->comment('已认缴');
            $table->integer('renjiaocishu')->default(0)->comment('认缴次数');
            $table->decimal('renjiaonum', 15, 3)->default(0)->comment('已认缴股数');
            $table->tinyInteger('state')->default(0)->comment('状态:0:申购中，1：已中签，2未中签');
            $table->tinyInteger('pay_state')->default(0)->comment('支付状态:0:未支付，1：支付成功, 2:支付中');
            $table->tinyInteger('check_state')->default(0)->comment('支付状态:0:待审核，1：审核成功，2：拒绝');
            $table->tinyInteger('grant_state')->default(0)->comment('支付状态:0:未持仓，1：已持仓，2：已退款');
            $table->tinyInteger('is_show')->default(1)->comment('是否展示，0：不展示，1：展示 平仓后的记录无需再展示');
            $table->timestamp('buy_time')->nullable()->comment('申购时间');

            $table->unsignedDecimal('exchange_rate', 15, 5)->default(0.00000)->comment('货币兑换汇率(买入时)');
            $table->unsignedDecimal('user_spread', 15, 5)->default(0.00000)->comment('用户汇率价差');

            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}ipo_order auto_increment=1 comment '新股申购表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ipo_order');
    }
}
