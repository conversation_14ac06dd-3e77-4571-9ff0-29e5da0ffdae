<?php

namespace App\Console\Commands;

use App\Services\ProductPriceUpdateService;
use Illuminate\Console\Command;

class CleanupPriceLocks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'price:cleanup-locks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired price update locks to prevent deadlocks';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired price update locks...');
        
        try {
            ProductPriceUpdateService::cleanupExpiredLocks();
            $this->info('Price update locks cleanup completed successfully.');
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to cleanup price update locks: ' . $e->getMessage());
            return 1;
        }
    }
}
