<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\TraderFollowerMoneyChange;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TraderFollowerMoneyChangeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TraderFollowerMoneyChange(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableBatchActions();

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }

            $model = $grid->model()->with(['user', 'trader']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('money_type');
            $grid->column('user.email', '用户');
            $grid->column('trader.name', '交易员');
            $grid->column('order_sn');
            $grid->column('product_name');
            $grid->column('detailed');
            $grid->column('before_money');
            $grid->column('money');
            $grid->column('after_money');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TraderFollowerMoneyChange(), function (Show $show) {
            $show->field('id');
            $show->field('money_type');
            $show->field('user_id');
            $show->field('trader_id');
            $show->field('order_sn');
            $show->field('product_name');
            $show->field('detailed');
            $show->field('before_money');
            $show->field('money');
            $show->field('after_money');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TraderFollowerMoneyChange(), function (Form $form) {
            $form->display('id');
            $form->text('money_type');
            $form->text('user_id');
            $form->text('trader_id');
            $form->text('order_sn');
            $form->text('product_name');
            $form->text('detailed');
            $form->text('before_money');
            $form->text('money');
            $form->text('after_money');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
