<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWithdrawTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('withdraw', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->unsignedInteger('user_id')->default(0)->index()->comment('用户id');
            $table->string('order_sn', 50)->default('')->index()->comment('订单号');

            $table->tinyInteger('type')->default(1)->comment('提现方式 1=银行卡 2=加密货币');
            $table->integer('user_card_id')->comment('银行账户id');

            $table->decimal('money', 15, 3)->default(0.00)->comment('提现金额');
            $table->tinyInteger('state')->default(0)->comment('审核状态:0=待审核,1=成功,2=失败');
            $table->string('remark', 60)->default('')->comment('备注');

            $table->unsignedDecimal('exchange_rate', 15, 5)->default(0.00)->comment('货币兑换汇率(买入时)');
            $table->unsignedDecimal('user_spread', 15, 5)->default(0.00000)->comment('用户汇率价差');

            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}withdraw auto_increment=1 comment '提现表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('withdraw');
    }
}
