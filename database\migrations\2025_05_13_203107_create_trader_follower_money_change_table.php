<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTraderFollowerMoneyChangeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trader_follower_money_change', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->string('money_type', 50)->index()->comment('货币:usd,eur');
            $table->unsignedInteger('user_id')->index()->comment('用户id');
            $table->unsignedInteger('trader_id')->index()->comment('用户id');
            $table->string('order_sn', 50)->default('')->index()->comment('订单号');
            $table->string('product_name', 255)->default('')->comment('产品名称');
            $table->string('detailed', 255)->default('')->comment('备注（记录详情）');
            $table->decimal('before_money', 15, 3)->default(0.00)->comment('金额变动前');
            $table->decimal('money', 15, 3)->default(0.00)->comment('变动金额');
            $table->decimal('after_money', 15, 3)->default(0.00)->comment('变动后金额');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}money_change auto_increment=1 comment '跟随者的资金流水表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trader_follower_money_change');
    }
}
