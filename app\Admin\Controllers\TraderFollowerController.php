<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\UserTable;
use App\Admin\Renderable\TraderTable;
use App\Admin\Repositories\TraderFollower;
use App\Models\Trader;
use App\Models\Users;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class TraderFollowerController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TraderFollower(), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableCreateButton();
            $adminId = Admin::user()->id;
            $model = $grid->model()
                ->with(['trader', 'user'])
                ->whereHas('trader', function ($query) use ($adminId) {
                    $query->where('admin_id', $adminId);
                });
            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('trader.name', '交易员');
            $grid->column('user.email', '关注用户');
            $grid->column('bet_money');
            $grid->column('used_money', '已使用额度');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TraderFollower(), function (Show $show) {
            $show->field('id');
            $show->field('trader_id');
            $show->field('user_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TraderFollower(), function (Form $form) {
            $form->display('id');
            $form->selectTable('trader_id')
                ->from(TraderTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Trader::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择交易员']);
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择用户']);

            $form->number('bet_money')->required()->rules('required|numbic|min:1')->help('默认扣除美金账户金额，并进行冻结');


            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
