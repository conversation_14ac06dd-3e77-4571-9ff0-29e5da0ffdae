<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class TraderFollower extends Model
{
    use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'trader_follower';

    protected $fillable = [
        'trader_id',
        'user_id',
        'bet_money',
        'used_money',
        'state',
    ];

    public function user()
    {
        return $this->belongsTo(Users::class, 'user_id', 'id');
    }

    public function trader()
    {
        return $this->belongsTo(Trader::class, 'trader_id', 'id')->where('deleted_at', null);
    }

}
