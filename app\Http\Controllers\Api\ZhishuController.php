<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use App\Models\Zhishu;
use App\Services\JiangshanService;
use App\Services\TwelvedataNewService;
use Illuminate\Http\Request;

class ZhishuController extends ApiBaseController
{

    /**
     * 热门指数 todo
     */
    public function hotProduct(Request $request)
    {
    }

    public function list(Request $request)
    {
        $country = $request->input('country', 'de'); // 股票產品

        $result = Zhishu::query()->where('state', 1)
            ->where('country', $country)
            ->orderBy('weight', 'desc')
            ->get();
        $jiangshan = new JiangshanService();
        $result = $jiangshan->get_new_data_by_index_batch($result, $country);
        $result->makeHidden(['created_at', 'updated_at', 'deleted_at', 'other_id', 'state']);
        return $this->returnMsg(200, __('api.请求成功'), $result);
    }

    /**
     * 详情
     */
    public function detail(Request $request)
    {
        $id = $request->get('id'); // id
        $zhishu = Zhishu::query()->find($id);
        if (!$zhishu) {
            return $this->returnMsg(500, __('api.产品不存在'));
        }
        $jiangshan = new JiangshanService();
        $result = $jiangshan->get_new_data_by_index_by_one($zhishu);

        $result->makeHidden(['created_at', 'updated_at', 'deleted_at', 'other_id', 'state']);

        return $this->returnMsg(200, __('api.请求成功'), $result);
    }

    /**
     * K线
     */

    public function kline(Request $request)
    {
        $id = $request->get('id', 0); // 产品类型  股票、大宗商品、外汇等。

        $lineType = $request->get('line_type'); // 类型


        // 匠山 1=1分钟 ，2=5分钟，3= 15分钟 ，4=30分钟 ，5=45分钟 ，6=天， 7=周 ，8=月 以匠山的爲準

        if (!in_array($lineType, [1, 2, 3, 4, 5, 6, 7, 8])) {
            $lineType = 8;
        }

        $product = Zhishu::query()->find($id);
        if (!$product) {
            return $this->returnMsg(500, __('api.产品不存在'));
        }

        $jiangshan = new JiangshanService();
        $res = $jiangshan->getKline($product, $lineType);

        if (!$res) {
            return $this->returnMsg(500, __('api.请求失败'));
        }

        return $this->returnMsg(200, __('api.请求成功'), $res);
    }

}
