# 邮件发送性能优化

## 问题解决

### ✅ 优化前后对比
- **优化前**: 邮件同步发送，等待时间 6-10 秒
- **优化后**: 邮件异步发送，响应时间 < 1 秒

## 实现方案

### 1. 队列系统
使用Laravel队列系统将邮件发送任务异步处理：

**配置更改**:
```env
# .env 文件
QUEUE_CONNECTION=database  # 从 sync 改为 database
```

**新增文件**:
- `app/Jobs/SendVerificationEmail.php` - 邮件发送任务
- `database/migrations/*_create_jobs_table.php` - 队列数据表

### 2. 服务优化
**EmailService 新增方法**:
- `sendVerificationCodeAsync()` - 异步发送（推荐）
- `sendVerificationCode()` - 同步发送（备用）

**智能回退机制**:
如果队列系统失败，自动回退到同步发送，确保邮件不丢失。

## 使用方法

### 开发环境测试
```bash
# 1. 发送测试邮件（立即返回）
php artisan email:send-test <EMAIL>

# 2. 启动队列工作进程处理邮件
php artisan queue:work --timeout=60
```

### 生产环境部署
```bash
# 使用提供的批处理文件启动队列工作进程
start-queue-worker.bat
```

或者手动启动：
```bash
php artisan queue:work --timeout=60 --sleep=3 --tries=3 --max-time=3600
```

## 队列配置说明

### 任务参数
- **tries**: 3 - 最大重试次数
- **timeout**: 60秒 - 单个任务超时时间
- **sleep**: 3秒 - 无任务时休眠时间
- **max-time**: 3600秒 - 工作进程最大运行时间

### 错误处理
- 发送失败自动重试最多3次
- 所有重试失败后记录错误日志
- 队列系统故障时自动回退到同步发送

## 性能监控

### 检查队列状态
```bash
# 查看待处理任务数量
php artisan tinker --execute="echo 'Jobs in queue: ' . DB::table('jobs')->count();"

# 查看失败任务
php artisan queue:failed
```

### 日志监控
```bash
# 实时查看邮件发送日志
tail -f storage/logs/laravel.log | grep email
```

### 关键日志信息
- `Verification code email queued successfully` - 邮件成功加入队列
- `Processing email job` - 开始处理邮件任务
- `Verification code email sent successfully` - 邮件发送成功

## 生产环境建议

### 1. 进程管理
使用进程管理工具确保队列工作进程持续运行：

**Supervisor 配置示例**:
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
```

### 2. 性能调优
- **多进程**: 启动多个队列工作进程提高并发处理能力
- **Redis**: 生产环境建议使用Redis作为队列驱动
- **监控**: 设置队列长度监控和告警

### 3. 备份方案
```env
# 如果需要临时禁用队列，改回同步模式
QUEUE_CONNECTION=sync
```

## 故障排除

### 队列不工作
1. 检查数据库连接
2. 确认jobs表已创建
3. 检查队列工作进程是否运行

### 邮件发送慢
1. 检查SMTP服务器响应时间
2. 优化邮件模板大小
3. 考虑使用专业邮件服务（如SendGrid、Mailgun）

### 内存泄漏
队列工作进程长时间运行可能导致内存泄漏，建议：
- 设置 `--max-time` 参数定期重启
- 监控内存使用情况
- 使用进程管理工具自动重启

## 扩展功能

### 邮件模板缓存
```php
// 可以考虑缓存邮件模板提高性能
Cache::remember('email_template_verification', 3600, function() {
    return view('emails.verification')->render();
});
```

### 批量发送
```php
// 批量发送邮件
$emails = ['<EMAIL>', '<EMAIL>'];
foreach ($emails as $email) {
    EmailService::sendVerificationCodeAsync($email, $code, 'register');
}
```

### 优先级队列
```php
// 高优先级邮件
SendVerificationEmail::dispatch($email, $code, $type)->onQueue('high');

// 普通优先级邮件
SendVerificationEmail::dispatch($email, $code, $type)->onQueue('default');
```

---

**总结**: 通过实施队列系统，邮件发送响应时间从6-10秒优化到不到1秒，大大提升了用户体验。
