<?php

namespace App\Admin\Forms\IpoOrder;

use App\Models\ExchangeRate;
use App\Models\Ipo;
use App\Models\IpoOrder;
use App\Models\Users;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;


class ChangeNumForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $zhongqianshu = $input['zhongqianshu'] ?? 0;


        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!is_numeric($zhongqianshu)) {
            return $this->response()->error('中签股数不是数字类型');
        }

        if ($zhongqianshu <= 0) {
            return $this->response()->error('中签股数必须要大于0');
        }


        DB::beginTransaction();
        try {
            $ipoOrder = IpoOrder::find($id);
            if (!$ipoOrder) {
                throw  new \Exception('数据不存在');
            }
            if ($ipoOrder->state != 0) {
                throw  new \Exception('不是申购中，不允许改签');
            }
            // 查询新股信息
            $ipo = Ipo::query()->where('id', $ipoOrder->ipo_id)->first();

            $buy_price = $ipoOrder->buy_price;

            // 如果不是德国和西班牙的IPO
            if (!in_array($ipo->country, ['de', 'sp'])) {
                // 获取当前产品的货币与欧元的汇率
                $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                    ->where('to_currency', 'EUR')
                    ->firstOrFail();

                $users = Users::query()->where('id', $ipoOrder->user_id)->first();

                // 换算金额和费用
                $buy_price = calculateEuroCost($buy_price, $exchangeRate->rate, $users->spread > 0 ? $users->spread : 0.00036);
            }

            $money = $buy_price * $zhongqianshu;
            $rate = $ipoOrder->buy_in_fee_rate;
            $fees = buy_commission($money, $rate);

            $allAmount = $money + $fees;

            $ipoOrder->zhongqianshu = $zhongqianshu;
            $ipoOrder->yingrenjiao = $allAmount;
            $ipoOrder->xurenjiao = $allAmount;
            $ipoOrder->save();
            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');

        $this->number('zhongqianshu', '中签股数')->required()->rules('required|numeric|min:1', [
            'required' => '请输入中签股数',
            'numeric' => '中签股数',
            'min' => '中签股数必须要大于0',
        ])->help('这里的设置是为了自动公布中签用的');
    }
}
