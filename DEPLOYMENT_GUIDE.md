# 宝塔面板部署指南

## 1. 数据库迁移

### 方法一：宝塔终端（推荐）
1. **进入宝塔面板** → **文件** → **终端**
2. **进入项目目录**：
   ```bash
   cd /www/wwwroot/sk_etoro_v2
   ```
3. **执行迁移**：
   ```bash
   php artisan migrate
   ```

### 方法二：phpMyAdmin手动执行
如果无法使用终端，可以通过宝塔面板的phpMyAdmin执行SQL：
```sql
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 2. 宝塔面板队列配置

### Supervisor守护进程设置

1. **进入宝塔面板**
2. **软件商店** → **已安装** → **Supervisor管理器**
3. **添加守护进程**，填写以下信息：

**配置参数**：
- **名称**：`laravel-email-queue`
- **启动用户**：`www`
- **运行目录**：`/www/wwwroot/sk_etoro_v2`
- **启动命令**：`php artisan queue:work --sleep=3 --tries=3 --max-time=3600 --timeout=60`
- **进程数量**：`1`（可根据需要调整为2-3个）

**命令参数说明**：
- `--sleep=3`：无任务时休眠3秒
- `--tries=3`：失败重试3次
- `--max-time=3600`：1小时后重启进程（防止内存泄漏）
- `--timeout=60`：单个任务超时60秒

### 管理队列进程

在宝塔面板的Supervisor管理器中可以：
- **启动/停止**：控制队列进程运行
- **重启**：重新启动队列进程
- **查看日志**：监控队列运行状态
- **查看状态**：检查进程是否正常运行

## 3. 验证部署

### 检查数据库表
在宝塔面板终端中执行：
```bash
cd /www/wwwroot/sk_etoro_v2
php artisan tinker --execute="echo 'Jobs table exists: ' . (Schema::hasTable('jobs') ? 'Yes' : 'No');"
```

### 测试邮件队列
```bash
# 发送测试邮件
php artisan email:send-test <EMAIL>

# 检查队列任务数量
php artisan tinker --execute="echo 'Jobs in queue: ' . DB::table('jobs')->count();"
```

### 监控队列状态
- **宝塔面板**：Supervisor管理器中查看进程状态
- **日志文件**：`storage/logs/laravel.log`
- **命令行**：
  ```bash
  # 查看失败的任务
  php artisan queue:failed

  # 查看队列统计
  php artisan queue:monitor
  ```

## 4. 故障排除

### 常见问题

**队列任务不执行**：
1. 在宝塔面板Supervisor中检查进程状态
2. 查看进程日志是否有错误
3. 检查数据库连接是否正常

**邮件发送失败**：
1. 检查 `.env` 中的SMTP配置
2. 查看 `storage/logs/laravel.log` 邮件日志
3. 测试SMTP服务器连接

**权限问题**：
在宝塔面板终端中执行：
```bash
cd /www/wwwroot/sk_etoro_v2
chown -R www:www storage bootstrap/cache
chmod -R 775 storage bootstrap/cache
```

### 监控建议
- 定期检查Supervisor中的进程状态
- 监控 `storage/logs/laravel.log` 日志文件
- 设置队列任务数量告警（超过100个待处理任务时）

---

**部署检查清单**：
- ✅ 数据库迁移完成
- ✅ Supervisor守护进程已启动
- ✅ 测试邮件发送成功
- ✅ 队列进程正常运行
- ✅ 日志文件无错误信息
