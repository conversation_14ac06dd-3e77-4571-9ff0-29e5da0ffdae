# Email Configuration for Better Deliverability
# Copy these settings to your .env file

# Mail Driver
MAIL_MAILER=smtp

# SMTP Configuration
MAIL_HOST=server.cloudcone.email
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=q@mwKqNBd4QpYLF
MAIL_ENCRYPTION=tls

# From Address
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Vyntor"

# Application Name (used in email headers)
APP_NAME="Vyntor"

# Email Logo Configuration
# Logo display mode: base64|external|css|text
EMAIL_LOGO_MODE=base64

# External logo URL (only used when EMAIL_LOGO_MODE=external)
# EMAIL_LOGO_URL=https://your-cdn.com/logo.png

# Additional Email Settings (optional)
# MAIL_LOG_CHANNEL=stack
# MAIL_TIMEOUT=30

# For testing purposes, you can use log driver to see emails in logs
# MA<PERSON>_MAILER=log

# For production, consider using services like:
# - Mailgun: MAIL_MAILER=mailgun
# - Amazon SES: MAIL_MAILER=ses
# - Postmark: MAIL_MAILER=postmark
