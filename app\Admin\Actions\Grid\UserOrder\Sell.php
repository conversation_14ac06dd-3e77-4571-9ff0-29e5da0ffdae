<?php

namespace App\Admin\Actions\Grid\UserOrder;


use App\Models\UserOrder;
use App\Services\OrderService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class Sell extends RowAction
{
    /**
     * @return string
     */
    protected $title = '强制平仓';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        try {
            $model = UserOrder::findOrFail($id);

            if ($model->status != 1) {
                throw new \Exception('订单不是持仓中，无法强制平仓');
            }

            // 根据订单类型选择不同的平仓服务
            if ($model->trade_id > 0) {
                // 跟单订单使用TraderOrderService
                $res = \App\Services\TraderOrderService::closeTraderOrder($model->id, 2);
            } else {
                // 自主订单使用OrderService
                $res = OrderService::sell($model->user_id, $model->id, 2);
            }

            return $this->response()->success('强制平仓成功')->refresh();

        } catch (\Exception $e) {
            return $this->response()->error('强制平仓失败:' . $e->getMessage())->refresh();
        }
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确定强制平仓', ''];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }

}
