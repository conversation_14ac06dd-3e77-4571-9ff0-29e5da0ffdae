<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\IpoOrder\ChangeNum;
use App\Admin\Actions\Grid\IpoOrder\Primary;
use App\Admin\Actions\Grid\IpoOrder\Deduction;
use App\Admin\Actions\Grid\IpoOrder\Check;
use App\Admin\Actions\Grid\IpoOrder\QzCheck;
use App\Admin\Renderable\UserTable;
use App\Admin\Renderable\IpoProductTable;
use App\Admin\Repositories\IpoOrder;
use App\Models\IpoOrder as IpoOrderModel;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class IpoOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new IpoOrder(), function (Grid $grid) {


            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user', 'ipo']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }
            $model->orderBy('id', 'desc');


            $grid->column('id')->sortable();
            $grid->column('order_sn');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('ipo.name', '名称');
            $grid->column('ipo.symbol', '股票代码');
            $grid->column('stamp_duty', '印花税');
            $grid->column('exchange_rate', '货币兑换汇率(买入时)');
            $grid->column('user_spread', '用户汇率价差');
            $grid->column('buy_num');
            $grid->column('buy_price');
            $grid->column('zhongqianshu');
            $grid->column('xurenjiao');
            $grid->column('yingrenjiao');
            $grid->column('yirenjiao');
            $grid->column('renjiaocishu');
            $grid->column('state')->using([0 => '申购中', 1 => '已中签', 2 => '未中签'])->dot(
                [
                    0 => 'primary',
                    1 => 'success',
                    2 => 'danger',
                ],
            );
            $grid->column('pay_state')->using([0 => '未支付', 1 => '支付成功', 2 => '支付中'])->label([
                0 => 'danger',
                1 => 'success',
                2 => 'primary',
            ]);
            $grid->column('check_state')->using([0 => '待审核', 1 => '通过', 2 => '审核拒绝'])->label(
                [
                    0 => '#c9cccf',
                    1 => 'success',
                    2 => 'danger',
                ],
            );
            $grid->column('grant_state')->using([0 => '未持仓', 1 => '已持仓', 2 => '已退款'])->label(
                [
                    0 => '#c9cccf',
                    1 => 'success',
                    2 => '#cba640',
                ],
            );;
//            $grid->column('is_show')->using([0 => '否', 1 => '是']);
            $grid->column('buy_time');
//            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->fixColumns(5, -1);
            $grid->setActionClass(Grid\Displayers\Actions::class);
            $grid->actions(function ($actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->append(new ChangeNum()); // 改签
                $actions->append(new Primary()); // 公布中签
                $actions->append(new Deduction()); // 新股扣费
                $actions->append(new Check()); // 审核
                $actions->append(new QzCheck()); // 强制审核，只能通过买了部分数量的股票
            });


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new IpoOrder(), function (Show $show) {
            $show->disableEditButton();
            $show->disableDeleteButton();
            $show->field('id');
            $show->field('ipo_id');
            $show->field('user_id');
            $show->field('order_sn');
            $show->field('buy_num');
            $show->field('buy_price');
            $show->field('zhongqianshu');
            $show->field('xurenjiao');
            $show->field('yingrenjiao');
            $show->field('yirenjiao');
            $show->field('renjiaocishu');
            $show->field('state');
            $show->field('pay_state');
            $show->field('check_state');
            $show->field('grant_state');
            $show->field('is_show');
            $show->field('buy_time');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new IpoOrder(), function (Form $form) {
            $form->display('id');
            $form->selectTable('ipo_id')
                ->from(IpoProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(IpoOrderModel::class, 'id', 'name') // 设置编辑数据显示
                ->required()->rules('required', ['required' => '请选择一个新股']);
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()->rules('required')->rules('required', ['required' => '请选择用户'])->help('<span style="color: red">请先选用户，否则无法计算手续费。如果多次切换用户，需要重新填写申购价格或申购股数、杠杆，否则无法计算</span>');
//            $form->text('order_sn');
            $form->text('buy_price')->required()->rules('required|numeric|min:0.01', ['required' => '请输入申购价格', 'numeric' => '申购价格必须是数字', 'min' => '申购价格不得低于0.01']);
            $form->text('buy_num')
                ->required()
                ->rules('required|numeric|min:1', ['required' => '请输入申购股数', 'numeric' => '申购股数必须是数字', 'min' => '申购股数不得低于0.01']);
//            $form->text('zhongqianshu');
            $form->text('xurenjiao')
                ->readOnly()->help('用户中签后，需要支付的金额。用户没有支付过，则与中签应认缴相同');
            $form->text('yingrenjiao')
                ->readOnly()
                ->help('中签后实际应该支付的总金额');
//            $form->text('yirenjiao');
//            $form->text('renjiaocishu');
//            $form->text('state');
//            $form->text('pay_state');
//            $form->text('check_state');
//            $form->text('grant_state');
//            $form->text('is_show');
            $form->datetime('buy_time')->default(date('Y-m-d H:i:s'));

            $form->display('created_at');
            $form->display('updated_at');

            Admin::script(<<<JS
function updateYingRenJiao() {
    var buy_price = $('input[name="buy_price"]').val();
    var buy_num = $('input[name="buy_num"]').val();
    var user_id = $('input[name="user_id"]').val();

    if (buy_price && buy_num) {
        $.ajax({
            url: '/admin/user-order/calc-fees',
            type: 'POST',
            data: {
                _token: Dcat.token,
                user_id: user_id,
                buy_price: buy_price,
                buy_quantity: buy_num,
                lever: 1
            },
            success: function (res) {
                if (res.status) {
                    var total = buy_price * buy_num + res.data.buy_fees;
                    $('input[name="yingrenjiao"]').val(total);
                    $('input[name="xurenjiao"]').val(total);
                } else {
                    Dcat.error(res.message || '计算失败');
                }
            }
        });
    }
}

$('input[name="buy_price"], input[name="buy_num"]').on('input', updateYingRenJiao);
JS
            );

        });
    }
}
