# 🔍 产品搜索API接口文档

## 🎯 **功能概述**

为前端提供产品搜索功能，支持按产品代码（symbol）或产品名称（name）进行精确搜索，返回实时数据，不分页。

## 📊 **接口列表**

### 1. 产品搜索 - `/api/product/search`
### 2. 搜索建议 - `/api/product/suggestions`

## 🔍 **1. 产品搜索接口**

### 请求信息
```http
GET /api/product/search
```

### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `keyword` | string | 是 | 搜索关键词（产品代码或名称，精确匹配） |
| `pro_type` | integer | 否 | 产品类型筛选 |
| `limit` | integer | 否 | 返回数量限制，默认50 |

### 产品类型说明
- `1` = 股票
- `2` = 大宗商品
- `3` = ETF
- `4` = 外汇
- `5` = 数字货币

### 请求示例
```bash
# 精确搜索苹果股票
GET /api/product/search?keyword=AAPL

# 搜索股票类型的产品
GET /api/product/search?keyword=Apple Inc.&pro_type=1

# 限制返回数量
GET /api/product/search?keyword=TSLA&limit=10
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 123,
            "pro_type": 1,
            "pro_type_text": "股票",
            "symbol": "AAPL",
            "name": "Apple Inc.",
            "currency": "USD",
            "exchange": "NASDAQ",
            "country": "United States",
            "country_text": "美国",
            "price": 150.25,
            "open": 148.50,
            "high": 152.00,
            "low": 147.80,
            "close": 150.25,
            "volume": 45678900,
            "previous_close": 147.75,
            "change": 2.50,
            "percent_change": 1.69,
            "is_market_open": true,
            "is_hot": true,
            "logo": "/images/aapl.png",
            "display_text": "AAPL - Apple Inc."
        }
    ]
}
```

## 💡 **2. 搜索建议接口**

### 请求信息
```http
GET /api/product/suggestions
```

### 请求参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `pro_type` | integer | 否 | 产品类型筛选 |
| `limit` | integer | 否 | 返回数量，默认10 |

### 请求示例
```bash
# 获取热门产品建议
GET /api/product/suggestions

# 获取热门股票建议
GET /api/product/suggestions?pro_type=1&limit=5
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 123,
            "pro_type": 1,
            "pro_type_text": "股票",
            "symbol": "AAPL",
            "name": "Apple Inc.",
            "currency": "USD",
            "price": 150.25,
            "change": 2.50,
            "percent_change": 1.69,
            "logo": "/images/aapl.png",
            "display_text": "AAPL - Apple Inc."
        }
    ]
}
```

## 🎯 **搜索逻辑说明**

### 搜索条件
1. **精确搜索**：`symbol = keyword` OR `name = keyword`
2. **状态筛选**：只搜索启用的产品（`state = 1`）
3. **删除筛选**：排除已删除的产品（`deleted_at IS NULL`）
4. **实时数据**：调用三方数据源获取最新价格和交易数据

### 排序规则
1. **权重优先**：`weight DESC`
2. **热门程度**：`is_hot DESC`
3. **ID倒序**：`id DESC`

### 筛选条件
- **产品类型**：精确匹配 `pro_type`

### 实时数据更新
- **股票产品**：调用江山服务（JiangshanService）获取实时数据
- **其他产品**：调用TwelveData服务获取实时数据
- **数据字段**：price, open, high, low, close, volume, previous_close, change, percent_change
- **错误处理**：单个产品数据获取失败不影响其他产品

## 📱 **前端使用示例**

### 搜索框实现
```javascript
// 搜索产品
const searchProducts = async (keyword, filters = {}) => {
    const params = new URLSearchParams({
        keyword,
        ...filters
    });
    
    const response = await fetch(`/api/product/search?${params}`);
    const result = await response.json();
    
    if (result.code === 200) {
        return result.data;
    } else {
        throw new Error(result.message);
    }
};

// 使用示例
searchProducts('AAPL', { pro_type: 1 })
    .then(data => {
        console.log('搜索结果:', data.products);
        console.log('分页信息:', data.pagination);
    })
    .catch(error => {
        console.error('搜索失败:', error.message);
    });
```

### 搜索建议实现
```javascript
// 获取搜索建议
const getSearchSuggestions = async (proType = null) => {
    const params = new URLSearchParams();
    if (proType) params.append('pro_type', proType);
    params.append('limit', '8');
    
    const response = await fetch(`/api/product/suggestions?${params}`);
    const result = await response.json();
    
    if (result.code === 200) {
        return result.data;
    } else {
        throw new Error(result.message);
    }
};

// 显示热门推荐
getSearchSuggestions(1) // 获取热门股票
    .then(suggestions => {
        suggestions.forEach(product => {
            console.log(`${product.symbol} - ${product.name}`);
        });
    });
```

### 搜索界面组件
```html
<!-- 搜索框 -->
<div class="search-container">
    <input type="text" 
           id="search-input" 
           placeholder="搜索产品代码或名称..."
           onkeyup="handleSearch(event)">
    
    <!-- 产品类型筛选 -->
    <select id="product-type">
        <option value="">全部类型</option>
        <option value="1">股票</option>
        <option value="2">大宗商品</option>
        <option value="3">ETF</option>
        <option value="4">外汇</option>
        <option value="5">数字货币</option>
    </select>
    
    <button onclick="performSearch()">搜索</button>
</div>

<!-- 搜索结果 -->
<div id="search-results"></div>

<!-- 热门推荐 -->
<div id="hot-suggestions">
    <h3>热门推荐</h3>
    <div id="suggestion-list"></div>
</div>
```

## 🚀 **性能优化**

### 数据库索引
确保以下字段有索引：
- `symbol` - 产品代码搜索
- `state` - 状态筛选
- `pro_type` - 产品类型筛选
- `weight` - 排序优化
- `is_hot` - 热门筛选
- `deleted_at` - 软删除筛选

### 搜索优化
- **关键词长度限制**：1-50字符
- **分页限制**：最大100条/页
- **缓存策略**：热门推荐可缓存5-10分钟

## 🛡️ **安全机制**

### 输入验证
- 关键词长度验证（1-50字符）
- 产品类型枚举验证
- 分页参数范围验证

### SQL注入防护
- 使用Laravel的查询构建器
- 参数绑定防止SQL注入
- LIKE查询转义特殊字符

## 📊 **错误码说明**

| 错误码 | 说明 |
|--------|------|
| 200 | 搜索成功 |
| 422 | 参数错误（关键词为空或过长） |
| 500 | 服务器错误 |

### 错误响应示例
```json
{
    "code": 422,
    "message": "搜索关键词不能为空",
    "data": null
}
```

## 🎯 **使用场景**

1. **产品搜索页面**：用户主动搜索产品
2. **交易下单页面**：选择交易产品
3. **产品选择器**：各种需要选择产品的场景
4. **自动完成**：输入时的实时建议
5. **热门推荐**：首页或搜索页的推荐展示

现在您有了完整的产品搜索API，支持按代码和名称搜索，并提供丰富的筛选和排序功能！🔍
