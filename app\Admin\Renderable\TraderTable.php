<?php

namespace App\Admin\Renderable;


use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Trader;

class TraderTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new Trader(), function (Grid $grid) {

            $model = $grid->model();
            // 获取当前登录的管理员账号
            $currentAdminUserId = Admin::user()->id;

            $model->where('admin_id', $currentAdminUserId);
            $model->where('state', 1);

            $grid->column('name');
            $grid->column('avatar', '头像')->image('', 100, 100);


            $grid->quickSearch(['id', 'name']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name')->width(4);
            });
        });
    }
}
