<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminIpWhitelist
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 检查是否启用IP白名单验证
        if (!env('ADMIN_IP_WHITELIST_ENABLED', false)) {
            return $next($request);
        }

        // 获取客户端IP
        $clientIp = $this->getClientIp($request);
        
        // 获取白名单IP列表
        $whitelist = $this->getWhitelistIps();
        
        // 检查IP是否在白名单中
        if (!$this->isIpAllowed($clientIp, $whitelist)) {
            // 记录未授权访问日志
            \Log::warning('管理后台未授权IP访问', [
                'ip' => $clientIp,
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'time' => now()->toDateTimeString()
            ]);
            
            // 返回403错误
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => '访问被拒绝：IP地址不在白名单中'
                ], 403);
            }
            
            return response()->view('errors.403', [
                'message' => '访问被拒绝：您的IP地址不在允许访问的白名单中'
            ], 403);
        }

        return $next($request);
    }

    /**
     * 获取客户端真实IP
     */
    private function getClientIp(Request $request)
    {
        // 优先获取代理后的真实IP
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($headers as $header) {
            $ip = $request->server($header);
            if (!empty($ip) && $ip !== 'unknown') {
                // 处理多个IP的情况（取第一个）
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                // 验证IP格式（包括私有IP）
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * 获取白名单IP列表
     */
    private function getWhitelistIps()
    {
        $whitelist = env('ADMIN_IP_WHITELIST', '127.0.0.1,::1');
        return array_map('trim', explode(',', $whitelist));
    }

    /**
     * 检查IP是否在白名单中
     */
    private function isIpAllowed($clientIp, $whitelist)
    {
        foreach ($whitelist as $allowedIp) {
            if ($this->matchIp($clientIp, $allowedIp)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 匹配IP地址（支持CIDR格式）
     */
    private function matchIp($clientIp, $allowedIp)
    {
        // 精确匹配
        if ($clientIp === $allowedIp) {
            return true;
        }

        // CIDR格式匹配
        if (strpos($allowedIp, '/') !== false) {
            list($subnet, $mask) = explode('/', $allowedIp);
            
            // IPv4 CIDR
            if (filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                $clientLong = ip2long($clientIp);
                $subnetLong = ip2long($subnet);
                $maskLong = -1 << (32 - (int)$mask);
                
                return ($clientLong & $maskLong) === ($subnetLong & $maskLong);
            }
            
            // IPv6 CIDR（简单实现）
            if (filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
                // 简化的IPv6匹配，实际项目中可能需要更复杂的实现
                return strpos($clientIp, substr($subnet, 0, strpos($subnet, '::', 2))) === 0;
            }
        }

        return false;
    }
}
