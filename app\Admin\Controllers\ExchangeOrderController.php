<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\ExchangeOrder;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ExchangeOrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ExchangeOrder(), function (Grid $grid) {
            $grid->disableEditButton();

            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('order_sn');
            $grid->column('from_currency');
            $grid->column('to_currency');
            $grid->column('from_money');
            $grid->column('rate');
            $grid->column('to_money');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ExchangeOrder(), function (Show $show) {
            $show->disableEditButton();
            $show->field('id');
            $show->field('user_id');
            $show->field('order_sn');
            $show->field('from_currency');
            $show->field('to_currency');
            $show->field('from_money');
            $show->field('rate');
            $show->field('to_money');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ExchangeOrder(), function (Form $form) {
            $form->disableEditingCheck();
            $form->display('id');
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()->rules('required');
//            $form->display('order_sn');
            $form->select('from_currency')
                ->options(function () {
                    return \App\Models\AreaConfig::where('state', 1)
                        ->distinct()
                        ->pluck('name_show', 'area_code');
                })->required();
            $form->select('to_currency')
                ->options(function () {
                    return \App\Models\AreaConfig::where('state', 1)
                        ->distinct()
                        ->pluck('name_show', 'area_code');
                })->required();
            $form->number('from_money')->required()->attribute('class', 'form-money-input');
            $form->text('rate')->readOnly();
            $form->text('to_money')->readOnly()->help('四舍五入，只保留两位小数点');

            $form->display('created_at');
            $form->display('updated_at');


            // 加载动态计算JS
            $form->html($this->ajaxRateScript());
        });
    }

    protected function ajaxRateScript()
    {
        $url = admin_url('exchange-order_calc-rate'); // 自定义接口地址

        return <<<HTML
<script>
Dcat.ready(function () {
    function fetchRateAndCalc() {
        var from = $('select[name="from_currency"]').val();
        var to = $('select[name="to_currency"]').val();
        var money = $('input[name="from_money"]').val();
        console.log(111)

        if (!from || !to || !money) {
            return;
        }

        $.ajax({
            url: '{$url}',
            type: 'POST',
            data: {
                _token: Dcat.token,
                from_currency: from,
                to_currency: to,
                from_money: money
            },
            success: function (res) {
                if (res.status) {
                    $('input[name="rate"]').val(res.data.rate);
                    $('input[name="to_money"]').val(res.data.to_money);
                } else {
                    Dcat.error(res.message);
                }
            }
        });
    }

    $('select[name="from_currency"], select[name="to_currency"]').on('change', fetchRateAndCalc);
    $('.form-money-input').on('input', fetchRateAndCalc);
});
</script>
HTML;
    }


    public function calcRate()
    {
        $from = request('from_currency');
        $to = request('to_currency');
        $money = (float)request('from_money');

        if ($from == $to) {
            return response()->json([
                'status' => false,
                'message' => '兑换币种和目标币种不能相同',
            ]);
        }

        $rate = \App\Models\ExchangeRate::where('from_currency', $from)
            ->where('to_currency', $to)
            ->value('rate');

        if (!$rate) {
            return response()->json([
                'status' => false,
                'message' => '找不到该汇率',
            ]);
        }

        return response()->json([
            'status' => true,
            'data' => [
                'rate' => $rate,
                'to_money' => round($money * $rate, 2),
            ]
        ]);
    }

}
