<?php

namespace App\Console\Commands;

use App\Services\JiangshanService;
use Illuminate\Console\Command;
use App\Models\UserOrder;
use App\Models\Product;
use App\Models\UsersInfo;
use App\Models\MoneyChange;
use App\Services\TwelvedataNewService;
use Illuminate\Support\Facades\DB;

class AutoOrderDeal extends Command
{
    protected $signature = 'order:auto-deal';
    protected $description = '自动处理止盈止损、挂单成交、开盘自动买卖';

    public function handle()
    {
        die;
        // 查询所有有可能需要处理的订单
        $orders = UserOrder::whereIn('status', [1, 4])
            ->where(function ($q) {
                $q->where('is_open_buy', 1)
                    ->orWhere('is_open_sell', 1)
                    ->orWhere('take_profit', '>', 0)
                    ->orWhere('stop_loss', '>', 0);
            })
            ->with('product')
            ->limit(200)
            ->get();

        if ($orders->isEmpty()) return;

        // 获取所有相关产品，并批量拉行情（会写入product表）
        $productIds = $orders->pluck('product_id')->unique()->values()->all();
        $products = Product::whereIn('id', $productIds)->get();

        if ($products->pro_type == 1) {
            $Jiangshan = new JiangshanService();
            $Jiangshan->get_new_data_serices_list($products);
        } else {
            TwelvedataNewService::quoteByBatch($products);
        }

//        TwelvedataNewService::quoteByBatch($products);

        // 取最新行情数据
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

        foreach ($orders as $order) {
            // 用最新行情
            $product = $products[$order->product_id] ?? null;
            if (!$product) continue;

            try {
                DB::beginTransaction();
                // 再次加锁取订单，避免并发
                $row = UserOrder::where('id', $order->id)->lockForUpdate()->first();
                if (!$row || !in_array($row->status, [1, 4])) {
                    DB::rollBack();
                    continue;
                }

                // 1. 处理挂单自动成交
                if ($row->is_open_buy == 1 && $row->status == 4) {
                    $deal = false;
                    if ($row->buy_type == 1 && $product->is_market_open == 1) $deal = true;
                    if ($row->buy_type == 2 && $product->is_market_open == 1) {
                        if ($row->direction_data == 1 && $product->price >= $row->buy_price) $deal = true;
                        if ($row->direction_data == 2 && $product->price <= $row->buy_price) $deal = true;
                    }
                    if ($deal) {
                        $row->status = 1;
                        $row->is_open_buy = 0;
                        $row->save();
                        DB::commit();
                        continue;
                    }
                }

                // 2. 自动止盈止损平仓
                if ($row->status == 1) {
                    $profit = $row->direction_data == 1
                        ? round(($product->price - $row->buy_price) * $row->buy_quantity, 2)
                        : round(($row->buy_price - $product->price) * $row->buy_quantity, 2);

                    $closeType = 0;
                    // 基于价格的止盈止损判断
                    if ($row->direction_data == 1) {
                        // 买涨：止盈价格高于买入价，止损价格低于买入价
                        if ($row->take_profit > 0 && $row->take_profit > $row->buy_price && $product->price >= $row->take_profit) {
                            $closeType = 1; // 止盈
                        }
                        if ($row->stop_loss > 0 && $row->stop_loss < $row->buy_price && $product->price <= $row->stop_loss) {
                            $closeType = 2; // 止损
                        }
                    } else if ($row->direction_data == 2) {
                        // 买跌：止盈价格低于买入价，止损价格高于买入价
                        if ($row->take_profit > 0 && $row->take_profit < $row->buy_price && $product->price <= $row->take_profit) {
                            $closeType = 1; // 止盈
                        }
                        if ($row->stop_loss > 0 && $row->stop_loss > $row->buy_price && $product->price >= $row->stop_loss) {
                            $closeType = 2; // 止损
                        }
                    }
                    if ($closeType) {
                        $this->autoCloseOrder($row, $product, $profit, $closeType);
                        DB::commit();
                        continue;
                    }
                }

                // 3. 自动开盘卖出
                if ($row->is_open_sell == 1 && $product->is_market_open == 1 && $row->status == 1) {
                    $profit = $row->direction_data == 1
                        ? round(($product->price - $row->buy_price) * $row->buy_quantity, 2)
                        : round(($row->buy_price - $product->price) * $row->buy_quantity, 2);
                    $this->autoCloseOrder($row, $product, $profit, 3);
                    DB::commit();
                    continue;
                }

                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error('自动撮合失败:' . $e->getMessage());
                continue;
            }
        }
    }

    /**
     * 自动平仓操作
     * @param UserOrder $order
     * @param Product $product
     * @param float $profit
     * @param int $closeType 1:止盈 2:止损 3:自动开盘
     */
    private function autoCloseOrder($order, $product, $profit, $closeType)
    {
        $sellMoney = $order->buy_cost + $profit;
        if ($sellMoney <= 0) $sellMoney = 0.01;
        $sell_fees = sell_commission($sellMoney, $order->user_id);
        $sxf = $sell_fees['sxf'];
        $see_fee = $sell_fees['see_fees'];
        $pingcangjine = $sellMoney - $sxf - $see_fee;
        if ($pingcangjine <= 0) $pingcangjine = 0.01;

        $user_info = UsersInfo::where('user_id', $order->user_id)->lockForUpdate()->first();
        $balance_type = strtolower($product->currency ?? "USD") . '_balance';
        $dongjie_type = strtolower($product->currency ?? "USD") . '_dongjie';
        $before = $user_info->$balance_type;
        $user_info->$balance_type += $pingcangjine;
        $user_info->$dongjie_type -= $order->buy_cost + $order->buy_fees;
        $user_info->save();

        $order->status = 2;
        $order->sell_price = $product->price;
        $order->sell_quantity = $order->buy_quantity;
        $order->sell_money = $pingcangjine;
        $order->profit_amount = $profit;
        $order->sell_fees = $sxf;
        $order->see_fee = $see_fee;
        $order->sell_time = time();
        $order->sell_type = $closeType;
        $order->is_open_sell = 0;
        $order->save();

        MoneyChange::create([
            'money_type' => 'EUR',
            'user_id' => $order->user_id,
            'order_sn' => $order->order_sn,
            'product_name' => $product->name,
            'detailed' => "自动平仓|类型:{$closeType}|盈亏:{$profit}|手续费:{$sxf}|证交税:{$see_fee}",
            'type' => MoneyChange::TYPE_TRADE,
            'before_money' => $before,
            'money' => $pingcangjine,
            'after_money' => $before + $pingcangjine,
        ]);
    }
}
