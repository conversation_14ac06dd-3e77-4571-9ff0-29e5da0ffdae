<?php

namespace App\Admin\Repositories;

use App\Models\Product as Model;
use App\Services\JiangshanService;
use App\Services\TwelvedataNewService;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Grid;


class ProductStock extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function get(Grid\Model $model)
    {

        $query = parent::get($model);

        $country = request()->get('country', 'de');

//
        if (!$query->isEmpty()) {
            $jiangshan = new JiangshanService();
            $updateResult = $jiangshan->get_new_data_serices_list($query->getCollection(), $country);
            $query->setCollection($updateResult);
        }

        return $query;
    }

}
