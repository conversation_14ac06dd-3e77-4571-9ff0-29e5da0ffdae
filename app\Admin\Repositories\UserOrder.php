<?php

namespace App\Admin\Repositories;

use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\UserOrder as Model;
use App\Models\UsersInfo;
use App\Services\JiangshanService;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\Product;

class UserOrder extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 新增记录.
     *
     * @param Form $form
     * @return mixed
     */
    public function store(Form $form)
    {
        $result = null;

        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();

            // 获取当前产品
            $product = Product::where('id', $updates['product_id'])
                ->where('deleted_at', null)
                ->where('state', 1)
                ->first();
            if (!$product) {
                throw new \Exception('产品不存在或状态不合法');
            }

            if ($product->pro_type == 1) {

                // 拉取最新行情
                $jiangshan = new JiangshanService();
                $product = $jiangshan->get_new_data_serices($product);
            } else {
                // 拉取最新行情
                $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
                $product = Product::find($updates['product_id']);
            }

            $is_open_buy = 1; // 后台添加的，都按开盘中买入

            $yinkui = ($product->price - $updates['buy_price']) * $updates['buy_quantity'];

            $userid = $updates['user_id'];

            // 根据会员获取当前的费率
            $rate = get_buy_fee_rate($userid);

            // 计算成本
            $buy_cost = round(($updates['buy_price'] * $updates['buy_quantity']) / $updates['lever'], 2);
            if ($buy_cost <= 0) throw new \Exception('买入成本必须大于0');
            $buy_fees = buy_commission($buy_cost, $rate); // 手续费

            // 整理持仓订单的数据
            $out_trade_no = generateUniqueOrderNumber();
            $updates['order_sn'] = $out_trade_no;
            $updates['product_type'] = $product->pro_type;
            $updates['buy_type'] = 2; // 限价买入
            $updates['buy_fees'] = $buy_fees; // 手续费
            $updates['buy_fees_rate'] = $rate; // 手续费费率
            $updates['buy_cost'] = $buy_cost; // 成本
            $updates['buy_time'] = strtotime($updates['buy_time']); // 买入时间
            $updates['take_profit'] = $updates['take_profit'] ?? 0; // 止赢
            $updates['stop_loss'] = $updates['stop_loss'] ?? 0; // 止损
            $updates['stop_loss'] = $updates['stop_loss'] ?? 0; // 止损
            $updates['status'] = 1; // 持仓中
            $updates['is_open_buy'] = $is_open_buy; // 开盘，未开盘
            $updates['market_cap'] = $product->price * $updates['buy_quantity']; // 当前市值
            $updates['profit_amount'] = $yinkui; // 赢亏

//            dd($updates);

            $userInfo = UsersInfo::where('user_id', $userid)->lockForUpdate()->first();
            if (!$userInfo) throw new \Exception('用户资金信息不存在');

            // todo: 西班牙市值超过10亿欧元的股票需要加多一个印花税
            // 印花税：0.2%
            $stamp_duty = 0;
            if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
                $stamp_duty = round($buy_cost * 0.002, 2); // 印花税
            }

            $user = \App\Models\Users::query()->where('id', $userid)->first();

            $exchange_rate = 1;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                if (in_array($product->pro_type, [2, 3, 4, 5])) {
                    // todo: 调用接口获取一次实时的接口

                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                } else {
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }

                // 本金
                $buy_cost = calculateEuroCost($buy_cost, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                // 手续费
                $buy_fees = calculateEuroCost($buy_fees, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                // 印花税
                $stamp_duty = calculateEuroCost($stamp_duty, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                $exchange_rate = $exchangeRate->rate;
            }

            $updates['stamp_duty'] = $stamp_duty;
            $updates['exchange_rate'] = $exchange_rate;
            $updates['user_spread'] = $user->spread > 0 ? $user->spread : 0.00036;

            $before = $userInfo->eur_balance;

            // 买入本金 = 买入成本 + 手续费 + 印花税
            $freeze_total = $buy_cost + $buy_fees + $stamp_duty;
            if ($userInfo->eur_balance < $freeze_total) {
                throw new \Exception(__('用户资金不足，无法进行买入'));
            }

            // 扣减，冻结金额
            $userInfo->eur_balance = $before - $freeze_total;
            $userInfo->eur_dongjie += $buy_cost;
            $userInfo->save();

            $currency = strtolower($product->currency);
            $updates['currency'] = strtoupper($currency); // 货币

            MoneyChange::create([
                'money_type' => 'EUR',
                'user_id' => $userid,
                'order_sn' => $out_trade_no,
                'product_name' => $product->name,
                'detailed' => "买入订单|代码|{$product->symbol}|产品|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE,
                'before_money' => $before,
                'money' => -$buy_cost,
                'after_money' => $before - $buy_cost,
            ]);
            if ($buy_fees > 0) {
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $userid,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "买入手续费|代码|{$product->symbol}|产品|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE,
                    'before_money' => $before - $buy_cost,
                    'money' => -$buy_fees,
                    'after_money' => $before - $buy_cost - $buy_fees,
                ]);
            }

            if ($stamp_duty > 0) {
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $userid,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "买入印花税|代码|{$product->symbol}|产品|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE,
                    'before_money' => $before - $buy_cost - $buy_fees,
                    'money' => -$stamp_duty,
                    'after_money' => $before - $buy_cost - $buy_fees - $stamp_duty,
                ]);
            }

            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }

            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }

            $result = $model->save();

            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
