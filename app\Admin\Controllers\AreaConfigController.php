<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\AreaConfig;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use App\Models\AreaConfig as AreaConfigModel;

class AreaConfigController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AreaConfig(), function (Grid $grid) {

            $grid->disableDeleteButton();


            $grid->model()->orderBy('state', 'desc')->orderBy('weight', 'desc');

            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('name_cn');
            $grid->column('name_show');
            $grid->column('iso3');
            $grid->column('area_code');
            // 如果 icon_url 已经是完整 URL：
            $grid->column('icon_url')->image();
            $grid->column('symbol');
            $grid->column('state')->switch();

//            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('name');
                $filter->equal('area_code');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AreaConfig(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('name_en', '国家');
            $show->field('iso3');
            $show->field('name_show');
            $show->field('area_code');
            $show->field('icon_url')->image();
            $show->field('symbol');
            $show->field('created_at');
            $show->field('updated_at');

            $show->panel()
                ->tools(function ($tools) {
                    $tools->disableDelete(); // 禁用删除按钮
                });

        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AreaConfig(), function (Form $form) {
            $form->display('id');
            $form->display('name');
            $form->text('name_cn', '国家');
            $form->display('area_code');
            $form->text('symbol');

            $form->text('name_show');
            $form->image('icon_url');

            $form->switch('state');
            // 数字输入框
            $form->number('weight');

            $form->tools(function (Form\Tools $tools) {
                $tools->disableDelete(); // 禁用删除按钮
            });

        });
    }

    /**
     * 获取地区代码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAreaCode(Request $request)
    {
        $areaId = $request->get('q');

        if (!$areaId) {
            return response()->json([]);
        }

        $area = AreaConfigModel::find($areaId);

        if (!$area) {
            return response()->json([]);
        }

        // 返回格式必须符合Dcat Admin select组件的要求
        // 必须返回字符串键值对，而不是数组
        return response()->json([
            $area->area_code => $area->area_code
        ]);
    }
}
