<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTraderFollowerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trader_follower', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('trader_id')->index()->comment('交易员id');
            $table->integer('user_id')->index()->comment('用户id');
            $table->decimal('bet_money', 15, 3)->default(0.00)->comment('投资总金额（客户提交）');
            $table->decimal('used_money', 15, 3)->default(0.00)->comment('已投资金额（已经建仓）');
            $table->tinyInteger('state')->default(1)->comment('关注状态0:未关注，1：关注');
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->index()->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}trader_follower auto_increment=1 comment '关注交易员表'");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trader_follower');
    }
}
