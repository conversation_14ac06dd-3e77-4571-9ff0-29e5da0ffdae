<?php

namespace App\Http\Controllers\Api;

use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\SystemConfig;
use App\Models\UserCard;
use App\Models\UsersInfo;
use App\Models\Withdraw;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class WithdrawController extends ApiBaseController
{

    /**
     * 提现申请
     */
    public function submitWithdraw(Request $request)
    {
        $rules = [
            'amount' => 'required',
            'type' => 'required',
            'pay_pass' => 'required',
            'user_card' => 'required',
        ];
        $this->validate($request, $rules);

        // 时间限制
        $date = date('Y-m-d');
        $withdraw_begin_time = SystemConfig::getValue('withdraw_begin_time');
        $now = time();
        if ($withdraw_begin_time) {
            $begin = $date . ' ' . $withdraw_begin_time;
            $begin_time = strtotime($begin);
            if ($now < $begin_time) return $this->returnMsg(218, __('api.还没到提现时间'));
        }

        $withdraw_end_time = SystemConfig::getValue('withdraw_end_time');
        if ($withdraw_end_time) {
            $end = $date . ' ' . $withdraw_end_time;
            $end_time = strtotime($end);
            if ($now > $end_time) return $this->returnMsg(219, __('api.今天提现时间已结束，请明天再来提现'));
        }

        $data = $request->all();

        // 1：bank  2:usdt
        if (!in_array($data['type'], [1, 2])) {
            return $this->returnMsg(503, __('api.参数错误-1'));
        }

        if ($data['amount'] <= 0) {
            return $this->returnMsg(503, __('api.参数错误-2'));
        }

        // 获取当前登录用户
        $user = $request->user();

        // 次数限制 todo

        // 密码校验
        if (!Hash::check($data['pay_pass'], $user->pay_password)) {
            return $this->returnMsg(504, __('api.支付密码错误'));
        }

//        $count = Withdraw::where('created_at', '>=' ,$date .' 00:00:00')->where('user_id', $user->id)->count();
//        if (isset($daily_withdraw_times) && !empty($daily_withdraw_times)) {
//            if ($count >= $daily_withdraw_times) {
//                return $this->returnMsg(216, __('api.日提款次数达到上限'));
//            }
//        }
        $balanceField = 'eur_balance';
        $dongjieField = 'eur_dongjie';
        $amount = $data['amount'];
        if ($data['type'] == 1) {
            // 最大 最小金额限制
            $min = SystemConfig::getValue('min_withdraw_money');
            $max = SystemConfig::getValue('max_withdraw_money');
            $moneyType = 'eur';
        } else {
            // 最大 最小金额限制
            $min = SystemConfig::getValue('min_usdt_withdraw_money');
            $max = SystemConfig::getValue('max_usdt_withdraw_money');
            $moneyType = 'usd';
        }

        if ($min && $amount < $min) {
            return $this->returnMsg(503, __('api.提款金额不能低于') . ":{$min}");
        }

        if ($max && $amount > $max) {
            return $this->returnMsg(503, __('api.提款金额不能高于') . ":{$max}");
        }
        $exchange_rate = 1;
        if ($data['type'] !== 1) {
            // 获取当前产品的货币与欧元的汇率
            $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                ->where('to_currency', 'EUR')
                ->firstOrFail();

            $amount = calculateEuroCost($amount, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
            $exchange_rate = $exchangeRate->rate;
        }
        DB::beginTransaction();
        try {
            $out_trade_no = 'T' . time() . $user->id . rand(1000, 9999);
            $userInfo = UsersInfo::query()->where('user_id', $user->id)->lockForUpdate()->first();
            $userCard = UserCard::find($data['user_card']);

            if (!$userCard) {
                return $this->returnMsg(503, __('api.银行卡不存在'));
            }

            if ($userCard->user_id != $user->id) {
                return $this->returnMsg(503, __('api.银行卡归属错误,禁止提款'));
            }

            // 每笔提款加收5元手续费
            $fee = SystemConfig::getValue('withdraw_fee');
            if (in_array($user->id, [1,2,3])) { // 1,2,3 是测试账号
                $fee = 0;
            }

            if ($userInfo->$balanceField < $amount + $fee) {
                return $this->returnMsg(503, __('api.账户金额不足'));
            }

            $beforBalance = $userInfo->$balanceField;
            $afterBalance = $beforBalance - $amount;

            // 开始扣减余额
            $userInfo->$balanceField -= $amount + $fee;
            $userInfo->$dongjieField += $amount;
            $userInfo->save();

            // 添加提现记录
            Withdraw::create([
                'user_id' => $user->id,
                'order_sn' => $out_trade_no,
                'type' => $data['type'],
                'user_card_id' => $data['user_card'],
                'money' => $data['amount'],
                'exchange_rate' => $exchange_rate,
                'user_spread' => $user->spread > 0 ? $user->spread : 0.00036,
            ]);

            // 添加流水记录
            MoneyChange::create([
                'user_id' => $user->id,
                'order_sn' => $out_trade_no,
                'type' => MoneyChange::TYPE_WITHDRAW,
                'money_type' => strtoupper($moneyType),
                'before_money' => $beforBalance,
                'money' => -$amount,
                'after_money' => $afterBalance,
                'detailed' => '提款申请',
            ]);

            if ($fee > 0) {
                // 添加手续费流水记录
                MoneyChange::create([
                    'user_id' => $user->id,
                    'order_sn' => $out_trade_no,
                    'type' => MoneyChange::TYPE_WITHDRAW,
                    'money_type' => "EUR",
                    'before_money' => $beforBalance - $amount,
                    'money' => -$fee,
                    'after_money' => $beforBalance - $amount - $fee,
                    'detailed' => '提款手续费',
                ]);
            }

            DB::commit();
            return $this->returnMsg(200, __('api.请求成功'), $out_trade_no);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(503, $e->getMessage());
        }
    }


    /**
     * 提款申请记录
     */
    public function list(Request $request)
    {
        $user = $request->user();
        $res = Withdraw::query()
            ->with(['cardData' => function ($query) {
                $query->select('id', 'type', 'bank', 'bank_owner', 'bank_card_no', 'network', 'usdt_address'); // 根据你卡表实际字段来写
            }])
            ->where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->select(['id', 'type', 'order_sn', 'user_card_id', 'money', 'state', 'remark', 'created_at', 'updated_at'])
            ->paginate(10);
        return $this->returnMsg(200, __('api.请求成功'), $res);

    }

}
