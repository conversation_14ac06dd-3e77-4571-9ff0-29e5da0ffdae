<?php

namespace App\Admin\Repositories;

use App\Models\MoneyChange;
use App\Models\Recharge as Model;
use App\Models\UsersInfo;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class Recharge extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 新增记录.
     *
     * @param  Form  $form
     * @return mixed
     */
    public function store(Form $form)
    {
        $result = null;

        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();

            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }
            $orderSn = generateUniqueOrderNumber();
            $userId = $updates['user_id'];
            $amount = $updates['amount'];
            $updates['order_sn'] = $orderSn;
            $updates['state'] = 1;
            $updates['bank_id'] = 1;
            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }
            $wallet = UsersInfo::query()->where('user_id', $userId)->lockForUpdate()->firstOrFail();

            // 添加流水记录
            $moneyChangeModel = new MoneyChange();
            $moneyChangeModel->create([
                'user_id' => $userId,
                'order_sn' => $orderSn,
                'type' => MoneyChange::TYPE_RECHARGE,
                'money_type' => 'EUR',
                'before_money' => $wallet->eur_balance,
                'money' => $amount,
                'after_money' => $wallet->eur_balance + $amount,
                'detailed' => '充值申请', // 账户充值
            ]);
            $wallet->eur_balance += $amount;
            $wallet->save();
            $result = $model->save();
            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
