<?php

namespace App\Http\Controllers\Api;

use App\Models\Recharge;
use App\Models\RechargeBank;
use App\Models\RechargeUsdt;
use App\Models\SystemConfig;
use Illuminate\Http\Request;

class RechargeController extends ApiBaseController
{

    /**
     * 充值银行卡
     */
    public function bank(Request $request)
    {

        $bank = RechargeBank::query()
            ->where('state', 1)
            ->select(['id', 'bank_name', 'bank_no', 'bank_owner'])
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $bank);
    }

    /**
     * 充值银行卡
     */
    public function usdt(Request $request)
    {

        $bank = RechargeUsdt::query()
            ->where('state', 1)
            ->select(['id', 'type', 'address'])
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $bank);
    }

    /**
     * 充值申请
     */
    public function submitRecharge(Request $request)
    {

        $rules = [
            'amount' => 'required',
            'type' => 'required',
            'bank_id' => 'required',
//            'recharge_name' => 'required',
        ];
        $this->validate($request, $rules);

        // 时间限制 todo

        $data = $request->all();

        // 1：bank  2:usdt
        if (!in_array($data['type'], [1, 2])) {
            return $this->returnMsg(503, __('api.充值类型不合法'));
        }

        if ($data['amount'] <= 0) {
            return $this->returnMsg(503, __('api.充值金额必须大于0'));
        }

        // 获取当前登录用户
        $user = $request->user();

        $out_trade_no = time() . $user->id . rand(1000, 9999);
        $data['order_sn'] = $out_trade_no;
        $data['user_id'] = $user->id;
        if ($data['type'] == 1) {
            if (!$data['recharge_name']) {
                return $this->returnMsg(503, __('api.请输入充值人姓名'));
            }
            // 银行卡充值
            // 查询充值卡是否可用
            $ex = RechargeBank::query()->where('id', $data['bank_id'])->where('state', 1)->exists();
            if (!$ex) {
                return $this->returnMsg(503, __('api.充值银行卡不存在'));
            }
            // 最大 最小金额限制
            $min = SystemConfig::getValue('min_recharge_money');
            $max = SystemConfig::getValue('max_recharge_money');
        } else {
            // usdt 充值
            $ex = RechargeUsdt::query()->where('id', $data['bank_id'])->where('state', 1)->exists();
            if (!$ex) {
                return $this->returnMsg(503, __('api.充值USDT地址不存在'));
            }
            // 最大 最小金额限制
            $min = SystemConfig::getValue('min_usdt_recharge_money');
            $max = SystemConfig::getValue('max_usdt_recharge_money');
        }

        if ($min && $data['amount'] < $min) {
            return $this->returnMsg(503, __('api.充值金额不能低于') . ":{$min}");
        }

        if ($max && $data['amount'] > $max) {
            return $this->returnMsg(503, __('api.充值金额不能高于') . ":{$max}");
        }

        $res = Recharge::create($data);

        if ($res) {
            return $this->returnMsg(200, 'success', $data['order_sn']);
        } else {
            return $this->returnMsg(503, __('api.充值申请失败，请稍后再试'));
        }


    }


    /**
     * 充值申请记录
     */
    public function list(Request $request)
    {
        $user = $request->user();
        $res = Recharge::query()
            ->where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->select(['id', 'bank_id', 'type', 'order_sn', 'amount', 'image', 'state', 'remark', 'created_at'])
            ->paginate(10);
        return $this->returnMsg(200, __('api.请求成功'), $res);

    }

}
