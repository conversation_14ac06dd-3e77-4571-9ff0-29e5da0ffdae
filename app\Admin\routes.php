<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => array_merge(config('admin.route.middleware'), ['admin.ip.whitelist']),
], function (Router $router) {

    $router->get('/', 'HomeController@index');

    $router->get('/system-setting', 'SystemConfigController@siteSetting'); // 系统设置
    $router->resource('auth/users', 'AdminUserController'); // 管理员管理
    $router->resource('admin/position', 'AdminPositionController'); // 职位管理


    // ===============用户管理================
    $router->resource('users', 'UsersController');
    $router->resource('user_card', 'UserCardController'); // 用户银行卡管理
    $router->resource('user_message', 'UserMessageController'); // 用户消息
    $router->resource('user_verify', 'UserVerifyController'); // 用户验证
    // ===============用户管理end================
    $router->resource('area_config', 'AreaConfigController');

    // 获取地区代码的接口
    $router->get('/get-area-code', 'AreaConfigController@getAreaCode');

    // =======产品管理=====================
    $router->resource('product_stock', 'ProductStockController'); // 股票
    $router->get('/product_stock_country_exchange', 'ProductStockController@exchangeByCountry');
    $router->resource('product_forex', 'ProductForexController'); // 外汇
    $router->resource('product_crypro_currency', 'ProductCryptoCurrencyController'); // 加密货币
    $router->resource('product_commodities', 'ProductCommoditiesController'); // 大宗 商品
    $router->resource('product_etf', 'ProductEtfController'); // ETF
    $router->resource('product_ipo', 'IpoController'); // 新股
    $router->resource('zhishu', 'ZhishuController'); // 指数
    // =======产品管理end==================

    // =============== 财务管理==============
    $router->resource('money_change', 'MoneyChangeController'); // 资金流水
    $router->resource('recharge', 'RechargeController'); // 充值申请
    $router->resource('withdraw', 'WithdrawController'); // 提现申请
    $router->resource('exchange_order', 'ExchangeOrderController'); // 资金兑换
    $router->post('exchange-order_calc-rate', 'ExchangeOrderController@calcRate');

    // =============== 财务管理end==============

    // =============== 收款管理==============
    $router->resource('bankrecharge', 'RechargeBankController'); // 收款银行卡
    $router->resource('usdtrecharge', 'RechargeUsdtController'); // 收款USDT
    // =============== 收款管理end==============

    // =============== 股票功能==================
    $router->resource('dividend', 'DividendController'); // 红利股票
    $router->resource('day_trading', 'DayTradingController'); // 日内交易
    $router->resource('big_trade', 'BigTradeController'); // 大宗交易
    // =============== 股票功能end ==============

    // =============== 订单管理==================
    $router->resource('user_order', 'UserOrderController'); // 持仓列表
    $router->post('user-order/calc-fees', 'UserOrderController@calcFees');
    $router->resource('dividend_order', 'DividendOrderController'); // 红利股票订单
    $router->post('dividend-order/calculate', 'DividendOrderController@calculateDividend'); // 计算手续费
    $router->resource('day_trading_order', 'DayTradingOrderController'); // 日内交易订单
    $router->resource('big_trade_order', 'BigTradeOrderController'); // 大宗交易订单
    $router->resource('ipo_order', 'IpoOrderController'); // 新股申购订单
    // =============== 订单管理end ==============

    // =============== 货币管理==================
    $router->resource('exchange_rate', 'ExchangeRateController'); // 汇率
    // =============== 货币管理end ==============

    // =============== 投资管理==================
    $router->resource('/trader', 'TraderController'); // 交易员管理
    $router->resource('/trader_follower', 'TraderFollowerController'); // 关注交易员管理
    $router->resource('/trader_group', 'TraderGroupController'); // 交易员投资组合
    $router->resource('/trader_info', 'TraderInfoController'); // 交易员统计资料
    $router->resource('/trader_article', 'TraderArticleController'); // 交易员文章
    $router->resource('/tfmoneychange', 'TraderFollowerMoneyChangeController'); // 跟随着的流水

    // =============== 投资管理end ==============

    // =============== 新闻管理==================
    $router->resource('/news', 'NewsController'); // 新闻列表
    // =============== 新闻管理end ==============

});
