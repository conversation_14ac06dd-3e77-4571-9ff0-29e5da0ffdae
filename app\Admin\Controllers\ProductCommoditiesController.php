<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Product;
use App\Models\Product as Model;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ProductCommoditiesController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Product(), function (Grid $grid) {
            if (!Admin::user()->inRoles(['administrator'])) {
                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchActions();
            }

            $grid->model()
                ->where('pro_type', Model::$PRO_TYPE_COMMODITES)
                ->orderBy('state', 'desc')
                ->orderBy('weight', 'desc');


            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('symbol');
            $grid->column('category');
//            $grid->column('timestamp');
//            $grid->column('last_quote_at');
//            $grid->column('datetime');
            $grid->column('price');
            $grid->column('open');
            $grid->column('high');
            $grid->column('low');
            $grid->column('close');
            $grid->column('previous_close');
            $grid->column('change')->display(function ($change) {
                if ($this->percent_change > 0) {
                    return "<span style='color:#00a870'>{$change}⬆</span>";
                }elseif ($this->percent_change < 0) {
                    return "<span style='color:red'>{$change}⬇</span>";
                }else{
                    return $change;
                }
            });
            $grid->column('percent_change')->display(function ($percent_change) {
                if ($percent_change > 0) {
                    return "<span style='color:#00a870'>{$percent_change}⬆</span>";
                }elseif ($percent_change < 0) {
                    return "<span style='color:red'>{$percent_change}⬇</span>";
                }else{
                    return $percent_change;
                }
            });
            $grid->column('is_market_open')->display(function ($is_market_open) {
                if ($is_market_open == 1) {
                    return '<span style="color: #14b114">是</span>';
                } else {
                    return '<span style="color: #bdc1bd">否</span>';
                }
            });
//            $grid->column('fifty_two_week');
            $grid->column('is_hot')->switch();
            $grid->column('state')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('symbol')->width(3);
                $filter->equal('state')->select(['0' => '禁用', '1'=> '启用'])->width(3);
                $filter->equal('is_hot')->select(['0' => '否', '1'=> '是'])->width(3);


            });

            $grid->paginate(10);

            // 禁用分页选择器
            $grid->disablePerPages();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Product(), function (Show $show) {
            $show->disableEditButton();
            $show->disableDeleteButton();
            $show->field('id');
            $show->field('name');
            $show->field('symbol');
            $show->field('category');
            $show->field('timestamp');
            $show->field('last_quote_at');
            $show->field('datetime');
            $show->field('price');
            $show->field('open');
            $show->field('high');
            $show->field('low');
            $show->field('close');
            $show->field('previous_close');
            $show->field('change');
            $show->field('percent_change');
            $show->field('is_market_open');
//            $show->field('fifty_two_week');
            $show->field('state');
            $show->field('is_hot');
            $show->field('weight');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Product(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('symbol');
            $form->text('category');
            $form->text('timestamp');
            $form->text('last_quote_at');
            $form->text('datetime');
            $form->text('price');
            $form->text('open');
            $form->text('high');
            $form->text('low');
            $form->text('close');
            $form->text('previous_close');
            $form->text('change');
            $form->text('percent_change');
            $form->text('is_market_open');
//            $form->text('fifty_two_week');
            $form->text('state');
            $form->switch('is_hot');
            $form->switch('state');

//            $form->display('created_at');
//            $form->display('updated_at');
        });
    }
}
