<?php

namespace App\Admin\Actions\Grid\UserOrder;


use App\Models\UserOrder;
use App\Services\OrderService;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class Cancel extends RowAction
{
    /**
     * @return string
     */
    protected $title = '撤销挂单';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $id = $this->getKey();
        try {
            $model = UserOrder::findOrFail($id);

            if ($model->status != 4) {
                throw new \Exception('订单不是挂单中，无法撤销');
            }

            // 根据订单类型选择不同的撤销服务
            if ($model->trade_id > 0) {
                // 跟单订单使用TraderOrderService
                $res = \App\Services\TraderOrderService::cancelTraderOrder($model->id);
            } else {
                // 自主订单使用OrderService
                $res = OrderService::cancel($model->user_id, $model->id);
            }

            return $this->response()->success('撤销挂单成功')->refresh();

        } catch (\Exception $e) {
            return $this->response()->error('撤销挂单失败:' . $e->getMessage())->refresh();
        }
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['确定撤销挂单', ''];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }

}
