<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class VerificationCodeMail extends Mailable
{
    use Queueable, SerializesModels;

    public $code;
    public $type;
    public $appName;
    public $subject;
    public $purpose;

    /**
     * Create a new message instance.
     */
    public function __construct($code, $type, $subject, $purpose, $appName)
    {
        $this->code = $code;
        $this->type = $type;
        $this->subject = $subject;
        $this->purpose = $purpose;
        $this->appName = $appName;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                    ->from(config('mail.from.address'), config('mail.from.name'))
                    ->view('emails.verification-code');
    }
}
