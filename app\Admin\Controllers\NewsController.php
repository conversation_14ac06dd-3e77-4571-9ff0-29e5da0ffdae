<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\News;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class NewsController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new News(), function (Grid $grid) {
            $grid->model()->orderBy('news_add_time', 'desc');
            $grid->column('id')->sortable();
            $grid->column('type')->using(['de' => '德国', 'fr' => '法国', 'sp' => '西班牙', 'en' => '英国', 'my' => '马来', 'jp' => '日本', 'us' => '美国']);
            $grid->column('title');
            $grid->column('image')->image('', 80, 80);
//            $grid->column('content');
            $grid->column('news_add_time');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
//                $filter->equal('id');
                $filter->equal('type')->select(['de' => '德国', 'fr' => '法国', 'sp' => '西班牙', 'en' => '英国', 'my' => '马来', 'jp' => '日本', 'us' => '美国']);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new News(), function (Show $show) {
            $show->field('id');
            $show->field('type');
            $show->field('title');
            $show->field('image');
            $show->field('content');
            $show->field('news_add_time');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new News(), function (Form $form) {
            $form->display('id');
            $form->select('type')->options(['de' => '德国', 'fr' => '法国', 'sp' => '西班牙', 'en' => '英国', 'my' => '马来', 'jp' => '日本', 'us' => '美国'])->required();
            $form->text('title')->required();
            $form->image('image')->required();
            $form->editor('content')->required();
            $form->datetime('news_add_time')->required();

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
