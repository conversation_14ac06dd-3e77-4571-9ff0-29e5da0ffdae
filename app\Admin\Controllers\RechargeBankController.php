<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\RechargeBank;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeBankController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RechargeBank(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('bank_name');
            $grid->column('bank_no');
            $grid->column('bank_owner');
            $grid->column('bank_address');
            $grid->column('info');
            $grid->column('state')->switch();
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RechargeBank(), function (Show $show) {
            $show->field('id');
            $show->field('bank_name');
            $show->field('bank_no');
            $show->field('bank_owner');
            $show->field('bank_address');
            $show->field('info');
            $show->field('state');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeBank(), function (Form $form) {
            $form->display('id');
            $form->text('bank_name')->required();;
            $form->text('bank_no')->required();;
            $form->text('bank_owner')->required();;
            $form->text('bank_address');
            $form->text('info');
            $form->switch('state');

//            $form->display('created_at');
//            $form->display('updated_at');
        });
    }
}
