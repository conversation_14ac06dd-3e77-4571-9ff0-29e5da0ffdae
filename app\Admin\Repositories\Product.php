<?php

namespace App\Admin\Repositories;

use App\Models\Product as Model;
use App\Services\TwelvedataNewService;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Grid;


class Product extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function get(Grid\Model $model)
    {

        $query = parent::get($model);


//
//        if(!$query->isEmpty()){
//            TwelvedataNewService::quoteByBatch($query); // 批量更新
//            return parent::get($model);
//        }

        return $query;
    }

}
