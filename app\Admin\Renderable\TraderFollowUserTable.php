<?php

namespace App\Admin\Renderable;

use App\Models\TraderFollower;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Users;

class TraderFollowUserTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;
        $userIds = TraderFollower::query()->where('trader_id', $id)->where('state', 1)->pluck('user_id')->toArray();

        return Grid::make(new Users(), function (Grid $grid) use ($userIds) {

            $model = $grid->model();
            $model->whereIn('id', $userIds);


            $grid->column('id');
            $grid->column('email');

            $grid->quickSearch(['id', 'email']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('email')->width(4);
            });
        });
    }
}
