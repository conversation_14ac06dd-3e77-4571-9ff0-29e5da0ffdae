<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductPriceUpdateService
{
    /**
     * 价格更新缓存前缀
     */
    const PRICE_UPDATE_CACHE_PREFIX = 'price_update_lock:';
    
    /**
     * 缓存锁定时间（秒）
     */
    const CACHE_LOCK_DURATION = 30;
    
    /**
     * 最小更新间隔（秒）
     */
    const MIN_UPDATE_INTERVAL = 5;

    /**
     * 安全更新产品价格（防止并发冲突）
     * 
     * @param Product $product
     * @param array $priceData
     * @return bool
     */
    public static function safeUpdatePrice(Product $product, array $priceData)
    {
        $lockKey = self::PRICE_UPDATE_CACHE_PREFIX . $product->id;
        
        // 检查是否在更新间隔内
        if (!self::canUpdate($product)) {
            Log::info('Product price update skipped due to interval limit', [
                'product_id' => $product->id,
                'symbol' => $product->symbol
            ]);
            return false;
        }
        
        // 尝试获取缓存锁
        if (!Cache::add($lockKey, time(), self::CACHE_LOCK_DURATION)) {
            Log::info('Product price update skipped due to cache lock', [
                'product_id' => $product->id,
                'symbol' => $product->symbol
            ]);
            return false;
        }
        
        try {
            // 使用数据库事务和行锁进行更新
            DB::transaction(function () use ($product, $priceData) {
                $lockedProduct = Product::where('id', $product->id)
                    ->lockForUpdate()
                    ->first();
                    
                if ($lockedProduct) {
                    $lockedProduct->update($priceData);
                }
            }, 3);
            
            Log::info('Product price updated successfully', [
                'product_id' => $product->id,
                'symbol' => $product->symbol,
                'price' => $priceData['price'] ?? 'N/A'
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Product price update failed', [
                'product_id' => $product->id,
                'symbol' => $product->symbol,
                'error' => $e->getMessage()
            ]);
            
            return false;
            
        } finally {
            // 释放缓存锁
            Cache::forget($lockKey);
        }
    }
    
    /**
     * 批量安全更新产品价格
     * 
     * @param array $updates [['product_id' => 1, 'data' => [...]], ...]
     * @return array 更新结果统计
     */
    public static function batchSafeUpdate(array $updates)
    {
        $results = [
            'success' => 0,
            'skipped' => 0,
            'failed' => 0
        ];
        
        // 按产品ID分组，避免重复更新
        $groupedUpdates = [];
        foreach ($updates as $update) {
            $productId = $update['product_id'];
            // 只保留最新的更新数据
            $groupedUpdates[$productId] = $update;
        }
        
        foreach ($groupedUpdates as $update) {
            $product = Product::find($update['product_id']);
            if (!$product) {
                $results['failed']++;
                continue;
            }
            
            if (self::safeUpdatePrice($product, $update['data'])) {
                $results['success']++;
            } else {
                $results['skipped']++;
            }
        }
        
        return $results;
    }
    
    /**
     * 检查产品是否可以更新（基于时间间隔）
     * 
     * @param Product $product
     * @return bool
     */
    private static function canUpdate(Product $product)
    {
        if (!$product->updated_at) {
            return true;
        }
        
        $lastUpdate = $product->updated_at->timestamp;
        $now = time();
        
        return ($now - $lastUpdate) >= self::MIN_UPDATE_INTERVAL;
    }
    
    /**
     * 清理过期的价格更新锁
     */
    public static function cleanupExpiredLocks()
    {
        // 这个方法可以通过定时任务调用，清理可能的僵尸锁
        $pattern = self::PRICE_UPDATE_CACHE_PREFIX . '*';
        
        try {
            $keys = Cache::getRedis()->keys($pattern);
            foreach ($keys as $key) {
                $lockTime = Cache::get($key);
                if ($lockTime && (time() - $lockTime) > self::CACHE_LOCK_DURATION) {
                    Cache::forget($key);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to cleanup expired price update locks: ' . $e->getMessage());
        }
    }
}
