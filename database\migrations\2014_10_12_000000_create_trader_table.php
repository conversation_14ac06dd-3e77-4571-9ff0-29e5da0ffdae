<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTraderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trader', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('admin_id')->default(0)->index()->comment('所属管理员id');
            $table->string('name', 32)->nullable(true)->comment('名字');
            $table->string('nickname', 32)->nullable(true)->comment('昵称');
            $table->string('avatar', 60)->nullable(true)->comment('头像地址');
            $table->text('en_introduction')->nullable(true)->comment('交易员简介（英语）');
            $table->text('es_introduction')->nullable(true)->comment('交易员简介（西班牙语）');
            $table->text('de_introduction')->nullable(true)->comment('交易员简介（德语）');
            $table->tinyInteger('state')->default(1)->comment('1可用 0禁用');
            $table->tinyInteger('level')->default(1)->comment('交易员等级，1=明星 2=精英 3=冠军');
            $table->integer('weight')->default(1)->comment('权重，值越大越靠前');
            $table->integer('follower')->default(0)->comment('跟随者数量');
            $table->integer('fans')->default(0)->comment('粉丝数量');


            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->index()->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}trader auto_increment=1 comment '交易员表'");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trader');
    }
}
