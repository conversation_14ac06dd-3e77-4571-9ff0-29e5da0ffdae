<?php

namespace App\Admin\Extensions;

use Dcat\Admin\Admin;

class AdminSecurity
{
    /**
     * 初始化安全扩展
     */
    public static function boot()
    {
        // 添加全局脚本
        Admin::script(self::script());
    }

    /**
     * 全局脚本
     */
    protected static function script()
    {
        return <<<JS
// 登录表单增强
$(document).ready(function() {
    // 回车键提交
    $(document).on('keypress', '.login-box input', function(e) {
        if (e.which == 13) {
            $(this).closest('form').submit();
        }
    });

    // 数学验证码输入框只允许数字
    $(document).on('input', 'input[name="math_captcha"]', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
    });
});
JS;
    }
}
