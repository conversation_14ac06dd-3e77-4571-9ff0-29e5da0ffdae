<?php

use App\Models\SystemConfig;

if (!function_exists('generateUniqueOrderNumber')) {
    function generateUniqueOrderNumber()
    {
        // 自定义逻辑
        return random_int(10000, 99999) . substr(sprintf("%d", microtime(true) * 10000), -5);
    }
}

// 交易货币以欧元为准，通过汇率兑换，实现价差默认控制为（0.0036%），用户可以单独设置这个汇率价差的值
// 比如现在欧元兑换美元的实时汇率是：1.17102
// 那么买入的汇率应该要按照1.17060来计算
// 汇率价差就是0.036%
// 比如我现在买100美元的股票，那么实际要扣款的欧元余额是：

// 实时欧元/美元汇率：realRate = 1.17102
// 默认价差（spread）比例：spreadRate = 0.036% = 0.036/100 = 0.00036
// buyRate = 1.17102 × (1 – 0.00036) ≈ 1.17060
// 计算欧元扣款
// euroCost = usdAmount ÷ buyRate
// euroCost = 100 ÷ 1.17060 ≈ 85.43 EUR

// 计算欧元成本
if (!function_exists('calculateEuroCost')) {
    /**
     * 计算买入 USD 时需要扣除的 EUR
     *
     * @param float $usdAmount 要买入的美元金额
     * @param float $realRate 实时 EUR/USD 汇率
     * @param float $spread 价差值
     *
     * @return float             扣除的欧元金额
     */
    function calculateEuroCost(float $usdAmount, float $realRate, float $spread)
    {
        $buyRate = $realRate - $spread;

        if ($buyRate <= 0) {
            throw new \InvalidArgumentException(__('api.计算出的买入汇率不合法:')."{$buyRate}");
        }

        return round($usdAmount * $buyRate, 3);
    }
}

if (!function_exists('buy_commission')) {
    function buy_commission($amount, $rate)
    {
//        $lowFees = \App\Models\SystemConfig::getValue('zuidishouxufei');
        // 计算手续费
        $sxf = $amount * $rate;
//        if ($sxf < $lowFees) {
//            $sxf = $lowFees;
//        }

        return round($sxf, 3);
    }
}

if (!function_exists('get_buy_fee_rate')) {
    /**
     * 根据用户获取买入手续费率
     * 用户有配置
     */
    function get_buy_fee_rate($userId)
    {
        $user = \App\Models\Users::query()->find($userId);
        if ($user && $user->fee_rate > 0) {
            return $user->fee_rate;
        }
        return \App\Models\SystemConfig::getValue('shouxufeilv');
    }
}

if (!function_exists('sell_commission')) {
    function sell_commission($amount, $rate)
    {
        $lowFees = \App\Models\SystemConfig::getValue('zuidishouxufei');
        // 计算手续费
        $sxf = $amount * $rate;
        if ($sxf < $lowFees) {
            $sxf = $lowFees;
        }
        return round($sxf, 3);
    }
}

if (!function_exists('getAccountByPro')) {
    function getAccountByPro($product)
    {
        if ($product->currency) {
            $currency = strtolower($product->currency);
            $balance = $currency . '_balance';
            $dongjie = $currency . '_dongjie';
            $currency = strtoupper($product->currency);
        } else {
            if ($product->pro_type == 4) {
                // 外汇需要额外处理
                $arr = explode('/', $product->pro_type);
                $balance = strtolower($arr[1]) . '_balance';
                $dongjie = strtolower($arr[1]) . '_dongjie';
                $currency = strtoupper($arr[1]);
            } else {
                $balance = 'usd_balance';
                $dongjie = 'usd_dongjie';
                $currency = 'USD';
            }
        }
        return [
            'balance_field' => $balance,
            'dongjie_field' => $dongjie,
            'currency' => $currency,
        ];
    }
}


if (!function_exists('is_market_on')) {
    /**
     *
     * 根据产品判断是否开盘
     * 大宗、数字货币，外汇实时开盘
     * 股票类根据股票来判断
     * @param $product
     * @return bool
     */
    function is_market_on($product)
    {
        if (in_array($product->pro_type, [2, 4, 5])) {
            // 大宗、数字货币，外汇实时开盘
            return true;
        }

        if ($product->pro_type == 1) {
            // 股票类
            $test = env('test_open_market', false);
            if ($test) {
                // 测试使用，开盘
                return true;
            }

            $systemTime = new DateTime();

            switch ($product->country) {
                case 'us':
                    // 美股
                    // 美股開市時間為星期一至五的9:30am – 4pm
                    // 设置时区为美国东部时间（新洛沙）
                    // 设置美国东部时间（UTC-4）
                    $newYorkTimeZone = new DateTimeZone('America/New_York');
                    // 将系统时间转换为美国东部时间
                    $newYorkTime = $systemTime->setTimezone($newYorkTimeZone);
                    $date = $newYorkTime->format('Y-m-d H:i:s');
                    $day = $newYorkTime->format('Y-m-d');
                    $now = strtotime($date);
                    $w = intval(date("w", $now)); // 今天是周几 0-6

                    // 先判断今天是不是再工作日内-周一到周五
                    if ($w == 0 || $w == 6) {
                        return false;
                    }

                    if ($now < strtotime(date('Y-m-d 09:30:00', $now))) {
                        return false;
                    }

                    if ($now > strtotime(date('Y-m-d 16:00:00', $now))) {
                        return false;
                    }

                    $country = $product->country;
                    $holidays = SystemConfig::getValue($country . '_holidays');
                    if ($holidays) {
                        $holidaysArr = preg_split('/\r\n|\r|\n/', $holidays);
                        if (in_array($day, $holidaysArr)) {
                            return false; // 今天是节假日，不开盘
                        }
                    }
                    return true;
                    break;
                case 'my':
                    // 马来 时间跟北京时间一样
                    // 设置马来时间
                    $myTimeZone = new DateTimeZone('Asia/Shanghai');
                    // 将系统时间转换为马来时间
                    $myTime = $systemTime->setTimezone($myTimeZone);
                    $date = $myTime->format('Y-m-d H:i:s');
                    $day = $myTime->format('Y-m-d');
                    $now = strtotime($date);
                    $w = intval(date("w", $now)); // 今天是周几 0-6
                    // 先判断今天是不是再工作日内-周一到周五
                    if ($w == 0 || $w == 6) {
                        return false;
                    }

                    if ($now < strtotime(date('Y-m-d 09:00:00'))) {
                        return false;
                    }

                    if ($now >= strtotime(date('Y-m-d 12:30:00')) && $now < strtotime(date('Y-m-d 14:30:00'))) {
                        return false;
                    }

                    if ($now >= strtotime(date('Y-m-d 16:45:00'))) {
                        return false;
                    }

                    $country = $product->country;
                    $holidays = SystemConfig::getValue($country . '_holidays');
                    if ($holidays) {
                        $holidaysArr = preg_split('/\r\n|\r|\n/', $holidays);
                        if (in_array($day, $holidaysArr)) {
                            return false; // 今天是节假日，不开盘
                        }
                    }
                    return true;
                    break;
                case 'jp':
                    // 日本时间
                    // 设置日本时间
                    $jpTimeZone = new DateTimeZone('Asia/Tokyo');
                    // 将系统时间转换为日本时间
                    $jpTime = $systemTime->setTimezone($jpTimeZone);
                    $date = $jpTime->format('Y-m-d H:i:s');
                    $day = $jpTime->format('Y-m-d');
                    $now = strtotime($date);
                    $w = intval(date("w", $now)); // 今天是周几 0-6
                    // 先判断今天是不是再工作日内-周一到周五
                    if ($w == 0 || $w == 6) {
                        return false;
                    }

                    if ($now < strtotime(date('Y-m-d 09:00:00'))) {
                        return false;
                    }

                    if ($now >= strtotime(date('Y-m-d 11:30:00')) && $now < strtotime(date('Y-m-d 12:30:00'))) {
                        return false;
                    }

                    if ($now >= strtotime(date('Y-m-d 15:30:00'))) {
                        return false;
                    }

                    $country = $product->country;
                    $holidays = SystemConfig::getValue($country . '_holidays');
                    if ($holidays) {
                        $holidaysArr = preg_split('/\r\n|\r|\n/', $holidays);
                        if (in_array($day, $holidaysArr)) {
                            return false; // 今天是节假日，不开盘
                        }
                    }
                    return true;
                    break;
                case 'de':
                case 'fr':
                case 'sp':
                    // 系统是德国时间
                    // 德国，法国, 西班牙的时间一样 开盘 停盘时间也一样
                    // 德国股市开盘时间是每周一到周五的早上9点至17:30(德国当地时间)，
                    $now = time();
                    $w = intval(date("w", $now)); // 今天是周几 0-6

                    // 先判断今天是不是再工作日内-周一到周五
                    if ($w == 0 || $w == 6) {
                        return false;
                    }

                    if ($now < strtotime(date('Y-m-d 09:00:00'))) {
                        return false;
                    }

                    if ($now >= strtotime(date('Y-m-d 17:30:00'))) {
                        return false;
                    }
                    $country = $product->country;
                    $holidays = SystemConfig::getValue($country . '_holidays');
                    if ($holidays) {
                        $holidaysArr = preg_split('/\r\n|\r|\n/', $holidays);
                        $date = date('Y-m-d', $now);
                        if (in_array($date, $holidaysArr)) {
                            return false; // 今天是节假日，不开盘
                        }
                    }
                    return true;
                    break;
                case 'en':
                    // 英国股市（伦敦证券交易所）于UTC 时间早上7:00 开盘，下午3:30 收盘，无午休
                    // 英国时间
                    // 设置英国时间
                    $enTimeZone = new DateTimeZone('Europe/London');
                    // 将系统时间转换为英国时间
                    $enTime = $systemTime->setTimezone($enTimeZone);
                    $date = $enTime->format('Y-m-d H:i:s');
                    $day = $enTime->format('Y-m-d');
                    $now = strtotime($date);
                    $w = intval(date("w", $now)); // 今天是周几 0-6
                    // 先判断今天是不是再工作日内-周一到周五
                    if ($w == 0 || $w == 6) {
                        return false;
                    }

                    if ($now < strtotime(date('Y-m-d 07:00:00'))) {
                        return false;
                    }


                    if ($now >= strtotime(date('Y-m-d 15:30:00'))) {
                        return false;
                    }
                    $country = $product->country;
                    $holidays = SystemConfig::getValue($country . '_holidays');
                    if ($holidays) {
                        $holidaysArr = preg_split('/\r\n|\r|\n/', $holidays);
                        if (in_array($day, $holidaysArr)) {
                            return false; // 今天是节假日，不开盘
                        }
                    }
                    return true;
                    break;
                default:
                    return false;
            }
        }
        return false;
    }
}
