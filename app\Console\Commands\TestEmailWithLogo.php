<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailWithLogo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-logo {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email with different logo methods';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email format provided.');
            return 1;
        }

        $this->info("Testing different logo methods...");

//        // Method 1: CID (Current method)
//        $this->testCIDMethod($email);
//
//        // Method 2: Base64 (Fallback)
//        $this->testBase64Method($email);

        // Method 3: External URL (if you have a working domain)
        $this->testURLMethod($email);

        return 0;
    }

    private function testCIDMethod($email)
    {
        $this->info("\n1. Testing CID Method (Current)...");

        $htmlContent = $this->generateTestHTML('CID Method', 'cid:logo');

        try {
            Mail::send([], [], function ($message) use ($email, $htmlContent) {
                $message->to($email)
                    ->subject('Logo Test - CID Method')
                    ->setBody($htmlContent, 'text/html');

                $logoPath = public_path('logo.png');
                if (file_exists($logoPath)) {
                    $message->embed($logoPath, 'logo');
                }
            });

            $this->info("✅ CID method email sent");
        } catch (\Exception $e) {
            $this->error("❌ CID method failed: " . $e->getMessage());
        }
    }

    private function testBase64Method($email)
    {
        $this->info("\n2. Testing Base64 Method...");

        $logoPath = public_path('logo.png');
        if (file_exists($logoPath)) {
            $logoData = file_get_contents($logoPath);
            $base64 = base64_encode($logoData);
            $src = "data:image/png;base64,{$base64}";
        } else {
            $src = "";
        }

        $htmlContent = $this->generateTestHTML('Base64 Method', $src);

        try {
            Mail::send([], [], function ($message) use ($email, $htmlContent) {
                $message->to($email)
                    ->subject('Logo Test - Base64 Method')
                    ->setBody($htmlContent, 'text/html');
            });

            $this->info("✅ Base64 method email sent");
        } catch (\Exception $e) {
            $this->error("❌ Base64 method failed: " . $e->getMessage());
        }
    }

    private function testURLMethod($email)
    {
        $this->info("\n3. Testing URL Method...");

        // You can replace this with your actual domain
        $src = "https://via.placeholder.com/200x60/667eea/ffffff?text=LOGO";

        $htmlContent = $this->generateTestHTML('URL Method (Placeholder)', $src);

        try {
            Mail::send([], [], function ($message) use ($email, $htmlContent) {
                $message->to($email)
                    ->subject('Logo Test - URL Method')
                    ->setBody($htmlContent, 'text/html');
            });

            $this->info("✅ URL method email sent");
        } catch (\Exception $e) {
            $this->error("❌ URL method failed: " . $e->getMessage());
        }
    }

    private function generateTestHTML($method, $src)
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Logo Test - {$method}</title>
        </head>
        <body style='font-family: Arial, sans-serif; margin: 0; padding: 20px;'>
            <div style='max-width: 600px; margin: 0 auto; border: 1px solid #ddd; border-radius: 8px;'>
                <div style='padding: 40px 30px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px 8px 0 0;'>
                    " . ($src ? "<img src='{$src}' alt='Test Logo' style='max-width: 200px; max-height: 60px; height: auto; display: block; margin: 0 auto;' />" : "<h1 style='color: white; margin: 0;'>NO LOGO</h1>") . "
                </div>
                <div style='padding: 30px;'>
                    <h2>Logo Test - {$method}</h2>
                    <p>This email tests the {$method} for displaying logos in emails.</p>
                    <p><strong>Expected:</strong> Logo should appear in the header above.</p>
                    <p><strong>Actual:</strong> " . ($src ? "Logo source provided" : "No logo source") . "</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
