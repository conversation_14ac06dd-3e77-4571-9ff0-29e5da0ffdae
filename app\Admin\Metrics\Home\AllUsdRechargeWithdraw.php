<?php

namespace App\Admin\Metrics\Home;

use App\Models\Recharge;
use App\Models\Withdraw;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Metrics\Donut;

class AllUsdRechargeWithdraw extends Donut
{
    protected $labels = ['充值($)', '提现($)'];

    /**
     * 初始化卡片内容
     */
    protected function init()
    {
        parent::init();

        $color = Admin::color();
        $colors = [$color->primary(), $color->alpha('blue2', 0.5)];

        $this->title('总充提($)');
//        $this->subTitle('Last 30 days');
        $this->chartLabels($this->labels);
        // 设置图表颜色
        $this->chartColors($colors);
    }

    /**
     * 渲染模板
     *
     * @return string
     */
    public function render()
    {
        $this->fill();

        return parent::render();
    }

    /**
     * 写入数据.
     *
     * @return void
     */
    public function fill()
    {
        // 充值
        $rechargeQuery = Recharge::query()
            ->join('users', 'recharge.user_id', '=', 'users.id')
//            ->where('recharge.created_at', '>=', date('Y-m-01 00:00:00'))
//            ->where('recharge.created_at', '<=', date('Y-m-d 23:59:59'))
            ->where('recharge.state', 1)
            ->where('recharge.type', 2);
        // 获取当前登录的管理员账号
        $currentAdminUser = Admin::user();
        $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

        if (!empty($adminIds)) {
            $rechargeQuery->whereIn('users.parent_id', $adminIds);
        }
        $rechargeSum = $rechargeQuery->sum('recharge.amount');


        // 提现
        $withdrawQuery = Withdraw::query()
            ->join('users', 'withdraw.user_id', '=', 'users.id')
//            ->where('withdraw.created_at', '>=', date('Y-m-01 00:00:00'))
//            ->where('withdraw.created_at', '<=', date('Y-m-d 23:59:59'))
            ->where('withdraw.state', 1)
            ->where('withdraw.type', 2);


        if (!empty($adminIds)) {
            $withdrawQuery->whereIn('users.parent_id', $adminIds);
        }

        $withdrawSum = $withdrawQuery->sum('withdraw.money');

        $this->withContent($rechargeSum, $withdrawSum);

        // 图表数据
        $this->withChart([$rechargeSum, $withdrawSum]);
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data)
    {
        return $this->chart([
            'series' => $data
        ]);
    }

    /**
     * 设置卡片头部内容.
     *
     * @param mixed $desktop
     * @param mixed $mobile
     *
     * @return $this
     */
    protected function withContent($desktop, $mobile)
    {
        $blue = Admin::color()->alpha('blue2', 0.5);

        $style = 'margin-bottom: 8px';
        $labelWidth = 120;

        return $this->content(
            <<<HTML
<div class="d-flex pl-1 pr-1 pt-1" style="{$style}">
    <div style="width: {$labelWidth}px">
        <i class="fa fa-circle text-primary"></i> {$this->labels[0]}
    </div>
    <div>{$desktop}</div>
</div>
<div class="d-flex pl-1 pr-1" style="{$style}">
    <div style="width: {$labelWidth}px">
        <i class="fa fa-circle" style="color: $blue"></i> {$this->labels[1]}
    </div>
    <div>{$mobile}</div>
</div>
HTML
        );
    }
}
