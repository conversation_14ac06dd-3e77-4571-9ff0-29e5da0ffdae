<?php

namespace App\Admin\Forms;

use App\Models\MoneyChange;
use App\Models\DayTradingOrder;
use App\Models\Product;
use App\Models\UserOrder;
use App\Services\JiangshanService;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\UsersInfo;

class DayTradingOrderCheck extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $state = $input['state'] ?? 0;
        $successNum = $input['success_num'] ?? 0;


        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!in_array($state, [1, 2])) {
            return $this->response()->error('操作错误');
        }

        if (!$id) {
            return $this->response()->error('成交股数必须大于0');
        }

        DB::beginTransaction();
        try {
            $data = DayTradingOrder::find($id);

            if (!$data) {
                DB::rollBack();
                return $this->response()->error('数据不存在');
            }

            if ($data->state != 0) {
                throw new \Exception('订单状态不是未审核状态，请勿重复操作');
            }

            $userInfo = UsersInfo::where('user_id', $data->user_id)->lockForUpdate()->first();
            $product = Product::find($data->product_id);
            $balance = 'eur_balance';
            $dongjie = 'eur_dongjie';
            $before = $userInfo->$balance;

            if ($state == 1) {
                // 最终成交的股数，多还少补 本金退还，
                // 1：成交股数比提交股数多的，需要额外扣除本金，扣除手续费
                // 2： 成交股数比提交股数少的，需要额退回本金，退回手续费
                // 3： 持平就不做操作
                // 4：转持仓

                // a:先计算成功股数的手续费
                // b:先计算成功股数的手续费

                $successTotal = $successNum * $data->buy_in_price;
                // 根据订单的手续费率操作
                $rate = $data->buy_in_fee_rate;
                $successFees = buy_commission($successTotal, $rate); // 手续费计算

                $differenceNum = $successNum - $data->buy_in_num;
                if ($differenceNum > 0) {
                    // 成功股数比申请的还要多，需要再扣除 金额+手续费
                    $differenceTotal = $differenceNum * $data->buy_in_price; // 多的差额
                    $differenceFee = round($successFees - $data->buy_in_fee, 2); // 多的差额的手续费 成交股数的手续费-申请时手续费，剩下的就是需要补交的收费

                    $stamp_duty = 0;
                    if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
                        $stamp_duty = round($differenceTotal * 0.002, 2); // 印花税
                    }
                    if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                        $differenceTotal = calculateEuroCost($differenceTotal, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                        $differenceFee = calculateEuroCost($differenceFee, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                        $stamp_duty = calculateEuroCost($stamp_duty, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                    }
                    // 验证余额
                    if ($userInfo->$balance < ($differenceTotal + $differenceFee + $stamp_duty)) {
                        throw new \Exception('用户余额不足');
                    }

                    // 更新余额
                    $userInfo->$balance -= ($differenceTotal + $differenceFee + $stamp_duty);
                    $userInfo->$dongjie += $differenceTotal;
                    $userInfo->save();

                    // 添加资金流水
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $data->user_id,
                        'order_sn' => $data->order_sn,
                        'product_name' => $product->name,
                        'detailed' => "日内交易申购|代码|{$product->symbol}|名称|{$product->name}",
                        'type' => MoneyChange::TYPE_TRADE_DAY_TRADING,
                        'before_money' => $before,
                        'money' => -$differenceTotal,
                        'after_money' => $before - $differenceTotal,
                    ]);

                    if ($differenceFee > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $data->user_id,
                            'order_sn' => $data->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "日内交易申购手续费|代码|{$product->symbol}|名称|{$product->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE_DAY_TRADING,
                            'before_money' => $before - $differenceTotal,
                            'money' => -$differenceFee,
                            'after_money' => $before - $differenceTotal - $differenceFee,
                        ]);
                    }

                    if ($stamp_duty > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $data->user_id,
                            'order_sn' => $data->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "日内交易申购印花税|代码|{$product->symbol}|名称|{$product->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE_DIVIDEND,
                            'before_money' => $before - $differenceTotal - $differenceFee,
                            'money' => -$stamp_duty,
                            'after_money' => $before - $differenceTotal - $differenceFee - $stamp_duty,
                        ]);
                    }
                }
                elseif ($differenceNum < 0) {
                    // 成交股数低于提交的股数，需要给用户退回部分资金，部分手续费

                    $differenceTotal = abs($differenceNum) * $data->buy_in_price; // 需要退回的本金
                    $differenceFee = round($data->buy_in_fee - $successFees, 2); // 多的差额的手续费 申请时手续费-成交股数的手续费，需要退回的手续费

                    $stamp_duty = 0;
                    if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
                        $stamp_duty = round($differenceTotal * 0.002, 2); // 印花税
                    }
                    if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                        $differenceTotal = calculateEuroCost($differenceTotal, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                        $differenceFee = calculateEuroCost($differenceFee, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                        $stamp_duty = calculateEuroCost($stamp_duty, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                    }

                    // 更新余额
                    $userInfo->$balance += ($differenceTotal + $differenceFee + $stamp_duty);
                    $userInfo->$dongjie -= $differenceTotal;
                    $userInfo->save();

                    // 添加资金流水
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $data->user_id,
                        'order_sn' => $data->order_sn,
                        'product_name' => $product->name,
                        'detailed' => "退回部分日内交易申购|代码|{$product->symbol}|名称|{$product->name}",
                        'type' => MoneyChange::TYPE_TRADE_DAY_TRADING,
                        'before_money' => $before,
                        'money' => $differenceTotal,
                        'after_money' => $before + $differenceTotal,
                    ]);

                    if ($differenceFee > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $data->user_id,
                            'order_sn' => $data->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "退回部分日内交易申购手续费|代码|{$product->symbol}|名称|{$product->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE_DAY_TRADING,
                            'before_money' => $before + $differenceTotal,
                            'money' => $differenceFee,
                            'after_money' => $before + $differenceFee + $differenceTotal,
                        ]);
                    }

                    if ($stamp_duty > 0) {
                        // 添加资金流水-手续费
                        MoneyChange::create([
                            'money_type' => 'EUR',
                            'user_id' => $data->user_id,
                            'order_sn' => $data->order_sn,
                            'product_name' => $product->name,
                            'detailed' => "退回部分日内交易申购印花税|代码|{$product->symbol}|名称|{$product->name}",
                            'type' => MoneyChange::TYPE_TRADE_FEE_DIVIDEND,
                            'before_money' => $before + $differenceTotal + $differenceFee,
                            'money' => $stamp_duty,
                            'after_money' => $before + $differenceTotal + $differenceFee + $stamp_duty,
                        ]);
                    }
                }

                // 拉取最新行情
                $jiangshan = new JiangshanService();
                $product = $jiangshan->get_new_data_serices($product);
                // 转持仓
                UserOrder::create([
                    'user_id' => $data->user_id,
                    'order_sn' => $data->order_sn,
                    'product_id' => $data->product_id,
                    'product_type' => $product->pro_type,
                    'direction_data' => 1, // 买涨
                    'buy_type' => 2, // 市价
                    'buy_price' => $data->buy_in_price,
                    'buy_quantity' => $successNum,
                    'lever' => $data->lever,
                    'is_open_buy' => 0,
                    'status' => 1,
                    'buy_cost' => $successTotal,
                    'take_profit' => 0,
                    'stop_loss' => 0,
                    'buy_time' => time(),
                    'buy_fees' => $successFees, // 手续费
                    'buy_fees_rate' => $rate, // 手续费
                    'currency' => $product->currency, // 使用货币
                    'market_cap' => $product->price * $successNum, // 当前市值
                    'profit_amount' => ($product->price - $data->buy_in_price) * $successNum, // 赢亏
                    'user_spread' => $data->user_spread > 0 ? $data->user_spread : 0.00036, // 用户汇率价差
                    'stamp_duty' => $stamp_duty ?? 0, // 西班牙印花税
                    'exchange_rate' => $data->exchange_rate, // 货币兑换汇率(买入时)
                ]);

                DayTradingOrder::query()->where('id', $id)->update([
                    'state' => $state,
                    'success_num' => $successNum,
                    'success_fee' => $successFees,
                    'success_in_time' => date('Y-m-d H:i:s'),
                    'success_market_val' => $successTotal,
                ]);

            }
            else {
                $buy_in_market_val = $data->buy_in_market_val;
                $buy_in_fee = $data->buy_in_fee;
                $stamp_duty = $data->stamp_duty;
                if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                    $buy_in_market_val = calculateEuroCost($data->buy_in_market_val, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                    $buy_in_fee = calculateEuroCost($data->buy_in_fee, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                    $stamp_duty = calculateEuroCost($data->stamp_duty, $data->exchange_rate, $data->user_spread > 0 ? $data->user_spread : 0.00036);
                }

                // 审核拒绝
                // 需要把资金退回去
                // 更新余额
                $userInfo->$balance += ($buy_in_market_val + $buy_in_fee + $stamp_duty);
                $userInfo->$dongjie -= $buy_in_market_val;
                $userInfo->save();

                // 添加资金流水
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $data->user_id,
                    'order_sn' => $data->order_sn,
                    'product_name' => $product->name,
                    'detailed' => "退回日内交易申购|代码|{$product->symbol}|名称|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_DAY_TRADING,
                    'before_money' => $before,
                    'money' => $buy_in_market_val,
                    'after_money' => $before + $buy_in_market_val,
                ]);

                if ($buy_in_fee > 0) {
                    // 添加资金流水-手续费
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $data->user_id,
                        'order_sn' => $data->order_sn,
                        'product_name' => $product->name,
                        'detailed' => "退回日内交易申购手续费|代码|{$product->symbol}|名称|{$product->name}",
                        'type' => MoneyChange::TYPE_TRADE_FEE_DAY_TRADING,
                        'before_money' => $before + $buy_in_market_val,
                        'money' => $buy_in_fee,
                        'after_money' => $before + $buy_in_market_val + $buy_in_fee,
                    ]);
                }

                if ($stamp_duty > 0) {
                    // 添加资金流水-手续费
                    MoneyChange::create([
                        'money_type' => 'EUR',
                        'user_id' => $data->user_id,
                        'order_sn' => $data->order_sn,
                        'product_name' => $product->name,
                        'detailed' => "退回日内交易申购印花税|代码|{$product->symbol}|名称|{$product->name}",
                        'type' => MoneyChange::TYPE_TRADE_FEE_DIVIDEND,
                        'before_money' => $before + $buy_in_market_val + $buy_in_fee,
                        'money' => $stamp_duty,
                        'after_money' => $before + $buy_in_market_val + $buy_in_fee + $stamp_duty,
                    ]);
                }

                DayTradingOrder::query()->where('id', $id)->update([
                    'state' => $state,
                    'success_num' => 0,
                    'success_fee' => 0,
                    'success_in_time' => null,
                    'success_market_val' => null,
                ]);

            }
            DB::commit();
            return $this->response()->success('操作成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('操作成功失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');

        $this->display('order_sn', '订单号');
        $this->display('buy_in_price', '申购价格');
        $this->display('buy_in_num', '申购股数');
        $this->display('buy_in_market_val', '申购本金');
        $this->display('buy_in_fee', '申购手续费');
        $this->number('success_num', '成交股数')->required();
        $this->display('created_at', '申购时间');

        $this->radio('state', '操作')->options(['1' => '通过', '2' => '拒绝'])
            ->default('1');
    }

    // 返回表单数据，如不需要可以删除此方法
    public function default()
    {
        // 获取外部传递参数
        $id = $this->payload['id'] ?? null;

        $data = DayTradingOrder::find($id);

        return [
            'order_sn' => $data->order_sn,
            'buy_in_price' => $data->buy_in_price,
            'buy_in_num' => $data->buy_in_num,
            'buy_in_market_val' => $data->buy_in_market_val,
            'buy_in_fee' => $data->buy_in_fee,
            'success_num' => $data->buy_in_num,
            'created_at' => $data->created_at,
        ];
    }
}
