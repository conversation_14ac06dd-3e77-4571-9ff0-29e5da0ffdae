<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UserVerify;
use App\Models\UserVerify as UserVerifyModel;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserVerifyController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserVerify(), function (Grid $grid) {

            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disableActions();

            $currentAdminUser = Admin::user();

            if (!$currentAdminUser->can('funds-money-change-del')) {
                $grid->disableDeleteButton(); // 列表批量删除按钮
                $grid->actions(function ($actions) {
                    $actions->disableDelete(); // 每行的删除按钮
                });
            }


            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }
            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('first_name');
            $grid->column('middle_name');
            $grid->column('last_name');
            $grid->column('sex')->using(['f' => '女', 'm' => '男']);
            $grid->column('birthday');
            $grid->column('passport_no');
            $grid->column('stocks_trade_times')->using(UserVerifyModel::$STOCKS_TRADE_TIMES);
            $grid->column('crypto_trade_times')->using(UserVerifyModel::$CYPTO_TRADE_TIMES);
            $grid->column('lever_trade_times')->using(UserVerifyModel::$LEVER_TRADE_TIMES);
            $grid->column('info_learn')->using(UserVerifyModel::$INFO_LEARN);
            $grid->column('user_knows')->using(UserVerifyModel::$USER_KNOWS);
            $grid->column('hobby')->using(UserVerifyModel::$HOBBY);
            $grid->column('hold_time')->using(UserVerifyModel::$HOLD_TIME);
            $grid->column('trade_mudi')->using(UserVerifyModel::$TRADE_MUDI);
            $grid->column('plan_recharge')->using(UserVerifyModel::$PLAN_RECHARGE);
            $grid->column('hope_return')->using(UserVerifyModel::$HOPE_RETURN);
            $grid->column('my_friend')->using(UserVerifyModel::$MY_FRIEND);
            $grid->column('money_from')->using(UserVerifyModel::$MONEY_FROM);
            $grid->column('job');
            $grid->column('boss_name');
            $grid->column('year_money')->using(UserVerifyModel::$YEAR_MONEY);
            $grid->column('money_change')->using(UserVerifyModel::$MONEY_CHANGE);
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserVerify(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('first_name');
            $show->field('middle_name');
            $show->field('last_name');
            $show->field('sex');
            $show->field('birthday');
            $show->field('passport_no');
            $show->field('stocks_trade_times');
            $show->field('crypto_trade_times');
            $show->field('lever_trade_times');
            $show->field('info_learn');
            $show->field('user_knows');
            $show->field('hobby');
            $show->field('hold_time');
            $show->field('trade_mudi');
            $show->field('plan_recharge');
            $show->field('hope_return');
            $show->field('my_friend');
            $show->field('money_from');
            $show->field('job');
            $show->field('boss_name');
            $show->field('year_money');
            $show->field('money_change');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserVerify(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('first_name');
            $form->text('middle_name');
            $form->text('last_name');
            $form->text('sex');
            $form->text('birthday');
            $form->text('passport_no');
            $form->text('stocks_trade_times');
            $form->text('crypto_trade_times');
            $form->text('lever_trade_times');
            $form->text('info_learn');
            $form->text('user_knows');
            $form->text('hobby');
            $form->text('hold_time');
            $form->text('trade_mudi');
            $form->text('plan_recharge');
            $form->text('hope_return');
            $form->text('my_friend');
            $form->text('money_from');
            $form->text('job');
            $form->text('boss_name');
            $form->text('year_money');
            $form->text('money_change');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
