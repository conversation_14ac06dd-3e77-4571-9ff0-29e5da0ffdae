<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ExchangeRate;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ExchangeRateController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ExchangeRate(), function (Grid $grid) {
            // 禁用行选择器（去掉勾选框）
            $grid->disableRowSelector();
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('from_currency');
            $grid->column('to_currency');
            $grid->column('rate');
//            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });

            $grid->disableDeleteButton(); // 列表批量删除按钮
            $grid->actions(function ($actions) {
                $actions->disableDelete(); // 每行的删除按钮
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ExchangeRate(), function (Show $show) {
            $show->disableDeleteButton();
            $show->field('id');
            $show->field('from_currency');
            $show->field('to_currency');
            $show->field('rate');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ExchangeRate(), function (Form $form) {
            $form->disableDeleteButton();
            $form->display('id');
            $form->select('from_currency')
                ->options(function () {
                    return \App\Models\AreaConfig::where('state', 1)
                        ->orderBy('state', 'desc')->orderBy('weight', 'desc')
                        ->pluck('name_show', 'area_code');
                })->required();
            $form->select('to_currency')
                ->options(function () {
                    return \App\Models\AreaConfig::where('state', 1)
                        ->orderBy('state', 'desc')->orderBy('weight', 'desc')
                        ->pluck('name_show', 'area_code');
                })->required();
            $form->number('rate')->required()->rules('required|numeric|min:0.01');

            $form->display('created_at');
            $form->display('updated_at');

            // 添加保存前验证逻辑
            $form->saving(function (Form $form) {
                if ($form->from_currency == $form->to_currency) {
                    return $form->response()->error('源币种和目标币种不能相同');
                }

                $exists = \App\Models\ExchangeRate::where('from_currency', $form->from_currency)
                    ->where('to_currency', $form->to_currency)
                    ->when($form->model()->id, function ($query) use ($form) {
                        // 编辑时排除当前记录
                        $query->where('id', '<>', $form->model()->id);
                    })
                    ->exists();

                if ($exists) {
                    return $form->response()->error('该汇率对已存在');
                }
            });
        });
    }
}
