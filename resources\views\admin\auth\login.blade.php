<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ config('admin.name', 'Dcat Admin') }} | 登录</title>
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">

    <!-- 使用CDN资源 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    
    <style>
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-box {
            margin: 7% auto;
            width: 360px;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 25px;
        }
        .login-logo a {
            color: #fff;
            font-size: 35px;
            font-weight: 300;
            text-decoration: none;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .login-box-body {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .login-box-msg {
            margin: 0 0 20px 0;
            text-align: center;
            padding: 0;
            color: #666;
            font-size: 16px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #d2d6de;
            box-shadow: none;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: #667eea;
            border-color: #667eea;
            border-radius: 5px;
            padding: 10px 20px;
            width: 100%;
            font-size: 16px;
            transition: all 0.3s;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            border-color: #5a6fd8;
            transform: translateY(-1px);
        }
        .input-group-addon {
            background: #f4f4f4;
            border: 1px solid #d2d6de;
            border-right: none;
            color: #555;
            font-weight: bold;
            min-width: 80px;
            text-align: center;
        }
        .has-error .form-control {
            border-color: #dd4b39;
        }
        .help-block {
            color: #dd4b39;
            font-size: 12px;
            margin-top: 5px;
        }
        .checkbox {
            margin: 15px 0;
        }
        .checkbox label {
            font-weight: normal;
            color: #666;
        }
        .alert {
            padding: 10px 15px;
            margin-bottom: 15px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
    </style>
</head>
<body class="hold-transition login-page">
<div class="login-box">
    <div class="login-logo">
        <a href="{{ admin_url('/') }}">
            {!! config('admin.name', 'Dcat Admin') !!}
        </a>
    </div>
    
    <div class="login-box-body">
        <p class="login-box-msg">请登录您的账户</p>

        <!-- 错误提示区域 -->
        @if(session('error_message'))
            <div class="alert alert-danger" style="margin-bottom: 15px;">
                {{ session('error_message') }}
            </div>
        @endif

        <form action="{{ admin_url('auth/login') }}" method="post" id="login-form">
            {{ csrf_field() }}
            
            <!-- 用户名 -->
            <div class="form-group has-feedback {{ $errors->has('username') ? 'has-error' : '' }}">
                <input type="text" 
                       class="form-control" 
                       placeholder="用户名"
                       name="username" 
                       value="{{ old('username') }}"
                       required>
                <span class="fa fa-user form-control-feedback"></span>
                @if($errors->has('username'))
                    <span class="help-block">{{ $errors->first('username') }}</span>
                @endif
            </div>
            
            <!-- 密码 -->
            <div class="form-group has-feedback {{ $errors->has('password') ? 'has-error' : '' }}">
                <input type="password" 
                       class="form-control" 
                       placeholder="密码"
                       name="password"
                       required>
                <span class="fa fa-lock form-control-feedback"></span>
                @if($errors->has('password'))
                    <span class="help-block">{{ $errors->first('password') }}</span>
                @endif
            </div>
            
            <!-- 数学验证码 -->
            <div class="form-group has-feedback {{ $errors->has('math_captcha') ? 'has-error' : '' }}">
                <div class="input-group">
                    <span class="input-group-addon">{{ $math_question }}</span>
                    <input type="text"
                           class="form-control"
                           placeholder="请输入计算结果"
                           name="math_captcha"
                           required>
                </div>
                @if($errors->has('math_captcha'))
                    <span class="help-block">{{ $errors->first('math_captcha') }}</span>
                @endif
            </div>
            
            <!-- 记住我 -->
            <div class="row">
                <div class="col-xs-8">
                    <div class="checkbox icheck">
                        <label>
                            <input type="checkbox" name="remember" value="1"> 记住我
                        </label>
                    </div>
                </div>
                <div class="col-xs-4">
                    <button type="submit" class="btn btn-primary btn-block">登录</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>

<script>
// 表单提交处理
$(document).ready(function() {
    $('#login-form').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $btn = $form.find('button[type="submit"]');
        var formData = $form.serialize();

        $btn.prop('disabled', true).text('登录中...');

        $.ajax({
            url: $form.attr('action'),
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('登录响应:', response); // 调试信息

                if (response.status === true) {
                    // 登录成功，跳转到后台首页
                    window.location.href = response.redirect || '{{ admin_url("/") }}';
                } else {
                    // 服务器返回失败状态
                    $btn.prop('disabled', false).text('登录');
                    showErrorDialog(response.message || '登录失败');
                }
            },
            error: function(xhr) {
                console.log('登录错误:', xhr.status, xhr.responseText); // 调试信息
                $btn.prop('disabled', false).text('登录');

                if (xhr.status === 422) {
                    var response = xhr.responseJSON;
                    if (response && response.message) {
                        // 显示美观的错误弹窗
                        showErrorDialog(response.message, function() {
                            // 刷新表单（重新生成数学验证码）
                            if (response.reload) {
                                window.location.reload();
                            }
                        });
                    }
                } else if (xhr.status === 200) {
                    // 可能是重定向响应，直接跳转
                    window.location.href = '{{ admin_url("/") }}';
                } else {
                    showErrorDialog('登录失败，请重试');
                }
            }
        });
    });

    // 美观的错误弹窗函数
    function showErrorDialog(message, callback) {
        var overlay = $('<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:9999;display:flex;align-items:center;justify-content:center;"></div>');
        var dialog = $('<div style="background:white;padding:30px;border-radius:10px;box-shadow:0 10px 30px rgba(0,0,0,0.3);max-width:400px;text-align:center;"></div>');
        var icon = $('<div style="color:#e74c3c;font-size:48px;margin-bottom:15px;">⚠️</div>');
        var text = $('<div style="color:#333;font-size:16px;margin-bottom:20px;line-height:1.5;">' + message + '</div>');
        var button = $('<button style="background:#667eea;color:white;border:none;padding:10px 30px;border-radius:5px;cursor:pointer;font-size:14px;">确定</button>');

        button.click(function() {
            overlay.remove();
            if (callback) callback();
        });

        dialog.append(icon).append(text).append(button);
        overlay.append(dialog);
        $('body').append(overlay);

        // 点击遮罩层关闭
        overlay.click(function(e) {
            if (e.target === overlay[0]) {
                overlay.remove();
                if (callback) callback();
            }
        });
    }
});
</script>
</body>
</html>
