<?php

namespace App\Http\Controllers\Api;


use App\Models\BigTrade;
use App\Models\BigTradeOrder;
use App\Models\ExchangeRate;
use App\Models\MoneyChange;
use App\Models\Product;
use App\Models\Users;
use App\Models\UsersInfo;
use App\Services\JiangshanService;
use App\Services\TwelvedataNewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * 红利
 */
class BigTradeController extends ApiBaseController
{

    /**
     * 红利列表
     */
    public function bigTradeList(Request $request)
    {
        $res = BigTrade::query()
            ->where('state', 1)
            ->pluck('product_id', 'id')
            ->toArray();
        if (empty($res)) {
            return $this->returnMsg(200, __('api.请求成功'), []);
        }

        $products = Product::query()->whereIn('id', array_values($res))->get();

        foreach ($products as $product) {
            // 获取产品的最新数据
            if ($product->pro_type == 1) {
                $Jiangshan = new JiangshanService();
                $Jiangshan->get_new_data_serices($product);
            } else {
                TwelvedataNewService::quoteByOne($product);
            }
        }

        $data = BigTrade::query()
            ->with(['product' => function ($query) {
                $query->select('id', 'symbol', 'name', 'price', 'country');
            }])
            ->whereIn('id', array_keys($res))
            ->select(['id', 'buy_in_price', 'product_id'])
            ->get();
        return $this->returnMsg(200, __('api.请求成功'), $data);
    }

    /**
     * 大宗交易-购买
     */
    public function submitBigTrade(Request $request)
    {

//        $validatedData = $request->validate([
//            'id' => ['required', 'integer', 'max:255'],
//            'num' => ['required', 'integer'],
//        ]);

        // 验证是否开盘？ todo

        $num = $request->input('num');
        $devidedId = $request->input('id');
        if ($num < 1) {
            return $this->returnMsg(502, __('api.最低买入数量为1股'));
        }

        $dataModel = BigTrade::query()->where('state', 1)->where('id', $devidedId)->first();

        if (!$dataModel) {
            return $this->returnMsg(502, __('api.请选择正确的大宗交易股票'));
        }
        $product = Product::query()->where('id', $dataModel->product_id)->first();


        $userId = $request->user()->id;
        $user = Users::find($userId);
        if ($user->auth_status != 2) {
            return $this->returnMsg(200, __('api.用户未完成实名认证，请先完成认证'));
        }

        // 买入数量
        // todo 需要按股数计算还是还张数计算
        $buyNums = $num;
        $buy_cost_currency = $buy_cost = $dataModel->buy_in_price * $buyNums;

        DB::beginTransaction();
        try {
            $userInfo = UsersInfo::query()->where('user_id', $user->id)->lockForUpdate()->first();

            // 获取对应的费率，没有配置就获取全局的
            $rate = get_buy_fee_rate($user->id);

            // todo: 计算手续费
            $buy_fees_currency = $buy_fees = buy_commission($buy_cost, $rate);

            // todo: 西班牙市值超过10亿欧元的股票需要加多一个印花税
            // 印花税：0.2%
            $stamp_duty_currency = $stamp_duty = 0;
            if ($product->country == 'sp' && in_array($product->symbol, ['CCEP', 'ITX', 'SAN', 'TEF', 'ITX', 'REP', 'ENG', 'REE', 'BME', 'FER'])) {
                $stamp_duty_currency = $stamp_duty = round($buy_cost * 0.002, 2); // 印花税
            }

            // 如果当前产品不是欧元结算，则需要去计算欧元成本
            $exchange_rate = 1;
            if (in_array($product->pro_type, [2, 3, 4, 5]) || in_array($product->country, ['us', 'en', 'jp', 'my'])) {
                if (in_array($product->pro_type, [2, 3, 4, 5])) {
                    // todo: 调用接口获取一次实时的接口

                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', 'USD')
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }else{
                    // 获取当前产品的货币与欧元的汇率
                    $exchangeRate = ExchangeRate::where('from_currency', $product->currency)
                        ->where('to_currency', 'EUR')
                        ->firstOrFail();
                }

                // 要买入的金额
                // 实时 EUR/USD 汇率,
                // 价差值
                $buy_cost = calculateEuroCost($buy_cost, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                $buy_fees = calculateEuroCost($buy_fees, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                $stamp_duty = calculateEuroCost($stamp_duty, $exchangeRate->rate, $user->spread > 0 ? $user->spread : 0.00036);
                $exchange_rate = $exchangeRate->rate;
            }

            // todo: 默认扣欧元，所以不需要再请求这个
            $before = $userInfo->eur_balance;

            // 买入本金 = 买入成本 + 手续费 + 印花税
            $freeze_total = $buy_cost + $buy_fees + $stamp_duty;
            if ($before < $freeze_total) {
                return $this->returnMsg(502, __('api.用户资金不足'));
            }

            $userInfo->eur_balance = $before - $freeze_total;
            $userInfo->eur_dongjie += $buy_cost;
            $userInfo->save();

            // 生成订单号
            $out_trade_no = generateUniqueOrderNumber();
            // 添加大宗交易订单
            BigTradeOrder::create([
                'order_sn' => $out_trade_no,
                'user_id' => $user->id,
                'product_id' => $product->id,
                'buy_in_price' => $dataModel->buy_in_price, // 申购价格
                'buy_in_num' => $buyNums, // 申购数量
                'buy_in_market_val' => $buy_cost_currency, // 申购市值(成本)
                'buy_in_fee' => $buy_fees_currency, // 申购手续费
                'buy_in_fee_rate' => $rate, // 申购手续费费率
                'lever' => $dataModel->buy_in_ratio, // 杠杆
                'state' => 0,
                'success_num' => 0,
                'success_fee' => 0,
                'exchange_rate' => $exchange_rate, // 货币兑换汇率(买入时)
                'user_spread' => $user->spread > 0 ? $user->spread : 0.00036, // 用户汇率价差
                'stamp_duty' => $stamp_duty_currency, // 西班牙印花税
            ]);

            // 添加资金流水
            MoneyChange::create([
                'money_type' => 'EUR',
                'user_id' => $user->id,
                'order_sn' => $out_trade_no,
                'product_name' => $product->name,
                'detailed' => "大宗交易申购|代码|{$product->symbol}|名称|{$product->name}",
                'type' => MoneyChange::TYPE_TRADE_BIG_TRADE,
                'before_money' => $before,
                'money' => -$buy_cost,
                'after_money' => $before - $buy_cost,
            ]);

            if ($buy_fees > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $user->id,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "大宗交易申购手续费|代码|{$product->symbol}|名称|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE_BIG_TRADE,
                    'before_money' => $before - $buy_fees,
                    'money' => -$buy_fees,
                    'after_money' => $before - $buy_cost - $buy_fees,
                ]);
            }

            if ($stamp_duty > 0) {
                // 添加资金流水-手续费
                MoneyChange::create([
                    'money_type' => 'EUR',
                    'user_id' => $user->id,
                    'order_sn' => $out_trade_no,
                    'product_name' => $product->name,
                    'detailed' => "大宗交易申购印花税|代码|{$product->symbol}|名称|{$product->name}",
                    'type' => MoneyChange::TYPE_TRADE_FEE_BIG_TRADE,
                    'before_money' => $before - $buy_cost - $buy_fees,
                    'money' => -$stamp_duty,
                    'after_money' => $before - $buy_cost - $buy_fees - $stamp_duty,
                ]);
            }

            DB::commit();
            return $this->returnMsg(200, __('api.购买成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(502, __('api.购买失败') . ":" . $e->getMessage());
        }
    }

    /**
     * 日内交易订单
     */
    public function order(Request $request)
    {
        $user = $request->user();

        $res = BigTradeOrder::query()
            ->where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->get()->toArray();

        foreach ($res as &$v) {
            $product = Product::query()->where('id', $v['product_id'])->select(['id', 'name', 'symbol', 'price'])->first();
            $v['product'] = $product;
        }

        return $this->returnMsg(200, __('api.请求成功'), $res);
    }

}
