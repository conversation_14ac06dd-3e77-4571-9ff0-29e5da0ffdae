<?php

namespace App\Admin\Controllers;

use App\Admin\Renderable\AllProductTable;
use App\Admin\Renderable\TraderTable;
use App\Admin\Repositories\TraderArticle;
use App\Models\Product;
use App\Models\Trader;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;

class TraderArticleController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TraderArticle(), function (Grid $grid) {

            $adminId = Admin::user()->id;

            $model = $grid->model()
                ->with(['trader'])
                ->whereHas('trader', function ($query) use ($adminId) {
                    $query->where('admin_id', $adminId);
                });
            $model->orderBy('id', 'desc');

            $grid->column('id')->sortable();
            $grid->column('trader.name', '交易员');
            $grid->column('image')->image('', 100, 100);
            $grid->column('product_id')->display(function ($product_id) {
                if ($product_id) {
                    $product = Product::find($product_id);
                    return $product->name;
                } else {
                    return '';
                }
            });
            $grid->column('en_content', '文章内容（英语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->trader->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->en_content);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('es_content', '文章内容（西班牙语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->trader->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->es_content);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });
            $grid->column('de_content', '文章内容（德语）')
                ->display('查看') // 设置按钮名称
                ->modal(function ($modal) {
                    // 设置弹窗标题
                    $modal->title('查看 '.$this->trader->name);

                    // 自定义图标
                    $modal->icon('fa-eye');

                    $card = new Card(null, $this->de_content);

                    return "<div style='padding:10px 10px 0'>$card</div>";
                });

            $grid->column('is_top')->using([0 => '否', 1 => '是'])->badge([0 => 'primary', 1 => 'success']);
            $grid->column('is_hot')->using([0 => '否', 1 => '是'])->badge([0 => 'primary', 1 => 'success']);
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new TraderArticle(), function (Show $show) {
            $show->field('id');
            $show->field('trader_id');
            $show->field('product_id');
            $show->field('en_content','文章内容（英语）');
            $show->field('es_content','文章内容（西班牙语）');
            $show->field('de_content','文章内容（德语）');
            $show->field('image')->image('', 100, 100);
            $show->field('is_top');
            $show->field('is_hot');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new TraderArticle(), function (Form $form) {
            $form->display('id');
            $form->selectTable('trader_id')
                ->from(TraderTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Trader::class, 'id', 'name') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择交易员']);
            $form->selectTable('product_id')
                ->from(AllProductTable::make(['id' => $form->getKey()])) // 设置渲染类实例，
                ->model(Product::class, 'id', 'name'); // 设置编辑数据显示
            $form->editor('en_content','文章内容（英语）')->required();
            $form->editor('es_content','文章内容（西班牙语）')->required();
            $form->editor('de_content','文章内容（德语）')->required();
            $form->image('image')->required()
                ->autoUpload()
                ->rules('required|max:6000', ['required' => '请上传头像图片']);
            $form->switch('is_top');
            $form->switch('is_hot');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
