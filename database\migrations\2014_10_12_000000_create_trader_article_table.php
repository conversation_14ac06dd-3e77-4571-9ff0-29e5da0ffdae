<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTraderArticleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('trader_article', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->integer('trader_id')->index()->comment('交易员id');
            $table->integer('product_id')->nullable(true)->comment('产品id，可以为空，表示不挂载任何产品');
            $table->text('en_content')->nullable(true)->comment('文章内容（英语）');
            $table->text('es_content')->nullable(true)->comment('文章内容（西班牙语）');
            $table->text('de_content')->nullable(true)->comment('文章内容（德语）');
            $table->string('image')->nullable(true)->comment('文章图片');
            $table->tinyInteger('is_top')->default(0)->comment('是否置顶，1=是，0=否');
            $table->tinyInteger('is_hot')->default(0)->comment('是否热门，1=是，0=否');

            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->index()->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}trader_article auto_increment=1 comment '交易员文章表'");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('trader_article');
    }
}
