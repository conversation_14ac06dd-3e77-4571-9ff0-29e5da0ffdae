<?php

namespace App\Admin\Actions\Grid\Trader;

use App\Admin\Actions\Grid\User\Authenticatable;
use App\Admin\Actions\Grid\User\HasPermissions;
use App\Admin\Actions\Grid\User\Model;
use App\Admin\Forms\Trader\CreateOrderForm;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Widgets\Modal;

class CreateOrder extends RowAction
{
    /**
     * @return string
     */
    protected $title = '跟单建仓';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Modal
     */
    public function render()
    {

        $form = CreateOrderForm::make()->payload(['id' => $this->getKey()]);

        return Modal::make()
            ->lg()
            ->title('跟单建仓')
            ->body($form)
            ->button('<a class="btn btn-sm btn-warning" style="color: #f4f4f4">跟单建仓</a>');

    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        // return ['你确定要删除此行内容吗？', '弹窗内容'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }


}
