<?php

namespace App\Admin\Renderable;

use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Users;

class UserTable extends LazyRenderable
{
    public function grid(): Grid
    {
        // 获取外部传递的参数
        $id = $this->id;

        return Grid::make(new Users(), function (Grid $grid) {

            $model = $grid->model();
            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if(!empty($adminIds)){
                $model->whereIn('parent_id', $adminIds);
            }

            $grid->column('id');
            $grid->column('email');

            $grid->quickSearch(['id', 'email']);

            $grid->paginate(10);
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('email')->width(4);
            });
        });
    }
}
