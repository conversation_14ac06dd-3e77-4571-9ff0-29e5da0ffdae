<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;

class MathCaptchaService
{
    /**
     * 生成数学验证码
     */
    public static function generate()
    {
        $num1 = rand(1, 9);
        $num2 = rand(1, 9);
        $operators = ['+', '-'];
        $operator = $operators[array_rand($operators)];
        
        if ($operator === '+') {
            $result = $num1 + $num2;
        } else {
            // 确保减法结果为正数
            if ($num1 < $num2) {
                $temp = $num1;
                $num1 = $num2;
                $num2 = $temp;
            }
            $result = $num1 - $num2;
        }
        
        // 存储答案到session
        Session::put('math_captcha', (string)$result);
        
        return "{$num1} {$operator} {$num2} = ?";
    }
    
    /**
     * 验证数学验证码
     */
    public static function check($input)
    {
        if (empty($input)) {
            return false;
        }
        
        $sessionAnswer = Session::get('math_captcha');
        $result = $sessionAnswer && (string)$input === $sessionAnswer;
        
        // 验证后清除session中的答案
        if ($result) {
            Session::forget('math_captcha');
        }
        
        return $result;
    }
    
    /**
     * 获取当前验证码问题（用于显示）
     */
    public static function getQuestion()
    {
        // 如果session中没有验证码，生成一个新的
        if (!Session::has('math_captcha')) {
            return self::generate();
        }
        
        // 这里我们需要重新生成，因为我们只存储了答案
        return self::generate();
    }
}
