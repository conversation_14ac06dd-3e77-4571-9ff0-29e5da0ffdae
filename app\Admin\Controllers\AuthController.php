<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Http\Controllers\AuthController as BaseAuthController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthController extends BaseAuthController
{
    /**
     * 处理登录请求
     */
    public function postLogin(Request $request)
    {
        // 验证输入（包括数学验证码）
        $validator = Validator::make($request->all(), [
            $this->username() => 'required',
            'password' => 'required',
            'math_captcha' => 'required|math_captcha',
        ], [
            'math_captcha.required' => '请输入计算结果',
            'math_captcha.math_captcha' => '计算结果错误',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            $errorMessage = implode('；', $errors);

            // 如果是AJAX请求，返回JSON响应
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'status' => false,
                    'message' => $errorMessage,
                    'reload' => true
                ], 422);
            }

            // 普通请求返回带错误信息的页面
            return back()->withInput()->with('error_message', $errorMessage);
        }

        // 验证用户名密码
        $username = $request->input('username');
        $password = $request->input('password');
        $remember = $request->get('remember', false);

        // 记录登录尝试
        \Log::info('登录尝试', [
            'username' => $username,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // 尝试登录
        if (auth('admin')->attempt(['username' => $username, 'password' => $password], $remember)) {
            // 登录成功
            \Log::info('登录成功', ['username' => $username]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'status' => true,
                    'message' => '登录成功',
                    'redirect' => admin_url('/')
                ]);
            }

            // 非AJAX请求，直接重定向
            return redirect(admin_url('/'));
        }

        // 登录失败
        \Log::warning('登录失败', ['username' => $username]);
        $errorMessage = '用户名或密码错误';

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'status' => false,
                'message' => $errorMessage,
                'reload' => true
            ], 422);
        }

        return back()->withInput()->with('error_message', $errorMessage);
    }

    /**
     * 获取登录页面
     */
    public function getLogin(Content $content)
    {
        if ($this->guard()->check()) {
            return redirect(admin_url('/'));
        }

        // 返回自定义登录视图
        return view('admin.auth.login', [
            'math_question' => \App\Services\MathCaptchaService::generate(),
        ]);
    }

    /**
     * 获取用户名字段名
     */
    public function username()
    {
        return 'username';
    }
}
