<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\User\Balance;
use App\Admin\Forms\ChangeJoinStateForm;
use App\Admin\Renderable\UserWalletModal;
use App\Admin\Repositories\Users;
use App\Models\Users as UserModel;
use App\Services\AdminUserService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Admin;

class UsersController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
//        $grid->column('wallet.agent_balance', '代理钱包[RM]')->display(function ($agent_balance) {
//            return $agent_balance . ' RM';
//        });;

//        $grid->column('status')->using([1 => '正常', 0 => '禁用']);

        return Grid::make(new Users(), function (Grid $grid) {
            $model = $grid->model()->with(['wallet', 'parent']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereIn('parent_id', $adminIds);
            }

            $model->orderBy('id', 'desc');


            $grid->column('id')->sortable();
            $grid->column('area_code_id');
            $grid->column('fee_rate')->display(function ($feeRate) {
                if ($feeRate > 0) {
                    return "{$feeRate}";
                } else {
                    return "全局";
                }
            });
//            $grid->column('area_code');
//            $grid->column('parent_id');
            $grid->column('parent.username', '上级');
            $grid->column('email');
//            $grid->column('surname');
//            $grid->column('name');
//            $grid->column('mobile');
//            $grid->column('nickname');
//            $grid->column('avatar')->image();
            $grid->column('wallet', '余额')
                ->display('查看钱包')
                ->modal(function ($modal) {
                    $modal->title('用户 ' . $this->email . ' 的钱包');
                    return UserWalletModal::make()->payload(['id' => $this->id]);
                });

//            $grid->column('address');
//            $grid->column('birthday');
//            $grid->column('id_card_img')->image();
//            $grid->column('id_card_num');
            $grid->column('个人信息')->display('查看')->modal('个人信息', function () {
                // 数据兜底处理
                $surname = $this->surname ?: '未填写';
                $name = $this->name ?: '未填写';
                $nickname = $this->nickname ?: '未填写';
                $birthday = $this->birthday ? date('Y-m-d', strtotime($this->birthday)) : '未填写';
                $address = $this->address ?: '未填写';
                $mobile = $this->mobile ?: '未填写';
                $id_card_num = $this->id_card_num ?: '未填写';

                $avatar = $this->avatar
                    ? "<img src={$this->avatar} class='avatar-img'>"
                    : "<div class='avatar-placeholder'>无头像</div>";

                $idCardImg = $this->id_card_img
                    ? "<img src={$this->id_card_img} class='id-card-img'>"
                    : "<div class='idcard-placeholder'>无证照</div>";

                return <<<HTML
    <style>
        .avatar-img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .avatar-placeholder {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: #f0f0f0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            border: 1px solid #ddd;
        }
        .id-card-img {
            max-width: 200px;
            border: 1px solid #ddd;
            padding: 2px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .idcard-placeholder {
            width: 200px;
            height: 120px;
            border: 1px dashed #ccc;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }
    </style>
    <div style="display: flex; gap: 15px; align-items: flex-start;">
        <div>{$avatar}</div>
        <div>
            <p><strong>姓：</strong> {$surname}</p>
            <p><strong>名：</strong> {$name}</p>
            <p><strong>昵称：</strong> {$nickname}</p>
            <p><strong>出生日期：</strong> {$birthday}</p>
            <p><strong>地址：</strong> {$address}</p>
            <p><strong>手机号：</strong> {$mobile}</p>
            <p><strong>证照：</strong><br>{$idCardImg}</p>
        </div>
    </div>
    HTML;
            });


//            $grid->column('password');
//            $grid->column('pay_password');
//            $grid->column('language');
            $grid->column('currency');
            $grid->column('score');
            $grid->column('auth_status')->using([0 => '未认证', 1 => '待审核', 2 => '已认证', 3 => '认证失败'])->dot(
                [
                    1 => 'primary',
                    2 => 'success',
                    3 => 'danger',
                ],
                'gray' // 第二个参数为默认值
            );
//            $grid->column('login_status', '登录状态')->switch();
            $grid->column('login_status')->display('开启')->modal(function (Grid\Displayers\Modal $modal) {
                // 标题
                $modal->title('弹窗标题');
                // 自定义图标
                $modal->icon('feather icon-edit');
                // 传递当前行字段值
                return ChangeJoinStateForm::make()->payload(['id' => $this->id]);
            });

//            $grid->column('trade_status', '交易状态')->switch();
//            $grid->column('lever_status', '杠杆状态')->switch();
//            $grid->column('join_ip');
//            $grid->column('join_time');
            $grid->column('login_ip');
            $grid->column('last_login_time');
//            $grid->column('last_error_time');
//            $grid->column('login_error_num');
//            $grid->column('invite_code');
//            $grid->column('type');
//            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->actions(function (Grid\Displayers\Actions $actions) {

                // append一个操作
                if (Admin::user()->can('change-money')) {
                    $actions->append(new Balance());
                }

                // 没有`delete`权限的角色不显示删除按钮
                $actions->disableDelete();
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('email');
                $filter->equal('mobile');
                $filter->select('auth_status', '是否认证')->options(function () {
                    return [
                        0 => '未认证',
                        1 => '待审核',
                        2 => '已认证',
                        3 => '认证失败',
                    ];
                })->placeholder('请选择认证状态');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Users(), function (Show $show) {
            $show->field('id');

            // 基本信息分组
            $show->panel()
                ->title('基本信息')
                ->tools(function ($tools) {
                    $tools->disableEdit();
//                    $tools->disableList();
                    $tools->disableDelete();
                });

            // 地区币种信息
            $show->field('area_code_id', '地区/币种');
            $show->field('fee_rate');

            // 用户基本信息
            $show->field('email', '电子邮箱');
            $show->field('surname', '姓氏');
            $show->field('name', '名字');
            $show->field('mobile', '手机号码');
            $show->field('nickname', '昵称');
            $show->field('avatar', '头像')->image();
            $show->field('address', '联系地址');
            $show->field('birthday', '生日');

            // 安全信息分组
            $show->newline();
            $show->divider('安全信息');

            // 身份认证信息
            $show->field('id_card_img', '身份证/护照图片')->image();
            $show->field('id_card_num', '身份证/护照号码');

            // 密码信息（仅显示占位符）
            $show->field('password', '密码');
            $show->field('pay_password', '支付密码');

            // 用户偏好设置
            $show->field('language', '语言')->using([
                'zh-CN' => '简体中文',
                'en' => '英文',
                'de' => '德文',
                'fr' => '法文',
                'ja' => '日文',
            ]);
            $show->field('currency', '默认货币');

            // 系统信息分组
            $show->newline();
            $show->divider('系统信息');

            // 关联管理员
            $show->field('parent_id', '所属业务ID');
            $show->field('parent_admin', '所属业务管理员')->unescape()->as(function () {
                $parentId = $this->parent_id;
                if (!$parentId || $parentId == 0) return '-';

                try {
                    // 使用原生 SQL 查询获取管理员用户名
                    $admin = DB::table(config('admin.database.users_table'))
                        ->where('id', $parentId)
                        ->first(['username']);

                    return $admin ? "<span class='label label-primary' style='color: #000'>{$admin->username}</span>" : $parentId;
                } catch (\Exception $e) {
                    return "<span class='label label-warning'>{$parentId}</span>";
                }
            });

//            $show->field('agent_id', '所属代理ID');
//            $show->field('agent_admin', '所属代理管理员')->unescape()->as(function () {
//                $agentId = $this->agent_id;
//                if (!$agentId || $agentId == 0) return '-';
//
//                try {
//                    // 使用原生 SQL 查询获取管理员用户名
//                    $admin = DB::table(config('admin.database.users_table'))
//                        ->where('id', $agentId)
//                        ->first(['username']);
//
//                    return $admin ? "<span class='label label-success' style='color: #000'>{$admin->username}</span>" : $agentId;
//                } catch (\Exception $e) {
//                    return "<span class='label label-warning'>{$agentId}</span>";
//                }
//            });

            // 用户状态
            $show->field('score', '信誉分');
            $show->field('auth_status', '认证状态')->using([
                0 => '未认证',
                1 => '待审核',
                2 => '已认证',
            ]);
            $show->field('login_status', '登录状态');
            $show->field('trade_status', '交易状态');
            $show->field('lever_status', '杠杆状态');

            // 用户类型
            $show->field('type', '用户类型')->using([
                0 => '普通用户',
                1 => '代理商',
            ]);
            $show->field('invite_code', '邀请码');

            // 登录相关信息
            $show->field('join_ip', '注册IP');
            $show->field('join_time', '注册时间');
            $show->field('login_ip', '登录IP');
            $show->field('last_login_time', '最后登录时间');
            $show->field('last_error_time', '最后错误时间');
            $show->field('login_error_num', '登录错误次数');

            $show->newline();
            $show->divider();
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Users(), function (Form $form) {

            // ✅ 禁用编辑页右上角“删除”按钮
            $form->disableDeleteButton();

            $form->display('id');

            // 基本信息分组
            $form->column(4, function (Form $form) {
                $form->fieldset('基本信息', function (Form $form) {
                    // 地区币种选择
                    $form->select('area_code_id', '地区/币种')
                        ->options(function () {
                            return \App\Models\AreaConfig::orderBy('weight', 'desc')->pluck('name', 'id');
                        })
                        ->required();

                    // 用户基本信息
                    $form->text('email', '电子邮箱')
                        ->rules('required|email|max:60')
                        ->required();
                    $form->text('surname', '姓氏')
                        ->rules('max:32');
                    $form->text('name', '名字')
                        ->rules('max:32');
                    $form->mobile('mobile', '手机号码')
                        ->options(['mask' => '999 9999 9999'])
                        ->rules('max:20');
                    $form->text('nickname', '昵称')
                        ->rules('max:32');
                    $form->image('avatar', '头像')
                        ->autoUpload()
                        ->rules('max:6000');
                    $form->textarea('address', '联系地址')
                        ->rows(2)
                        ->rules('max:200');
                    $form->date('birthday', '生日')
                        ->format('YYYY-MM-DD');
                });
            });

            // 安全信息分组
            $form->column(4, function (Form $form) {
                $form->fieldset('安全信息', function (Form $form) {
                    // 身份认证信息
                    $form->image('id_card_img', '身份证/护照图片')
                        ->autoUpload()
                        ->rules('max:6000');
                    $form->text('id_card_num', '身份证/护照号码')
                        ->rules('max:60');

                    // 密码设置
                    if ($form->isCreating()) {
                        $form->password('password', '密码')
                            ->rules('required|min:6|max:64')
                            ->required();
                        $form->password('pay_password', '支付密码')
                            ->rules('required:min:6|max:32');
                    } else {
                        unset($form->model()->pay_password); // 💥 防止绑定模型字段值
                        $form->password('password', '密码')
                            ->rules('nullable|min:6|max:64')
                            ->help('不填写则不修改密码');
                        $form->password('pay_password', '支付密码')
                            ->rules('nullable|min:6|max:32')
                            ->help('不填写则不修改支付密码');
                    }

                    // 用户偏好设置
                    $form->select('language', '语言')
                        ->options([
                            'zh-CN' => '简体中文',
                            'en' => '英文',
                            'de' => '德文',
                            'fr' => '法文',
                            'ja' => '日文',
                            'sp' => '西班牙语',
                        ])
                        ->default('zh-CN')->required();
                    $form->select('currency', '默认货币')
                        ->options(function () {
                            $res = [];
                            return \App\Models\AreaConfig::where('state', 1)->pluck('name_show', 'area_code');
                        })
                        ->default('USD')->required();
                    $form->text('fee_rate')->default(0)->help('0代表不设置，则按用户自己的费率，否则按全局的费率')->rules('required|numeric|min:0');
                    $form->text('spread', '汇率价差')->default(0)->help('0代表全局的费率0.00036')->rules('required|numeric|min:0');
                });
            });

            // 系统信息分组
            $form->column(4, function (Form $form) {
                $form->fieldset('系统信息', function (Form $form) {

                    // 关联管理员
//                    $form->select('parent_id', '所属业务')
//                        ->options(function () {
//                            return \Dcat\Admin\Models\Administrator::pluck('username', 'id');
//                        })
//                        ->rules('required')
//                        ->help('选择用户所属的业务管理员');
//                    $form->select('agent_id', '所属代理')
//                        ->options(function () {
//                            return \Dcat\Admin\Models\Administrator::pluck('username', 'id');
//                        })
//                        ->rules('required')
//                        ->help('选择用户所属的代理管理员');

                    // 用户状态
                    $form->number('score', '信誉分')
                        ->min(0)
                        ->max(100)
                        ->default(100);
                    $form->select('auth_status', '认证状态')
                        ->options([
                            0 => '未认证',
                            1 => '待审核',
                            2 => '已认证',
                            3 => '认证失败',
                        ])
                        ->default(0);

                    // 用户状态开关
                    $form->switch('login_status', '登录状态')
                        ->default(1);
                    $form->switch('trade_status', '交易状态')
                        ->default(1);
                    $form->switch('lever_status', '杠杆状态')
                        ->default(1);

                    // 用户类型
//                    $form->select('type', '用户类型')
//                        ->options([
//                            0 => '普通用户',
//                            1 => '代理商',
//                        ])
//                        ->default(0);

                    // 邀请码
//                    $form->text('invite_code', '邀请码')
//                        ->rules('max:12');
                    // 只读信息
                    if ($form->isEditing()) {
                        $form->display('join_ip', '注册IP');
                        $form->display('join_time', '注册时间');
                        $form->display('login_ip', '登录IP');
                        $form->display('last_login_time', '最后登录时间');
                        $form->display('last_error_time', '最后错误时间');
                        $form->number('login_error_num', '登录错误次数');
                    }
                });
            });

//            $form->display('created_at', '创建时间');
//            $form->display('updated_at', '更新时间');

            // 保存前回调
            $form->saving(function (Form $form) {
                // 如果密码字段为空，则不更新密码
                if ($form->isEditing()) {
                    if (!$form->input('password')) {
                        $form->deleteInput('password');
                    } else {
                        $form->input('password', bcrypt($form->input('password')));
                    }

                    if (!$form->input('pay_password')) {
                        $form->deleteInput('pay_password');
                    } else {
                        $form->input('pay_password', bcrypt($form->input('pay_password')));
                    }
                } else {
                    $form->password = bcrypt($form->password);
                    $form->pay_password = bcrypt($form->pay_password);
                }
            });
        });
    }
}
