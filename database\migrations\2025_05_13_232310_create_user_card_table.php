<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserCardTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_card', function (Blueprint $table) {
            $table->integerIncrements('id');
            $table->unsignedInteger('user_id')->index()->comment('用户id');
            $table->tinyInteger('type')->default(1)->comment('类型 1=银行卡 2=加密货币');

            $table->string('bank', 100)->nullable()->comment('银行名称');
            $table->string('bank_owner', 200)->nullable()->comment('持卡人姓名');
            $table->string('bank_address', 200)->nullable()->comment('开户行地址');
            $table->string('bank_card_no', 100)->nullable()->comment('银行卡号');

            $table->string('network')->nullable()->comment('加密货币网络');
            $table->string('usdt_address', 100)->nullable()->comment('U地址');

            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('创建时间');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))->comment('更新时间');
            $table->softDeletes()->index()->comment('删除时间');
        });
        $prefix = \Illuminate\Support\Facades\DB::connection()->getConfig('prefix');
        \Illuminate\Support\Facades\DB::statement("ALTER TABLE {$prefix}user_card auto_increment=1 comment '用户银行卡'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_card');
    }
}
