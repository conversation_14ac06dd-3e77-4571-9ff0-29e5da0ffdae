<?php

return [
    '请求成功' => 'Request Success',
    '请求失败' => 'Request Failed',

    '充值' => 'Recharge',
    '提现' => 'Withdrawal',
    '提现拒绝' => 'Withdrawal Rejected',
    '货币兑换-出' => 'Exchange Out',
    '货币兑换-进' => 'Exchange In',
    '交易删除' => 'Trade Deleted',
    '交易' => 'Trade',
    '红利交易' => 'Dividend Trade',
    '日内交易' => 'Intraday Trade',
    '大宗交易' => 'Block Trade',
    '交易手续费' => 'Trade Fee',
    '交易手续费删除' => 'Trade Fee Deleted',
    '红利交易手续费' => 'Dividend Trade Fee',
    '日内交易手续费' => 'Intraday Trade Fee',
    '大宗交易手续费' => 'Block Trade Fee',
    '证券交易税' => 'Securities Transaction Tax',
    '跟单撤单' => 'Follow Cancel',
    '返还本金' => 'Return Principal',
    '返回手续费' => 'Return Fee',
    '返回印花税' => 'Return Stamp Duty',

    '搜索关键词不能为空' => 'Search keyword cannot be empty',
    '搜索关键词过长' => 'Search keyword is too long',
    '搜索失败:' => 'Search failed:',
    '获取推荐失败:' => 'Failed to get recommendations:',
    '产品不存在' => 'Product Not Found',
    '充值银行卡不存在' => 'The recharge bank card does not exist',
    '充值USDT地址不存在' => 'The USDT deposit address does not exist',
    '请输入充值人姓名' => 'Please enter the name of the depositor',
    '支付密码错误' => 'Wrong payment password',
    '请先设置支付密码' => 'Please set a payment password first',
    '银行卡号位数' => 'Bank card number digits',
    '银行卡号已经被绑定' => 'The bank card number has been bound',
    '地址格式错误' => 'Address format error',
    '地址已被绑定' => 'The address has been bound',
    '请选择绑定类型' => 'Please select a binding type',
    '绑定类型错误' => 'Binding type error',
    '请输入支付密码' => 'Please enter the payment password',
    '请输入银行名称' => 'Please enter the bank name',
    '请输入银行开户人' => 'Please enter the bank account holder',
    '请输入银行卡号' => 'Please enter the bank card number',
    '请输入网络类型' => 'Please enter the network type',
    '请输入USDT地址' => 'Please enter the USDT address',
    '最多只能绑定5张银行卡' => 'A maximum of 5 bank cards can be bound',
    '账户金额不足' => 'Insufficient account balance',
    '银行卡不存在' => 'Bank card does not exist',
    '银行卡归属错误,禁止提款' => 'The bank card ownership is incorrect, withdrawal is prohibited',
    '银行卡不属于自己的,禁止提款' => 'The bank card does not belong to you, withdrawal is prohibited',
    '充值金额不能低于' => 'The recharge amount cannot be less than',
    '充值金额不能高于' => 'The recharge amount cannot be higher than',
    '还没到提现时间' => "It's not time to withdraw money yet",
    '今天提现时间已结束，请明天再来提现' => "Today's withdrawal time has ended, please come back tomorrow to withdraw",
    '每日提款次数达到上限' => "The daily withdrawal limit has been reached",
    '提款金额不能低于' => "The withdrawal amount cannot be less than",
    '提款金额不能高于' => "The withdrawal amount cannot be higher than",
    '不在购买时间' => "Not during purchase time",
    '最低买入数量为1股' => "The minimum purchase quantity is 1 share",
    '请选择正确的红利股票' => "Choose the Right Dividend Stock",
    '当前股票未开始,不支持购买' => "The current stock has not started, and purchase is not supported",
    '当前股票已过期，不支持购买' => "The current stock has expired and cannot be purchased",
    '您还未认证' => "You are not authenticated yet",
    '购买失败' => "Purchase failed",
    '购买成功' => "Purchase successful",
    '用户资金不足，无法进行买入' => "The user does not have enough funds to buy",
    '用户资金不足' => "User funds are insufficient",
    '请选择正确的日内交易股票' => "Please choose the right stock for day trading",
    '请选择正确的大宗交易股票' => "Please select the correct block trading stock",
    '汇率不存在' => "Exchange rate does not exist",
    '兑换币种和目标币种不能相同' => "The exchange currency and target currency cannot be the same",
    '兑换的金额不能是非数字' => "The exchange amount cannot be a non-numeric value",
    '兑换的金额不能小于等于0' => "The exchange amount cannot be less than or equal to 0",
    '提交成功' => "Submission successful",
    '提交失败' => "Submission failed",
    '取消失败' => "Cancellation failed",

    // Product
    '获取榜单失败' => "Failed to get the leaderboard",
    '没有推荐产品' => "No recommended products",
    '参数错误' => 'Parameter error',
    '参数错误-1' => 'Parameter error -1',
    '参数错误-2' => 'Parameter error -2',
    '产品信息未找到' => 'Product information not found',
    '产品信息获取成功' => 'Product information retrieved successfully',
    '产品信息获取失败' => 'Failed to retrieve product information',

    // Recharge
    '充值类型不合法' => 'Recharge type is not valid',
    '充值金额必须大于0' => 'Recharge amount must be greater than 0',
    '充值申请失败，请稍后再试' => 'Recharge application failed, please try again later',
    // userCard


    // User
    '邮箱不能为空' => 'Email cannot be empty',
    '邮箱格式不正确' => 'Email format is incorrect',
    '邮箱已存在' => 'Email already exists',
    '邮箱不存在' => 'Email does not exist',
    '密码不能为空' => 'Password cannot be empty',
    '密码不能低于8位字符' => 'Password cannot be less than 8 characters',
    '密码不能超出32位字符' => 'Password cannot exceed 32 characters',
    '验证码不能为空' => 'Verification code cannot be empty',
    '验证码必须是6位数字' => 'Verification code must be 6 digits',
    '邀请码不能为空' => 'Invite code cannot be empty',
    '邀请码必须是6位数字' => 'Invite code must be 6 digits',
    '类型不能为空' => 'Type cannot be empty',
    '类型必须是注册或重置密码' => 'Type must be registration or password reset',
    '验证码已发送' => 'Verification code has been sent',
    '发送验证码失败' => 'Failed to send verification code',
    '验证码邮件已发送至' => 'Verification code email has been sent to',
    '验证码错误或已过期' => 'Verification code is incorrect or has expired',
    '注册失败，请稍后重试' => 'Registration failed, please try again later',
    '注册成功' => 'Registration successful',
    '账号不存在' => 'Account does not exist',
    '您的账号已被禁止登录，请联系管理员' => 'Your account has been banned from logging in, please contact the administrator',
    '邮箱或密码不正确' => 'Email or password is incorrect',
    '请先验证您的邮箱' => 'Please verify your email first',
    '登录成功' => 'Login successful',
    '密码重置成功' => 'Password reset successful',
    '密码重置失败，请稍后重试' => 'Password reset failed, please try again later',
    '已从所有设备登出' => 'Logged out from all devices',
    '产品类型不正确' => 'Product type is incorrect',
    '未登录或登录已过期' => 'Not logged in or login has expired',
    '登出成功' => 'Logout successful',
    '登出失败，请稍后重试' => 'Logout failed, please try again later',
    '刷新令牌无效或已过期' => 'Refresh token is invalid or has expired',
    '令牌刷新成功' => 'Token refresh successful',
    '令牌刷新失败，请重新登录' => 'Token refresh failed, please log in again',
    '请输入旧登录密码' => 'Please enter the old login password',
    '请输入新登录密码' => 'Please enter the new login password',
    '新登录密码至少8个字符' => 'The new login password must be at least 8 characters',
    '新登录密码和确认密码不一致' => 'The new login password and confirmation do not match',
    '请输入新支付密码' => 'Please enter the new payment password',
    '支付密码至少8个字符' => 'The payment password must be at least 8 characters',
    '支付密码和确认密码不一致' => 'The payment password and confirmation do not match',
    '姓氏不能为空' => 'The last name cannot be empty',
    '姓氏必须是字符串' => 'The last name must be a string',
    '姓氏不能小于2个字符' => 'The last name must be at least 2 characters',
    '姓氏不能超过32个字符' => 'The last name may not be greater than 32 characters',
    '名字不能为空' => 'The first name cannot be empty',
    '名字必须是字符串' => 'The first name must be a string',
    '名字不能小于2个字符' => 'The first name must be at least 2 characters',
    '名字不能超过32个字符' => 'The first name may not be greater than 32 characters',
    '身份证号码不能为空' => 'The ID number cannot be empty',
    '身份证号码格式不正确' => 'The ID number format is incorrect',
    '身份证号码长度不能小于6个字符' => 'The ID number must be at least 6 characters',
    '身份证号码长度不能超过18个字符' => 'The ID number may not be greater than 18 characters',
    '身份证图片不能为空' => 'The ID card image cannot be empty',
    '身份证图片格式不正确' => 'The ID card image format is incorrect',
    '身份证图片不能超过255个字符' => 'The ID card image may not exceed 255 characters',
    '请上传文件' => 'Please upload a file',
    '只能上传图片文件' => 'Only image files are allowed',
    '图片格式仅支持jpeg,png,jpg,gif,svg,webp' => 'Only jpeg, png, jpg, gif, svg, webp formats are supported',
    '图片大小不能超过10MB' => 'The image size may not exceed 10MB',
    '不支持的币种类型' => 'Unsupported currency type',
    '用户余额信息未找到' => 'User balance information not found',
    '旧密码不正确' => 'The old password is incorrect',
    '登录密码修改成功' => 'Login password updated successfully',
    '请输入旧支付密码' => 'Please enter the old payment password',
    '旧支付密码不正确' => 'The old payment password is incorrect',
    '支付密码设置成功' => 'Payment password set successfully',
    '支付密码修改成功' => 'Payment password updated successfully',
    '您已完成实名认证' => 'You have already completed identity verification',
    '实名认证提交成功，请等待审核' => 'Identity verification submitted successfully, please wait for review',
    '文件上传成功' => 'File uploaded successfully',
    '邀请码无效' => 'Invalid invitation code',
    '个人信息更新成功' => 'Personal information updated successfully',


    // userOrder
    '请先登录' => 'Please log in first',
    '订单ID不合法' => 'Order ID is not valid',
    '订单不存在' => 'Order does not exist',
    '只有挂单状态可撤销' => 'Only pending orders can be canceled',
    '产品ID不能为空' => 'Product ID cannot be empty',
    '产品ID必须为整数' => 'Product ID must be an integer',
    '产品ID不存在或状态不合法' => 'Product ID does not exist or is not valid',
    '产品不存在或状态不合法' => 'Product does not exist or is not valid',
    '买入方向不能为空' => 'Buy direction cannot be empty',
    '买入方向必须为整数' => 'Buy direction must be an integer',
    '买入方向不合法' => 'Buy direction is not valid',
    '买入类型不能为空' => 'Buy type cannot be empty',
    '买入类型必须为整数' => 'Buy type must be an integer',
    '买入类型不合法' => 'Buy type is not valid',
    '买入价格不能为空' => 'Buy price cannot be empty',
    '买入价格必须为数字' => 'Buy price must be a number',
    '买入价格不能小于0' => 'Buy price cannot be less than 0',
    '买入数量不能为空' => 'Buy quantity cannot be empty',
    '买入数量必须为数字' => 'Buy quantity must be a number',
    '买入数量不能小于0.001' => 'Buy quantity cannot be less than 0.001',
    '买入数量不能小于1' => 'Buy quantity cannot be less than 1',
    '买入数量必须为整数' => 'Buy quantity must be an integer',
    '买入杠杆不能为空' => 'Buy leverage cannot be empty',
    '买入杠杆必须在1-100之间' => 'Buy leverage must be between 1 and 100',
    '买入杠杆必须为整数' => 'Buy leverage must be an integer',
    '用户资金信息不存在' => 'User fund information does not exist',
    '买入成本必须大于0' => 'Buy cost must be greater than 0',
    '止盈金额必须大于等于0' => 'Take profit amount must be greater than or equal to 0',
    '请在开盘时间进行交易' => 'Please trade during market hours',
    '止损金额必须大于等于0' => 'Stop loss amount must be greater than or equal to 0',
    '用户交易状态异常，请联系客服' => 'User trading status is abnormal, please contact customer service',
    '用户未开启杠杆交易，请先开启' => 'User has not enabled leveraged trading, please enable it first',
    '用户未完成实名认证，请先完成认证' => 'User has not completed real-name authentication, please complete it first',
    '股票' => 'Stock',
    '大宗商品' => 'Commodity',
    'ETF' => 'ETF',
    '外汇' => 'Forex',
    '数字货币' => 'Crypto Currency',
    '平仓' => 'Sell',
    '手续费' => 'Fee',
    '证交费' => 'See Fee',
    '计算出的买入汇率不合法' => 'The calculated buying rate is illegal',
    '计算出的买入汇率不合法:' => 'The calculated buying rate is illegal',
    '印花税' => 'Stamp duty',
    '删除买入订单' => 'Delete Buy Order',
    '删除买入手续费' => 'Delete Buy Fee',
    '删除买入印花税' => 'Delete Buy Stamp Duty',
    '买涨' => 'Buy Up',
    '买跌' => 'Buy Down',
    '市价' => 'Market Price',
    '限价' => 'Limit Price',
    '未知产品' => 'Unknown Product',
    '未知状态' => 'Unknown Status',
    '平仓成功' => 'Unknown Status',
    '平仓失败' => 'Unknown Status',
    '撤销订单' => 'Cancel Order',

    // Message
    "尊敬的会员" => "Dear members",
    "您申购的新股" => "The new shares you subscribed",
    "代码" => "Stock Code",
    "买入订单" => "Buy Order",
    "买入手续费" => "Buy Fee",
    "买入印花税" => "Buy Stamp Duty",
    "已中签" => "Allotted",
    "中签数量是" => "Allotted Quantity Is",
    "股" => "Shares",
    "已经扣除对应款项并且转为持仓,新股上市后即可正常交易" => "The corresponding amount has been deducted and converted into your holdings; you’ll be able to trade normally once the new shares are listed.",
    "但您的账户可用余额不足" => "However, your account’s available balance is insufficient.",
    "已经扣除部分对应款项" => "A portion of the corresponding amount has been deducted.",
    "还需" => "Still Need",
    "请尽快存款补足应交缴款" => "Please deposit funds as soon as possible to cover the required payment.",
    "已经扣除对应款项" => "The corresponding amount has been deducted.",
    "新股上市后即可正常交易" => "You can trade normally once the new shares are listed.",
    "股票申购手续费" => "Stock Fee",
    "中签数量" => "Number of winning tickets",
    "未能中签" => "Failed to win the lottery",
    "您在" => "You are at",
    "申购了新股" => "Subscribed for new shares",
    "中签公布日将通知您中签结果" => "The winning results will be notified on the winning announcement day",

    // Ipo
    '不在申购期内' => 'Not within the subscription period',
    '申购成功' => 'Buy Success',
    '申购失败' => 'Buy Failed',

    // Trader
    '关注成功' => 'focus on success',

    // Money Change
    '购买' => 'Buy',
    '产品名称' => 'Product',
    '产品' => 'Product',
    '资金兑换' => 'Money Exchange',
    '出' => 'Out',
    '进' => 'In',
    '充值申请' => 'Recharge',
    '申购手续费退还' => 'Subscription fee refund',
    '名称' => 'Name',
    '申购费退还' => 'Subscription fee refund',
    '股票申购' => 'Buy Stock',
    '提款申请' => 'Withdrawal request',
    '提款手续费' => 'Withdrawal fee',
    '拒绝' => 'Reject',
    '系统加' => 'System Add',
    '系统减' => 'System Deduction',

    //
    '日内交易申购' => 'Intraday Trading Subscription',
    '日内交易申购手续费' => 'Intraday trading subscription fee',
    '日内交易申购印花税' => 'Intraday trading subscription stamp duty',
    '红利申购' => 'Bonus subscription',
    '红利申购手续费' => 'Bonus subscription fee',
    '红利申购印花税' => 'Bonus subscription stamp duty',
    '大宗交易申购' => 'Block Trade Subscription',
    '大宗交易申购手续费' => 'Subscription fee for block trades',
    '大宗交易申购印花税' => 'Block trade subscription stamp duty',
    '退回大宗交易申购' => 'Return of block trade subscription',
    '退回大宗交易申购手续费' => 'Refund of bulk transaction subscription fees',
    '退回大宗交易申购印花税' => 'Refund of bulk transaction subscription fees',
    '退回日内交易申购' => 'Return of intraday trading subscription',
    '退回日内交易申购手续费' => 'Refund of intraday trading subscription fees',
    '退回日内交易申购印花税' => 'Refund of intraday trading subscription stamp duty',
    '退回红利申购' => 'Return of bonus subscription',
    '退回红利申购手续费' => 'Refund of bonus subscription fee',
    '退回红利申购印花税' => 'Refund of bonus subscription stamp duty',
    '退回部分日内交易申购' => 'Refund of some intraday trading subscriptions',
    '退回部分日内交易申购手续费' => 'Refund of part of the intraday transaction subscription fee',
    '退回部分日内交易申购印花税' => 'Refund of part of the intraday transaction subscription stamp duty',
    '退回部分大宗易申购手续费' => 'Refund part of the bulk subscription fee',
    '退回部分大宗易申购印花税' => 'Refund part of the bulk subscription fee',
    '退回部分大宗交易申购' => 'Refund of some block trade subscriptions',
    '退回部分红利申购手续费' => 'Refund of part of the bonus subscription fee',
    '退回部分红利申购印花税' => 'Refund of part of the bonus subscription stamp duty',
    '退回部分红利申购' => 'Refund of part of the bonus subscription',
    '请不要重复提交验证' => 'Please do not submit verification repeatedly',

    // UserMessage
    '消息ID不能为空' => 'Message ID cannot be empty',
    '消息ID必须为整数' => 'Message ID must be an integer',
    '消息不存在或已被删除' => 'Message does not exist or has been deleted',
    '获取成功' => 'Retrieved successfully',

    // Trader
    '您已关注过此交易员' => 'You have already followed this trader',
    '用户信息不存在' => 'User information does not exist',
    '余额不足' => 'Insufficient balance',
    '交易员不存在' => 'Trader does not exist',
    '扣除余额失败' => 'Failed to deduct balance',
    '未关注该交易员' => 'Not following this trader',
    '请先平仓后再取消跟单' => 'Please close positions before canceling follow',
    '恢复余额失败' => 'Failed to restore balance',
    '更新关注状态失败' => 'Failed to update follow status',
    '投资金额不足' => 'Insufficient investment amount',
    '更新失败' => 'Update failed',
    '跟单删除' => 'Follow Delete',
    '跟单交易' => 'Follow Trade',
    '关注交易员' => 'Follow Trader',
    '交易员ID' => 'Trader ID',
    '金额必须大于0' => 'Amount must be greater than 0',
    '账户余额不足' => 'Account balance is insufficient',
    '追加/减少跟单金额' => 'Add/Reduce Follow Amount',
    '操作' => 'Operation',
    '请联系客服处理未平仓订单后再取消跟单' => 'Please contact customer service to handle the open orders before canceling follow',

    // UserOrder
    '跟单订单不存在' => 'Follow order does not exist',
    '订单状态异常，无法平仓' => 'Order status is abnormal, cannot close position',
    '订单已锁定，请联系客服' => 'Order is locked, please contact customer service',
    '当前订单属于交易员订单' => 'Current order belongs to trader order',

    // IPO相关
    '新股ID不能为空' => 'IPO ID cannot be empty',
    '新股ID必须为整数' => 'IPO ID must be an integer',
    '新股ID不存在或状态不合法' => 'IPO ID does not exist or status is invalid',
    '申购数量不能为空' => 'Subscription quantity cannot be empty',
    '申购数量必须为整数' => 'Subscription quantity must be an integer',
    '申购数量不能小于1' => 'Subscription quantity cannot be less than 1',

    // 用户卡片相关
    '没有权限删除此卡' => 'No permission to delete this card',
    '用户信息未找到' => 'User information not found',

    // 提现相关
    '日提款次数达到上限' => 'Daily withdrawal limit reached',
];
