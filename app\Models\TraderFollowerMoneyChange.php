<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class TraderFollowerMoneyChange extends Model
{
	use HasDateTimeFormatter;
    use SoftDeletes;

    protected $table = 'trader_follower_money_change';

    protected $fillable = [
        'money_type',
        'user_id',
        'trader_id',
        'order_sn',
        'product_name',
        'before_money',
        'money',
        'after_money',
        'detailed',
        'created_at',
        'updated_at'
    ];

    public function user()
    {
        return $this->belongsTo(Users::class, 'user_id');
    }

    public function trader()
    {
        return $this->belongsTo(Trader::class, 'trader_id');
    }

}
