<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\UserCard;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Renderable\UserTable;


class UserCardController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserCard(), function (Grid $grid) {

            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '用户');
            $grid->column('type')->using([1 => '银行卡', 2 => 'USDT'])->label([
                1 => 'primary',
                2 => 'danger',
            ]);
            $grid->column('bank');
            $grid->column('bank_owner');
            $grid->column('bank_address');
            $grid->column('bank_card_no');
            $grid->column('network');
            $grid->column('usdt_address');
            $grid->column('created_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserCard(), function (Show $show) {
            $show->field('id');
            $show->field('user_id')->as(function ($userId) {
                $user = Users::find($userId);
                if ($user) {
                    return $user->email;
                } else {
                    return '';
                }
            });
            $show->field('type')->using([1 => '银行卡', 2 => 'USDT']);
            $show->field('bank');
            $show->field('bank_owner');
            $show->field('bank_address');
            $show->field('bank_card_no');
            $show->field('network');
            $show->field('usdt_address');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserCard(), function (Form $form) {

            $form->display('id');
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email')->required(); // 设置编辑数据显示
            $form->radio('type')->options(['1' => '银行卡', '2' => 'USDT'])
                ->when([1], function (Form $form) {
                    $form->text('bank');
                    $form->text('bank_owner');
                    $form->text('bank_card_no');
                    $form->text('bank_address');
                })
                ->when(2, function (Form $form) {
                    $form->text('network')->help('例如：TRC20');
                    $form->text('usdt_address');
                })->default('1');

            // 动态验证
            $form->saving(function (Form $form) {
                $rules = [];

                if ($form->type == 1) {
                    $rules = [
                        'bank' => 'required',
                        'bank_owner' => 'required',
                        'bank_card_no' => 'required',
                    ];
                }

                if ($form->type == 2) {
                    $rules = [
                        'network' => 'required',
                        'usdt_address' => 'required',
                    ];
                }

                $validator = \Validator::make($form->input(), $rules, [
                    'bank.required' => '请输入银行',
                    'bank_owner.required' => '请输入持卡人姓名',
                    'bank_card_no.required' => '请输入银行卡号',
                    'network.required' => '请输入网络协议',
                    'usdt_address.required' => '请输入U地址',
                ]);

                if ($validator->fails()) {
                    return $form->response()->error($validator->errors()->first());
                }
            });


        });
    }
}
