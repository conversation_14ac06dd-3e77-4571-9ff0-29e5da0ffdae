# 🔧 订单列表查询修复

## 🐛 **问题描述**

用户反馈：后台能看到订单，但是API接口 `/user_order/list` 查询不到订单。

## 🔍 **问题原因**

在之前添加统计功能时，订单查询被限制为只查询欧元（EUR）币种的订单：

```php
// 问题代码
$currency = strtolower($request->input('currency', 'eur'));

// 验证币种
if (!in_array($currency, ['eur', 'usd', 'gbp', 'jpy', 'myr'])) {
    $currency = 'eur';
}

// 查询时强制过滤币种
->where('currency', $currency)
```

这导致：
- 如果用户的订单不是EUR币种，就查询不到
- 即使不传currency参数，也会默认只查EUR订单

## ✅ **修复方案**

### 1. 移除强制币种限制
```php
// 修复后
$currency = $request->input('currency'); // 允许为空，查询所有币种
```

### 2. 修改查询条件
```php
// 修复前：强制过滤币种
->where('currency', $currency)

// 修复后：只有传了currency参数才过滤
->when($currency, function($query) use ($currency) {
    return $query->where('currency', $currency);
})
```

## 🎯 **修复后的行为**

### API调用方式

#### 1. 查询所有币种的订单（推荐）
```bash
GET /api/user_order/list
GET /api/user_order/list?status=1
```

#### 2. 查询特定币种的订单
```bash
GET /api/user_order/list?currency=eur
GET /api/user_order/list?currency=usd&status=1
```

#### 3. 查询特定状态的订单
```bash
GET /api/user_order/list?status=1  # 持仓中
GET /api/user_order/list?status=2  # 已平仓
GET /api/user_order/list?status=4  # 挂单中
```

## 🧪 **测试方法**

### 1. 调试订单数据
访问：`http://your-domain/debug-orders/{用户ID}`

这会返回：
```json
{
    "user_id": 123,
    "total_orders": 10,
    "eur_orders": 3,
    "raw_orders": [
        {
            "id": 1,
            "order_sn": "ORD123456",
            "currency": "usd",
            "status": 1,
            "trade_id": 0,
            "product_id": 456,
            "buy_cost": 1000.00,
            "created_at": "2024-12-03T10:00:00Z"
        }
    ]
}
```

### 2. 测试API接口
```bash
# 测试查询所有订单
curl -X GET "http://your-domain/api/user_order/list" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 测试查询特定币种
curl -X GET "http://your-domain/api/user_order/list?currency=usd" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 **支持的币种**

现在API支持查询所有币种的订单，包括但不限于：
- EUR (欧元)
- USD (美元)
- GBP (英镑)
- JPY (日元)
- MYR (马来西亚林吉特)
- 以及数据库中存在的任何其他币种

## 🔧 **统计功能保持不变**

修复后，统计功能仍然正常工作：
```json
{
    "orders": [...],
    "statistics": {
        "total_count": 10,
        "self_count": 6,
        "follow_count": 4,
        "total_investment": 8500.00,
        "total_profit_amount": 425.75,
        "total_market_value": 8925.75,
        "total_profit_percentage": 5.01,
        // ... 其他统计信息
    }
}
```

## 🎯 **向前兼容**

- ✅ 原有的API调用方式仍然有效
- ✅ 传入currency参数仍然可以过滤特定币种
- ✅ 不传currency参数现在会返回所有币种的订单
- ✅ 统计信息会包含所有查询到的订单

## 🚨 **注意事项**

1. **缓存清理**：如果使用了缓存，可能需要清理相关缓存
2. **前端适配**：前端可能需要适配多币种显示
3. **性能考虑**：查询所有币种可能返回更多数据

## 📱 **前端使用建议**

```javascript
// 推荐：查询所有订单
const allOrders = await api.get('/user_order/list');

// 如果需要特定币种
const eurOrders = await api.get('/user_order/list?currency=eur');

// 如果需要特定状态
const holdingOrders = await api.get('/user_order/list?status=1');

// 组合查询
const eurHoldingOrders = await api.get('/user_order/list?currency=eur&status=1');
```

现在您的订单列表应该可以正常查询到所有订单了！🎉
