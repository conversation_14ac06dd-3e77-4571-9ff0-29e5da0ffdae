<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\AdminPosition;
use App\Models\AdminPosition as AdminPositionModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Repositories\Menu;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Tree;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Widgets\Form as WidgetForm;

class AdminPositionController extends AdminController
{

    public function title()
    {
        return trans('职位管理');
    }

    public function index(Content $content)
    {
        return $content
            ->title($this->title())
            ->description('')
            ->body(function (Row $row) {
                $row->column(7, $this->treeView()->render());

                $row->column(5, function (Column $column) {
                    $form = new WidgetForm();
                    $form->action(admin_url('admin/position'));

                    $positionModel = AdminPositionModel::class;

                    $form->select('parent_id', '上级')->options($positionModel::selectOptions());
                    $form->text('name', '职位')->required();
                    $form->width(9, 2);

                    $column->append(Box::make(trans('admin.new'), $form));
                });
            });
    }


    protected function treeView()
    {
        return new Tree(new AdminPosition(), function (Tree $tree) {
            $tree->disableCreateButton();
            $tree->disableQuickCreateButton();
            $tree->disableEditButton();
            $tree->maxDepth(4);

            $tree->actions(function (Tree\Actions $actions) {
                if ($actions->getRow()->extension) {
                    $actions->disableDelete();
                }
            });

            $tree->branch(function ($branch) {
                $payload = "<i class='fa fa-universal-access'></i>&nbsp;<strong>{$branch['name']}</strong>";
                return $payload;
            });
        });
    }

    public function form()
    {
        $positionModel = AdminPositionModel::class;
        return Form::make(new AdminPosition(), function (Form $form) use ($positionModel) {
            $form->tools(function (Form\Tools $tools) {
                $tools->disableView();
            });

            $form->display('id', 'ID');

            $form->select('parent_id', '上级')->options(function () use ($positionModel) {
                return $positionModel::selectOptions();
            })->saving(function ($v) {
                return (int) $v;
            });
            $form->text('name', '职位名称')->required();
            $form->icon('weight', '排序');

            $form->display('created_at', trans('admin.created_at'));
            $form->display('updated_at', trans('admin.updated_at'));
        })->saved(function (Form $form, $result) {
            $response = $form->response()->location('admin/position');

            if ($result) {
                return $response->success(__('admin.save_succeeded'));
            }

            return $response->info(__('admin.nothing_updated'));
        });
    }
}
