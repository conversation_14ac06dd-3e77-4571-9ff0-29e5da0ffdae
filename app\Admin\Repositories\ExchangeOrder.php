<?php

namespace App\Admin\Repositories;

use App\Models\ExchangeOrder as Model;
use App\Models\MoneyChange;
use App\Models\UsersInfo;
use App\Models\ExchangeRate;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ExchangeOrder extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    /**
     * 新增记录.
     *
     * @param Form $form
     * @return mixed
     */
    public function store(Form $form)
    {
        $result = null;

        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();
            $from = $updates['from_currency'];
            $to = $updates['to_currency'];
            $userId = $updates['user_id'];
            $fromMoney = $updates['from_money'];


            if ($from == $to) {
                throw  new \Exception('兑换币种和目标币种不能相同');
            }

            // 获取费率
            $rate = ExchangeRate::query()->where('from_currency', $from)
                ->where('to_currency', $to)
                ->value('rate');
            if (!$rate) {
                throw  new \Exception('汇率不存在');
            }

            if ($fromMoney <= 0 || !is_numeric($fromMoney)) {
                throw  new \Exception('兑换的金额不能小于等于0或者非数字');
            }

            $updates['rate'] = $rate; // 覆盖表单的费率
            $toMoney = round($fromMoney * $rate, 2); // 兑换到的金额
            if ($toMoney <= 0) {
                throw  new \Exception('兑换到的金额是0');
            }
            $updates['to_money'] = $toMoney; // 覆盖表单兑换到的金额

            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }

            $userInfo = UsersInfo::query()->where('user_id', $userId)->lockForUpdate()->first();
            $fromBalanceField = strtolower($from) . '_balance';
            $toBalanceField = strtolower($to) . '_balance';

            $beforFromBalanc = $userInfo->$fromBalanceField;
            $beforToBalanc = $userInfo->$toBalanceField;

            if ($beforFromBalanc < $fromMoney) {
                throw  new \Exception($from . '资金余额不不足');
            }

            $userInfo->$fromBalanceField -= $fromMoney; // 扣除资金 兑换出
            $userInfo->$toBalanceField += $toMoney; // 添加资金 兑换到的金额
            $userInfo->save();

            // 生成订单号
            $out_trade_no = generateUniqueOrderNumber();
            $updates['order_sn'] = $out_trade_no;
            // 添加流水资金
            // 转换出
            MoneyChange::create([
                'money_type' => $from,
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_name' => '',
                'detailed' => "资金兑换|{$from} to {$to}|出",
                'type' => MoneyChange::TYPE_EXCHANGE_OUT,
                'before_money' => $beforFromBalanc,
                'money' => -$fromMoney,
                'after_money' => $beforFromBalanc - $fromMoney,
            ]);

            // 转换入
            MoneyChange::create([
                'money_type' => $to,
                'user_id' => $userId,
                'order_sn' => $out_trade_no,
                'product_name' => '',
                'detailed' => "资金兑换|{$from} to {$to}|进",
                'type' => MoneyChange::TYPE_EXCHANGE_IN,
                'before_money' => $beforToBalanc,
                'money' => $toMoney,
                'after_money' => $beforToBalanc + $toMoney,
            ]);


            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }

            $result = $model->save();

            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
