<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\Recharge\Check;
use App\Admin\Renderable\UserTable;
use App\Admin\Repositories\Recharge;
use App\Models\RechargeBank;
use App\Models\RechargeUsdt;
use App\Models\Users;
use App\Services\AdminUserService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Recharge(), function (Grid $grid) {
            $model = $grid->model()->with(['user']);

            // 获取当前登录的管理员账号
            $currentAdminUser = Admin::user();
            $adminIds = AdminUserService::getVisibleAdminIds($currentAdminUser);

            if (!empty($adminIds)) {
                $model->whereHas('user', function ($query) use ($adminIds) {
                    $query->whereIn('parent_id', $adminIds);
                });
            }

            $model->orderBy('id', 'desc');

            // 禁用行选择器（去掉勾选框）
            $grid->disableRowSelector();
            if (!Admin::user()->can('funds-recharge-add')) {
                $grid->disableCreateButton(); // 禁用新增
            }


            $grid->column('id')->sortable();
            $grid->column('order_sn');
            $grid->column('user.parent.username', '上级')->display(function () {
                return optional($this->user->parent)->username;
            });
            $grid->column('user.email', '账户');
            $grid->column('bank_id')->display(function ($bank_id) {
                if ($this->type == 1) {
                    $bankInfo = RechargeBank::find($bank_id);
                    if ($bankInfo) {
                        $html = "";
                        $html .= "<p>组别：{$bankInfo->bank_name}</p>";
                        $html .= "<p>卡号：{$bankInfo->bank_no}</p>";
                        $html .= "<p>持卡人姓名：{$bankInfo->bank_owner}</p>";
                        return $html;
                    } else {
                        return 'unknow';
                    }
                } else {
                    $usdtInfo = RechargeUsdt::find($bank_id);
                    if ($usdtInfo) {
                        $html = "";
                        $html .= "<p>U地址：{$usdtInfo->address}</p>";
                        return $html;
                    } else {
                        return 'unknow';
                    }
                }
            });
            $grid->column('recharge_name');
            $grid->column('amount');
            $grid->column('image')->image();
            $grid->column('state')->display(function ($state) {
                if ($state == 1) {
                    return '<span style="color: #43bd43">已审核</span>';
                } elseif ($state == 2) {
                    return '<span style="color: #bc0f24">审核拒绝</span>';
                } else {
                    return '<span style="color: #cac8c8">待审核</span>';
                }
            });//            $grid->column('postscript');
            $grid->column('remark');
            $grid->column('created_at');
//            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
                if ($actions->row->state == 0) {
                    if (Admin::user()->can('funds-recharge-check')) {
                        $actions->append(new Check());
                    }

                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Recharge(), function (Show $show) {
            // ✅ 移除右上角的删除按钮
            $show->disableDeleteButton();
            $show->disableEditButton();
            $show->field('id');
            $show->field('bank_id');
            $show->field('type');
            $show->field('user_id');
            $show->field('order_sn');
            $show->field('recharge_name');
            $show->field('amount');
            $show->field('image');
            $show->field('state');
            $show->field('postscript');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Recharge(), function (Form $form) {
            $form->disableEditingCheck();
            $form->disableDeleteButton();
            $form->display('id');
            $form->radio('type')->options([1 => '銀行卡'])->default(1);
            $form->selectTable('user_id')
                ->from(UserTable::make(['id' => $form->getKey()])) // 设置渲染类实例，并传递自定义参数
                ->model(Users::class, 'id', 'email') // 设置编辑数据显示
                ->required()
                ->rules('required', ['required' => '请选择用户']);

            $form->text('amount')->required()->rules('required|numeric|min:10', ['required' => '請輸入充值金額', 'numeric' => '充值金額只能是數字', 'min' => '不能低於10']);

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
