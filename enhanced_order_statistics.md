# 📊 订单列表增强统计功能

## 🎯 **功能概述**

为订单列表接口添加了丰富的统计信息，包括总投资额、盈亏金额、盈亏百分比、总市值等多维度统计数据。

## 📋 **新增统计字段**

### 原有统计字段
```json
{
    "total_count": 7,        // 总订单数
    "self_count": 0,         // 自主订单数
    "follow_count": 7        // 跟单订单数
}
```

### 新增统计字段
```json
{
    // 原有字段
    "total_count": 7,
    "self_count": 0,
    "follow_count": 7,
    
    // ✅ 总体统计
    "total_investment": 5000.00,        // 总投资额
    "total_profit_amount": 350.50,      // 总盈亏金额
    "total_market_value": 5350.50,      // 总市值
    "total_profit_percentage": 7.01,    // 总盈亏百分比
    
    // ✅ 持仓统计
    "holding_count": 3,                 // 持仓订单数
    "holding_investment": 3000.00,      // 持仓投资额
    "holding_profit_amount": 150.00,    // 持仓盈亏金额
    "holding_market_value": 3150.00,    // 持仓市值
    "holding_profit_percentage": 5.00,  // 持仓盈亏百分比
    
    // ✅ 已平仓统计
    "closed_count": 4,                  // 已平仓订单数
    "closed_investment": 2000.00,       // 已平仓投资额
    "closed_profit_amount": 200.50,     // 已平仓盈亏金额
    "closed_market_value": 2200.50,     // 已平仓市值
    "closed_profit_percentage": 10.03,  // 已平仓盈亏百分比
    
    // ✅ 其他状态统计
    "pending_count": 0,                 // 挂单订单数
    "cancelled_count": 0,               // 已撤单订单数
    
    // ✅ 盈亏分布
    "profit_orders_count": 5,           // 盈利订单数
    "loss_orders_count": 2,             // 亏损订单数
    "break_even_orders_count": 0,       // 保本订单数
    
    // ✅ 交易表现
    "win_rate": 75.00,                  // 胜率（已平仓订单）
    "max_profit": 120.50,               // 最大盈利
    "max_loss": -45.20                  // 最大亏损
}
```

## 🎯 **统计字段详细说明**

### 总体统计
| 字段 | 类型 | 说明 | 计算方式 |
|------|------|------|----------|
| `total_investment` | float | 总投资额 | 所有订单的buy_cost之和 |
| `total_profit_amount` | float | 总盈亏金额 | 所有订单的profit_amount之和 |
| `total_market_value` | float | 总市值 | 所有订单的market_cap之和 |
| `total_profit_percentage` | float | 总盈亏百分比 | (总盈亏 ÷ 总投资额) × 100 |

### 持仓统计（status = 1）
| 字段 | 类型 | 说明 | 计算方式 |
|------|------|------|----------|
| `holding_count` | integer | 持仓订单数 | status=1的订单数量 |
| `holding_investment` | float | 持仓投资额 | 持仓订单的buy_cost之和 |
| `holding_profit_amount` | float | 持仓盈亏 | 持仓订单的实时profit_amount之和 |
| `holding_market_value` | float | 持仓市值 | 持仓订单的实时market_cap之和 |
| `holding_profit_percentage` | float | 持仓盈亏率 | (持仓盈亏 ÷ 持仓投资额) × 100 |

### 已平仓统计（status = 2）
| 字段 | 类型 | 说明 | 计算方式 |
|------|------|------|----------|
| `closed_count` | integer | 已平仓订单数 | status=2的订单数量 |
| `closed_investment` | float | 已平仓投资额 | 已平仓订单的buy_cost之和 |
| `closed_profit_amount` | float | 已平仓盈亏 | 已平仓订单的最终profit_amount之和 |
| `closed_market_value` | float | 已平仓市值 | 已平仓订单的sell_money之和 |
| `closed_profit_percentage` | float | 已平仓盈亏率 | (已平仓盈亏 ÷ 已平仓投资额) × 100 |

### 交易表现统计
| 字段 | 类型 | 说明 | 计算方式 |
|------|------|------|----------|
| `win_rate` | float | 胜率 | (盈利的已平仓订单数 ÷ 总已平仓订单数) × 100 |
| `max_profit` | float | 最大盈利 | 所有订单中profit_amount的最大值 |
| `max_loss` | float | 最大亏损 | 所有订单中profit_amount的最小值 |

## 📊 **完整API响应示例**

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "orders": [
            // 订单列表数据...
        ],
        "statistics": {
            // 原有统计
            "total_count": 10,
            "self_count": 6,
            "follow_count": 4,
            
            // 总体统计
            "total_investment": 8500.00,
            "total_profit_amount": 425.75,
            "total_market_value": 8925.75,
            "total_profit_percentage": 5.01,
            
            // 持仓统计
            "holding_count": 5,
            "holding_investment": 5000.00,
            "holding_profit_amount": 250.00,
            "holding_market_value": 5250.00,
            "holding_profit_percentage": 5.00,
            
            // 已平仓统计
            "closed_count": 5,
            "closed_investment": 3500.00,
            "closed_profit_amount": 175.75,
            "closed_market_value": 3675.75,
            "closed_profit_percentage": 5.02,
            
            // 其他状态
            "pending_count": 0,
            "cancelled_count": 0,
            
            // 盈亏分布
            "profit_orders_count": 7,
            "loss_orders_count": 3,
            "break_even_orders_count": 0,
            
            // 交易表现
            "win_rate": 80.00,
            "max_profit": 150.50,
            "max_loss": -75.25
        },
        "server_time": "2024-12-03T12:30:45Z"
    }
}
```

## 🎯 **前端使用示例**

### 统计卡片显示
```javascript
const renderStatisticsCards = (statistics) => {
    return `
        <div class="statistics-grid">
            <!-- 总体统计 -->
            <div class="stat-card">
                <h3>总投资额</h3>
                <p class="amount">€${statistics.total_investment}</p>
            </div>
            
            <div class="stat-card">
                <h3>总盈亏</h3>
                <p class="amount ${statistics.total_profit_amount >= 0 ? 'profit' : 'loss'}">
                    €${statistics.total_profit_amount} (${statistics.total_profit_percentage}%)
                </p>
            </div>
            
            <div class="stat-card">
                <h3>总市值</h3>
                <p class="amount">€${statistics.total_market_value}</p>
            </div>
            
            <!-- 持仓统计 -->
            <div class="stat-card">
                <h3>持仓盈亏</h3>
                <p class="amount ${statistics.holding_profit_amount >= 0 ? 'profit' : 'loss'}">
                    €${statistics.holding_profit_amount} (${statistics.holding_profit_percentage}%)
                </p>
                <small>${statistics.holding_count} 个持仓</small>
            </div>
            
            <!-- 交易表现 -->
            <div class="stat-card">
                <h3>胜率</h3>
                <p class="percentage">${statistics.win_rate}%</p>
                <small>基于 ${statistics.closed_count} 个已平仓订单</small>
            </div>
        </div>
    `;
};
```

### 盈亏分布图表
```javascript
const renderProfitDistribution = (statistics) => {
    const chartData = {
        labels: ['盈利订单', '亏损订单', '保本订单'],
        datasets: [{
            data: [
                statistics.profit_orders_count,
                statistics.loss_orders_count,
                statistics.break_even_orders_count
            ],
            backgroundColor: ['#52c41a', '#ff4d4f', '#faad14']
        }]
    };
    
    // 使用Chart.js或其他图表库渲染
    new Chart(ctx, {
        type: 'doughnut',
        data: chartData
    });
};
```

### 状态分布统计
```javascript
const renderStatusDistribution = (statistics) => {
    return `
        <div class="status-stats">
            <div class="status-item">
                <span class="label">持仓中</span>
                <span class="count">${statistics.holding_count}</span>
            </div>
            <div class="status-item">
                <span class="label">已平仓</span>
                <span class="count">${statistics.closed_count}</span>
            </div>
            <div class="status-item">
                <span class="label">挂单中</span>
                <span class="count">${statistics.pending_count}</span>
            </div>
            <div class="status-item">
                <span class="label">已撤单</span>
                <span class="count">${statistics.cancelled_count}</span>
            </div>
        </div>
    `;
};
```

## 🚀 **性能优化**

### 计算优化
- ✅ 使用Collection的聚合方法（sum, count, max, min）
- ✅ 一次遍历完成多个统计计算
- ✅ 避免重复查询数据库

### 实时性保证
- ✅ 基于实时数据计算统计信息
- ✅ 包含最新的价格和盈亏数据
- ✅ 持仓订单使用实时市值

## 🎯 **业务价值**

1. **投资概览**：用户可以快速了解整体投资情况
2. **风险评估**：通过盈亏分布和胜率评估交易表现
3. **资产管理**：清楚了解持仓和已平仓的资产分布
4. **决策支持**：基于历史表现做出投资决策
5. **用户体验**：丰富的统计信息提升用户粘性

现在订单列表提供了全面的统计分析，帮助用户更好地管理投资组合！📊
