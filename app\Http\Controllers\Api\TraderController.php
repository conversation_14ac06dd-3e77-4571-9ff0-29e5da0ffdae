<?php

namespace App\Http\Controllers\Api;

use App\Models\MoneyChange;
use App\Models\Trader;
use App\Models\TraderArticle;
use App\Models\TraderFollower;
use App\Models\TraderFollowerMoneyChange;
use App\Models\TraderGroup;
use App\Models\TraderInfo;
use App\Models\UsersInfo;
use App\Services\JiangshanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TraderController extends ApiBaseController
{
    // 交易员列表
    public function traderList(Request $request)
    {
        $params = $request->all();
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 10;

        $user = $request->user();

        // 为每个交易员添加跟随状态
        $lang = $user->language ?? 'en'; // 默认使用英文

        // 根据语言设置对应的字段名
        switch ($lang) {
            case 'sp':
                $introField = 'es_introduction';
                break;
            case 'de':
                $introField = 'de_introduction';
                break;
            case 'en':
            default:
                $introField = 'en_introduction';
                break;
        }

        // 查询时只选择需要的字段，并设置别名为 introduction，便于前端统一处理
        $list = Trader::query()
            ->where('state', 1)
            ->select([
                'id',
                'name',
                'nickname',
                'avatar',
                'level',
                'weight',
                'follower',
                'fans',
                "$introField as Introduction", // 动态字段别名为 introduction
            ])
            ->orderBy('weight', 'desc')
            ->orderBy('id', 'desc')
            ->paginate($limit, ['*'], 'page', $page);


        if ($user && $list->count() > 0) {
            $traderIds = $list->pluck('id')->toArray();

            // 批量获取当前用户的跟随关系
            $followings = \App\Models\TraderFollower::where('user_id', $user->id)
                ->whereIn('trader_id', $traderIds)
                ->where('state', 1)
                ->get()
                ->keyBy('trader_id');

            // 为每个交易员添加跟随状态
            $list->getCollection()->transform(function ($trader) use ($followings) {
                $trader->is_following = isset($followings[$trader->id]);
                if ($trader->is_following) {
                    $follower = $followings[$trader->id];
                    $trader->follow_info = [
                        'bet_money' => (float)$follower->bet_money,
                        'used_money' => (float)$follower->used_money,
                        'available_money' => (float)($follower->bet_money - $follower->used_money),
                        'follow_time' => $follower->created_at,
                    ];
                } else {
                    $trader->follow_info = null;
                }
                return $trader;
            });
        } else {
            // 未登录用户，所有交易员都标记为未跟随
            $list->getCollection()->transform(function ($trader) {
                $trader->is_following = false;
                $trader->follow_info = null;
                return $trader;
            });
        }

        return $this->returnMsg(200, __('api.请求成功'), $list);
    }

    // 交易员详情
    public function traderView(Request $request)
    {
        $id = (int)$request->input('id');
        if (!$id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        $user = $request->user();

        // 为每个交易员添加跟随状态
        $lang = $user->language ?? 'en'; // 默认使用英文

        // 根据语言设置对应的字段名
        switch ($lang) {
            case 'sp':
                $introField = 'es_introduction';
                break;
            case 'de':
                $introField = 'de_introduction';
                break;
            case 'en':
            default:
                $introField = 'en_introduction';
                break;
        }

        // 查询时只选择需要的字段，并设置别名为 introduction，便于前端统一处理
        $trader = Trader::query()
            ->where('id', $id)
            ->select([
                'id',
                'name',
                'nickname',
                'avatar',
                'level',
                'weight',
                'follower',
                'fans',
                "$introField as Introduction", // 动态字段别名为 introduction
            ])
            ->first();

        if (!$trader) {
            return $this->returnMsg(404, __('api.交易员不存在'));
        }

        // 检查当前用户是否已跟随该交易员
        $user = $request->user();
        $isFollowing = false;
        $followInfo = null;

        if ($user) {
            $follower = \App\Models\TraderFollower::where('user_id', $user->id)
                ->where('trader_id', $id)
                ->where('state', 1)
                ->first();

            if ($follower) {
                $isFollowing = true;
                $followInfo = [
                    'bet_money' => (float)$follower->bet_money,
                    'used_money' => (float)$follower->used_money,
                    'available_money' => (float)($follower->bet_money - $follower->used_money),
                    'follow_time' => $follower->created_at,
                ];
            }
        }

        // 构建返回数据
        $traderData = $trader->toArray();
        $traderData['is_following'] = $isFollowing;
        $traderData['follow_info'] = $followInfo;

        return $this->returnMsg(200, __('api.请求成功'), $traderData);
    }

    // 交易员统计资料表(做图表统计用)
    public function traderInfo(Request $request)
    {
        $id = (int)$request->input('id');
        if (!$id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        $trader_info = TraderInfo::query()
            ->with(['trader'])
            ->where('trader_id', $id)
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $trader_info);
    }

    // 交易员投资组合(做投资列表展示用)
    public function traderGroup(Request $request)
    {
        $id = (int)$request->input('id');
        if (!$id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        $trader_group = TraderGroup::query()
            ->with(['product', 'trader'])
            ->where('trader_id', $id)
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $trader_group);
    }

    // 交易员文章(做文章列表展示用)
    public function traderArticle(Request $request)
    {
        $id = (int)$request->input('id');
        if (!$id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        $user = $request->user();

        // 为每个交易员添加跟随状态
        $lang = $user->language ?? 'en'; // 默认使用英文

        // 根据语言设置对应的字段名
        switch ($lang) {
            case 'sp':
                $introField = 'es_content';
                break;
            case 'de':
                $introField = 'de_content';
                break;
            case 'en':
            default:
                $introField = 'en_content';
                break;
        }

        $trader_article = TraderArticle::query()
            ->select([
                'id',
                'image',
                'trader_id',
                'product_id',
                "$introField as content", // 动态字段别名为 content
                'is_hot',
                'is_top',
                'created_at',
            ])
            ->with(['product', 'trader'])
            ->where('trader_id', $id)
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $trader_article);
    }

    // 交易员文章(首页文章列表展示用)
    public function index_article(Request $request)
    {
        // 获取分页参数
        $params = $request->all();
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 10;
        $level = isset($params['level']) ? (int)$params['level'] : 1; // 交易员等级，1=明星 2=精英 3=冠军

        $user = $request->user();

        // 为每个交易员添加跟随状态
        $lang = $user->language ?? 'en'; // 默认使用英文

        // 根据语言设置对应的字段名
        switch ($lang) {
            case 'sp':
                $introField = 'es_content';
                break;
            case 'de':
                $introField = 'de_content';
                break;
            case 'en':
            default:
                $introField = 'en_content';
                break;
        }

        $query = TraderArticle::query()
            ->select([
                'id',
                'image',
                'trader_id',
                'product_id',
                "$introField as content", // 动态字段别名为 content
                'is_hot',
                'is_top',
                'created_at',
            ])
            ->with(['product', 'trader'])
            // 先按 is_hot、created_at 排序
            ->orderBy('is_hot', 'desc')
            ->orderBy('created_at', 'desc')
            // 加上对 trader.level 的过滤
            ->whereHas('trader', function ($q) use ($level) {
                $q->where('level', $level);
            });

        // 分页
        $trader_article = $query->paginate($limit, ['*'], 'page', $page);

        return $this->returnMsg(200, __('api.请求成功'), $trader_article);
    }

    // 用户已关注的交易员列表
    public function userAttention(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        // 获取用户关注的交易员列表
        $trader_follower = TraderFollower::query()
            ->with(['trader:id,name,nickname,avatar,en_introduction,es_introduction,de_introduction,state,level,follower,fans,weight,created_at'])
            ->where('user_id', $user->id)
//            ->where('state', 1)
            ->get()->toArray();

        if (empty($trader_follower)) {
            return $this->returnMsg(200, __('api.请求成功'));
        }

        // 统计每个交易员为当前用户建仓的投资信息（报酬率、持仓总盈亏、净值（投资本金+总盈亏）、总投资金额）
        foreach ($trader_follower as &$follower) {
            $traderId = $follower['trader_id'];

            // 计算该交易员的投资统计
            $statistics = $this->calculateTraderStatistics($user->id, $traderId);

            // 添加统计信息到返回数据
            $follower['investment_statistics'] = $statistics;
        }

        return $this->returnMsg(200, __('api.请求成功'), $trader_follower);
    }

    // 用户已关注的交易员详情
    public function userAttentionView(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        $trader_id = $request->input('trader_id');

        // 获取用户关注的交易员列表
        $trader_follower = TraderFollower::query()
            ->where('trader_id', $trader_id)
            ->with(['trader:id,name,nickname,avatar,en_introduction,es_introduction,de_introduction,state,level,follower,fans,weight,created_at'])
            ->where('user_id', $user->id)
//            ->where('state', 1)
            ->get()->toArray();

        if (empty($trader_follower)) {
            return $this->returnMsg(404, __('api.未关注该交易员'));
        }

        // 统计每个交易员为当前用户建仓的投资信息（报酬率、持仓总盈亏、净值（投资本金+总盈亏）、总投资金额）
        foreach ($trader_follower as &$follower) {
            $traderId = $follower['trader_id'];

            // 获取交易员建仓的订单
            $trader_order = $this->getTraderorder($user->id, $traderId);

            // 计算该交易员的投资统计
            $statistics = $this->calculateTraderStatistics($user->id, $traderId);

            // 添加统计信息到返回数据
            $follower['investment_statistics'] = $statistics;
            $follower['trader_order'] = $trader_order;
        }

        return $this->returnMsg(200, __('api.请求成功'), $trader_follower);
    }

    // 跟单投资
    public function traderAttention(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        $money = $request->input('money');
        $trader_id = $request->input('trader_id');

        if (!$trader_id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        if (!$money || !is_numeric($money) || $money <= 0) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        // 判断当前用户是否已经跟随这个交易员
        $isset = TraderFollower::query()
            ->where('trader_id', $trader_id)
            ->where('user_id', $user->id)
            ->where('state', 1)
            ->first();

        if ($isset) {
            return $this->returnMsg(400, __('api.您已关注过此交易员'));
        }

        // 查询用户的余额是否足够
        $user_info = UsersInfo::query()->where('user_id', $user->id)->first();

        // 用户信息不存在
        if (!$user_info) {
            return $this->returnMsg(400, __('api.用户信息不存在'));
        }

        // 用户余额不足
        if ($user_info->eur_balance < $money) {
            return $this->returnMsg(400, __('api.余额不足'));
        }

        $trader = Trader::find($trader_id);

        if (!$trader) {
            return $this->returnMsg(404, __('api.交易员不存在'));
        }

        $before = $user_info->eur_balance; // 关注前的余额

        DB::beginTransaction();
        try {
            // 扣除用户余额
            $user_info->eur_balance -= $money;
            // $user_info->eur_dongjie += $money;
            if (!$user_info->save()) {
                return $this->returnMsg(500, __('api.扣除余额失败'));
            }

            // 关注交易员表
            TraderFollower::query()->create([
                'user_id' => $user->id,
                'trader_id' => $trader->id,
                'bet_money' => $money,
            ]);

            // 记录用户的资金变动
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $user->id,
                'order_sn' => generateUniqueOrderNumber(),
                'product_name' => $trader->name,
                'detailed' => "跟单交易|关注交易员|,|产品名称|:|{$trader->name}|,|交易员ID|:{$trader->id}",
                'type' => MoneyChange::TYPE_TRADE_ATTENTION,
                'before_money' => $before,
                'money' => -$money,
                'after_money' => $user_info->eur_balance,
            ]);

            TraderFollowerMoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $user->id,
                'trader_id' => $trader->id,
                'order_sn' => generateUniqueOrderNumber(),
                'product_name' => $trader->name,
                'detailed' => "跟单交易|关注交易员|,|产品名称|:|{$trader->name}|,|交易员ID|:{$trader->id}",
                'before_money' => 0,
                'money' => $money,
                'after_money' => $money,
            ]);

            $trader->follower += 1;
            $trader->fans += 1;
            $trader->save();

            DB::commit();
            return $this->returnMsg(200, __('api.请求成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(503, __('api.提交失败'), $e->getMessage());
        }
    }

    public function traderMoneyChange(Request $request)
    {
        $user = $request->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        $trader_id = $request->input('trader_id');

        if (!$trader_id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        // 查询用户是否关注了该交易员
        $follower = TraderFollower::query()
            ->where('user_id', $user->id)
            ->where('trader_id', $trader_id)
            ->first();

        if (!$follower) {
            return $this->returnMsg(404, __('api.未关注该交易员'));
        }

        // 获取该交易员的资金变动记录
        $money_changes = TraderFollowerMoneyChange::query()
            ->where('user_id', $user->id)
            ->where('trader_id', $trader_id)
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->returnMsg(200, __('api.请求成功'), $money_changes);

    }

    // 取消跟单
    public function cancelAttention()
    {
        // 取消跟单逻辑
        $user = request()->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));

        $trader_id = request()->input('trader_id');

        if (!$trader_id) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        // 查询用户是否关注了该交易员
        $follower = TraderFollower::query()
            ->where('user_id', $user->id)
            ->where('trader_id', $trader_id)
            ->where('deleted_at', null) // 确保未被软删除
            ->first();

        if (!$follower) {
            return $this->returnMsg(404, __('api.未关注该交易员'));
        }

        // 检查用户是否有该交易员建仓的正在持仓中的订单
        $has_open_orders = \App\Models\UserOrder::query()
            ->where('user_id', $user->id)
            ->where('trade_id', $trader_id)
            ->where('status', 1) // 只检查未平仓的订单
            ->exists();

        if ($has_open_orders) {
            return $this->returnMsg(400, __('api.请联系客服处理未平仓订单后再取消跟单'));
        }

        DB::beginTransaction();
        try {
            // 恢复用户的余额
            $user_info = UsersInfo::query()->where('user_id', $user->id)->first();
            if (!$user_info) {
                return $this->returnMsg(400, __('api.用户信息不存在'));
            }
            $user_info->eur_balance += $follower->bet_money;
            // $user_info->eur_dongjie -= $follower->bet_money;
            if (!$user_info->save()) {
                return $this->returnMsg(500, __('api.恢复余额失败'));
            }

            // 删除关注记录
            $follower->delete();

            // 关注状态改成未关注
//            $follower->state = 0;
//            if (!$follower->save()) {
//                return $this->returnMsg(500, __('api.更新关注状态失败'));
//            }

            // 记录用户的资金变动
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $user->id,
                'order_sn' => generateUniqueOrderNumber(),
                'product_name' => "取消跟单",
                'detailed' => "取消跟单|交易员ID|:{$trader_id}",
                'type' => MoneyChange::TYPE_TRADE_ATTENTION,
                'before_money' => $user_info->eur_balance - $follower->bet_money,
                'money' => $follower->bet_money,
                'after_money' => $user_info->eur_balance,
            ]);

            DB::commit();
            return $this->returnMsg(200, __('api.请求成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(503, __('api.提交失败'), $e->getMessage());
        }
    }

    // 追加减少投资金额
    public function attentionMoney()
    {
        // 追加或减少投资金额逻辑
        $user = request()->user();
        if (!$user) return $this->returnMsg(401, __('api.请先登录'));
        $trader_id = request()->input('trader_id');
        $money = request()->input('money');
        $action = request()->input('action'); // 'add' 或 'reduce'
        if (!$trader_id || !$money || !in_array($action, ['add', 'reduce'])) {
            return $this->returnMsg(400, __('api.参数错误'));
        }

        if (!is_numeric($money) || $money <= 0) {
            return $this->returnMsg(400, __('api.金额必须大于0'));
        }

        // 查询用户是否关注了该交易员
        $follower = TraderFollower::query()
            ->where('user_id', $user->id)
            ->where('trader_id', $trader_id)
            ->first();

        if (!$follower) {
            return $this->returnMsg(404, __('api.未关注该交易员'));
        }

        DB::beginTransaction();
        try {
            $user_info = UsersInfo::query()->where('user_id', $user->id)->first();
            if (!$user_info) {
                return $this->returnMsg(400, __('api.用户信息不存在'));
            }
            $before_money = $follower->bet_money;

            if ($action === 'add') {
                // 减少投资金额
                if ($user_info->eur_balance < $money) {
                    return $this->returnMsg(400, __('api.账户余额不足'));
                }
                // 追加投资金额
                $user_info->eur_balance -= $money;
                // $user_info->eur_dongjie += $money;
                $follower->bet_money += $money;
            } else {
                // 减少投资金额
                if ($follower->bet_money < $money) {
                    return $this->returnMsg(400, __('api.投资金额不足'));
                }
                $user_info->eur_balance += $money;
                // $user_info->eur_dongjie -= $money;
                $follower->bet_money -= $money;
            }

            if (!$user_info->save() || !$follower->save()) {
                return $this->returnMsg(500, __('api.更新失败'));
            }

            // 记录用户的资金变动
            MoneyChange::create([
                'money_type' => "EUR",
                'user_id' => $user->id,
                'order_sn' => generateUniqueOrderNumber(),
                'product_name' => "追加/减少跟单金额",
                'detailed' => "追加/减少跟单金额|交易员ID|:{$trader_id}|操作|:{$action}",
                'type' => MoneyChange::TYPE_TRADE_ATTENTION,
                'before_money' => $before_money,
                'money' => ($action === 'add' ? -$money : $money),
                'after_money' => $follower->bet_money,
            ]);

            DB::commit();
            return $this->returnMsg(200, __('api.请求成功'));
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnMsg(503, __('api.提交失败'), $e->getMessage());
        }
    }

    /**
     * 计算交易员为用户建仓的投资统计信息
     *
     * @param int $userId 用户ID
     * @param int $traderId 交易员ID
     * @return array 统计信息
     */
    private function calculateTraderStatistics($userId, $traderId)
    {
        // 获取该交易员为用户建仓的所有订单
        $orders = \App\Models\UserOrder::where('user_id', $userId)
            ->where('trade_id', $traderId)
            ->get();

        if ($orders->isEmpty()) {
            return [
                'total_investment' => 0,        // 总投资本金
                'total_profit_loss' => 0,       // 总盈亏
                'net_value' => 0,               // 净值（本金+盈亏）
                'return_rate' => 0,             // 报酬率（%）
            ];
        }

        // 分类统计
        $holdingOrders = $orders->where('status', 1);  // 持仓中
        $closedOrders = $orders->where('status', 2);   // 已平仓

        // 计算总投资本金（所有订单的买入成本）
        $totalInvestment = $orders->sum('buy_cost');

        // 计算总盈亏
        $totalProfitLoss = 0;

        // 持仓订单需要实时计算盈亏
        foreach ($holdingOrders as $order) {
            // 获取产品实时价格
            $product = \App\Models\Product::find($order->product_id);
            if ($product) {
                if ($product->pro_type == 1) {
                    $Jiangshan = new JiangshanService();
                    $jiangshan = $Jiangshan->get_new_data_serices($product);
                    $product->price = $jiangshan['price'] ?? $product->price;
                } else {
                    $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
                    $product->price = $quote['price'] ?? $product->price;
                }

                $product->refresh();

                // 计算实时盈亏
                if ($order->direction_data == 1) {
                    // 买涨
                    $profitLoss = ($product->price - $order->buy_price) * $order->buy_quantity;
                } else {
                    // 买跌
                    $profitLoss = ($order->buy_price - $product->price) * $order->buy_quantity;
                }
                $totalProfitLoss += $profitLoss;
            }
        }

        // 已平仓订单使用数据库中的盈亏数据
        $totalProfitLoss += $closedOrders->sum('profit_amount');

        // 计算净值（投资本金 + 总盈亏）
        $netValue = $totalInvestment + $totalProfitLoss;

        // 计算报酬率（总盈亏 / 总投资本金 * 100）
        $returnRate = $totalInvestment > 0 ? round(($totalProfitLoss / $totalInvestment) * 100, 2) : 0;

        return [
            'total_investment' => round($totalInvestment, 2),           // 总投资本金
            'total_profit_loss' => round($totalProfitLoss, 2),         // 总盈亏
            'net_value' => round($netValue, 2),                        // 净值
            'return_rate' => $returnRate,                              // 报酬率（%）
        ];
    }

    /**
     * 获取交易员为用户建仓的所有订单
     *
     * @param int $userId 用户ID
     * @param int $traderId 交易员ID
     * @return array 订单列表
     */
    private function getTraderorder($userId, $traderId)
    {
        // 获取该交易员为用户建仓的所有订单
        $orders = \App\Models\UserOrder::with([
            'product' => function ($query) {
                $query->select('id', 'name', 'symbol', 'currency', 'exchange', 'logo', 'price');
            }
        ])
            ->where('user_id', $userId)
            ->where('trade_id', $traderId)
            ->orderBy('buy_time', 'desc')
            ->get();

        if ($orders->isEmpty()) {
            return [];
        }

        $result = [];
        foreach ($orders as $order) {
            // 获取产品信息
            $product = $order->product;

            if ($order->status == 1 && $product) { // 持仓中
                if ($product->pro_type == 1) {
                    $Jiangshan = new JiangshanService();
                    $jiangshan = $Jiangshan->get_new_data_serices($product);
                    $product->price = $jiangshan['price'] ?? $product->price;
                } else {
                    $quote = \App\Services\TwelvedataNewService::quoteByOne($product);
                    $product->price = $quote['price'] ?? $product->price;
                }
                $product->refresh();

                // 计算实时盈亏
                if ($order->direction_data == 1) {
                    // 买涨
                    $currentProfitLoss = ($product->price - $order->buy_price) * $order->buy_quantity;
                } else {
                    // 买跌
                    $currentProfitLoss = ($order->buy_price - $product->price) * $order->buy_quantity;
                }

                // 计算当前市值
                $currentMarketValue = $order->buy_cost + $currentProfitLoss;
            } else {
                // 已平仓订单使用数据库中的盈亏
                $currentProfitLoss = $order->profit_amount;
                $currentMarketValue = $order->market_cap;
            }

            $result[] = [
                'id' => $order->id,
                'order_sn' => $order->order_sn, // 订单号
                'product_id' => $order->product_id, // 产品ID
                'product_info' => $product ? [
                    'id' => $product->id, // 产品ID
                    'name' => $product->name, // 产品名称
                    'symbol' => $product->symbol, // 产品代码
                    'currency' => $product->currency, // 产品货币
                    'exchange' => $product->exchange, // 产品交易所
                    'current_price' => (float)$product->price, // 当前价格
                ] : null,
                'direction_data' => $order->direction_data, // 交易方向（1=买涨，2=买跌）
                'buy_price' => (float)$order->buy_price, // 买入价格
                'buy_quantity' => $order->buy_quantity, // 买入数量
                'buy_cost' => (float)$order->buy_cost, // 买入成本
                'buy_fees' => (float)$order->buy_fees, // 买入费用
                'current_profit_loss' => round($currentProfitLoss, 2), // 当前盈亏
                'current_market_value' => round($currentMarketValue, 2), // 当前市值
                'profit_percentage' => $order->buy_cost > 0 ? round(($currentProfitLoss / $order->buy_cost) * 100, 2) : 0, // 盈亏百分比
                'status' => $order->status, // 订单状态（1=持仓中，2=已平仓）
                'buy_time' => $order->buy_time, // 买入时间
                'sell_time' => $order->sell_time, // 平仓时间
                'sell_price' => $order->sell_price, // 平仓价格
                'sell_quantity' => $order->sell_quantity, // 平仓数量
                'sell_money' => $order->sell_money, // 平仓金额
                'sell_fees' => $order->sell_fees, // 平仓费用
            ];
        }

        return $result;
    }
}
