<?php

namespace App\Admin\Repositories;

use App\Models\Zhishu as Model;
use App\Services\JiangshanService;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Grid;

class Z<PERSON>hu extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function get(Grid\Model $model)
    {

        $query = parent::get($model);

        $country = request()->get('country', 'de');

//
        if (!$query->isEmpty()) {
            $jiangshan = new JiangshanService();
            $updateResult = $jiangshan->get_new_data_by_index_batch($query->getCollection(), $country);
            $query->setCollection($updateResult);
        }

        return $query;
    }
}
