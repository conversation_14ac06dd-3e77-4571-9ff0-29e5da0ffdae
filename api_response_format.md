# 📊 订单列表API返回格式详细说明

## 🎯 **接口信息**

**接口地址：** `GET /api/user-orders`

**请求参数：**
- `status` (可选): 订单状态筛选 1=持仓中,2=已平仓,3=平仓中,4=挂单中,5=已撤单
- `currency` (可选): 币种筛选 eur,usd,gbp,jpy,myr，默认eur

## 📋 **完整返回格式**

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "orders": [
            {
                // ===== 基础订单信息 =====
                "id": 123,                          // 订单ID
                "order_sn": "ORD20241203001",       // 订单号
                "order_type": "self",               // 订单类型: "self"=自主下单, "follow"=跟单
                "trade_id": 0,                      // 交易员ID: 0=自主下单, >0=跟单交易员ID
                "product_id": 456,                  // 产品ID
                
                // ===== 交易方向和类型 =====
                "direction_data": 1,                // 交易方向: 1=买涨, 2=买跌
                "direction_text": "买涨",           // 交易方向文本
                "buy_type": 1,                      // 买入类型: 1=市价, 2=限价
                "buy_type_text": "市价",            // 买入类型文本
                
                // ===== 买入信息 =====
                "buy_price": 100.50,               // 买入价格
                "buy_quantity": 10,                 // 买入数量(股数)
                "buy_cost": 1005.00,               // 买入成本
                "buy_fees": 5.03,                  // 买入手续费
                "lever": 1,                        // 杠杆倍数
                "currency": "eur",                 // 使用币种
                "source": 1,                       // 来源: 1=普通买入,2=新股,3=日内交易,4=大宗交易,5=红利
                
                // ===== 订单状态 =====
                "status": 1,                       // 订单状态: 1=持仓中,2=已平仓,3=平仓中,4=挂单中,5=已撤单
                "status_text": "持仓中",           // 状态文本描述
                
                // ===== 实时价格和盈亏 =====
                "current_price": 102.30,           // 当前实时价格
                "profit_amount": 18.00,            // 实时盈亏金额
                "profit_percentage": 1.79,         // 盈亏百分比
                "market_cap": 1023.00,             // 当前市值
                
                // ===== 止盈止损设置 =====
                "take_profit": 50.00,              // 止盈金额设置
                "stop_loss": 20.00,                // 止损金额设置
                "auto_close_info": {               // 自动平仓状态信息
                    "can_take_profit": false,       // 是否可以止盈
                    "can_stop_loss": false,         // 是否可以止损
                    "take_profit_distance": 32.00,  // 距离止盈还差多少
                    "stop_loss_distance": -38.00,   // 距离止损还差多少
                    "risk_level": "normal"          // 风险等级: normal=正常, warning=警告, danger=危险
                },
                
                // ===== 平仓信息 =====
                "sell_price": 0,                   // 卖出价格(未平仓为0)
                "sell_quantity": 0,                // 卖出数量(未平仓为0)
                "sell_money": 0,                   // 卖出金额(未平仓为0)
                "sell_fees": 0,                    // 卖出手续费(未平仓为0)
                "see_fee": 0,                      // 其他费用
                "sell_type": 0,                    // 卖出类型: 1=市价, 2=限价
                
                // ===== 订单标识 =====
                "is_lock": 0,                      // 是否锁定: 0=未锁定, 1=锁定
                "is_open_buy": 0,                  // 是否开盘买入: 0=否, 1=是
                "is_open_sell": 0,                 // 是否开盘卖出: 0=否, 1=是
                
                // ===== 时间信息 =====
                "buy_time": 1701590400,            // 买入成交时间戳
                "sell_time": null,                 // 卖出成交时间戳(未平仓为null)
                "created_at": "2024-12-03T10:00:00Z", // 订单创建时间
                
                // ===== 显示信息 =====
                "display_type": "product",         // 显示类型: "product"=产品信息, "trader"=交易员信息
                "display_name": "苹果公司",        // 显示名称
                "display_symbol": "AAPL",          // 显示代码
                "display_icon": "/images/aapl.png", // 显示图标
                
                // ===== 产品详细信息 =====
                "product_info": {
                    "id": 456,                      // 产品ID
                    "name": "苹果公司",            // 产品名称
                    "symbol": "AAPL",              // 产品代码
                    "icon": "/images/aapl.png",    // 产品图标
                    "currency": "usd",             // 产品计价币种
                    "pro_type": 1,                 // 产品类型: 1=股票, 2=大宗商品, 3=ETF, 4=外汇, 5=数字货币
                    "country": "us",               // 产品所属国家
                    "other_id": "12345"            // 其他标识ID
                }
            }
        ],
        
        // ===== 跟单订单示例 =====
        {
            // 基础字段同上，以下是跟单订单特有字段
            "order_type": "follow",                // 跟单订单
            "trade_id": 789,                       // 交易员ID
            
            // 显示信息变为交易员信息
            "display_type": "trader",
            "display_name": "跟单 - 张三",
            "display_symbol": "",
            "display_icon": "/avatars/trader789.jpg",
            
            // ===== 交易员信息 =====
            "trader_info": {
                "id": 789,                         // 交易员ID
                "name": "张三",                    // 交易员姓名
                "nickname": "股神张三",            // 交易员昵称
                "avatar": "/avatars/trader789.jpg" // 交易员头像
            },
            
            // 产品信息(简化版)
            "product_info": {
                "id": 456,
                "name": "苹果公司",
                "symbol": "AAPL",
                "pro_type": 1,
                "country": "us",
                "other_id": "12345"
            },
            
            "can_view_details": true               // 可以查看交易员详情
        }
        
        // ===== 统计信息 =====
        "statistics": {
            "total_count": 45,                     // 总订单数
            "self_count": 30,                      // 自主下单数量
            "follow_count": 15                     // 跟单订单数量
        },
        
        // ===== 服务器信息 =====
        "server_time": "2024-12-03T12:30:45Z"     // 服务器当前时间
    }
}
```

## 🔍 **字段说明**

### 订单状态 (status)
- `1` = 持仓中 (正在交易)
- `2` = 已平仓 (交易完成)
- `3` = 平仓中 (正在平仓)
- `4` = 挂单中 (等待成交)
- `5` = 已撤单 (已取消)

### 交易方向 (direction_data)
- `1` = 买涨 (做多)
- `2` = 买跌 (做空)

### 买入类型 (buy_type)
- `1` = 市价单 (按市场价格立即成交)
- `2` = 限价单 (按指定价格等待成交)

### 产品类型 (pro_type)
- `1` = 股票
- `2` = 大宗商品
- `3` = ETF
- `4` = 外汇
- `5` = 数字货币

### 风险等级 (risk_level)
- `normal` = 正常 (盈亏在合理范围)
- `warning` = 警告 (接近止损线)
- `danger` = 危险 (非常接近止损)

### 订单来源 (source)
- `1` = 普通买入
- `2` = 新股申购
- `3` = 日内交易
- `4` = 大宗交易
- `5` = 红利再投资

## 🎯 **关键特性**

1. **实时数据**: `current_price`, `profit_amount`, `market_cap` 都是实时计算
2. **智能显示**: 自主订单显示产品信息，跟单订单显示交易员信息
3. **完整盈亏**: 包含实时盈亏、百分比、止盈止损状态
4. **风险管理**: 提供风险等级和距离止盈止损的距离
5. **时间追踪**: 完整的时间信息，包含服务器时间同步

## 💡 **使用示例**

### 前端显示逻辑
```javascript
// 根据订单类型显示不同信息
if (order.order_type === 'self') {
    // 自主订单：显示产品信息
    displayName = order.product_info.name;
    displayIcon = order.product_info.icon;
} else {
    // 跟单订单：显示交易员信息
    displayName = order.trader_info.name;
    displayIcon = order.trader_info.avatar;
}

// 实时盈亏显示
const profitColor = order.profit_amount >= 0 ? 'green' : 'red';
const profitText = `${order.profit_amount} (${order.profit_percentage}%)`;

// 风险等级显示
const riskColor = {
    'normal': 'green',
    'warning': 'orange',
    'danger': 'red'
}[order.auto_close_info?.risk_level];
```

### 数据更新频率
- **静态数据**: 缓存5分钟 (订单基本信息、产品信息)
- **实时数据**: 每次请求更新 (价格、盈亏、市值)
- **建议轮询**: 持仓页面5秒，其他页面10-30秒

### 错误处理
```json
{
    "code": 401,
    "message": "请先登录",
    "data": null
}
```

```json
{
    "code": 422,
    "message": "参数错误",
    "data": null
}
```

## 🔧 **接口调用示例**

```bash
# 获取所有订单
curl -X GET "https://your-domain/api/user-orders" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 获取持仓中的EUR订单
curl -X GET "https://your-domain/api/user-orders?status=1&currency=eur" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 获取已平仓的USD订单
curl -X GET "https://your-domain/api/user-orders?status=2&currency=usd" \
     -H "Authorization: Bearer YOUR_TOKEN"
```
