<?php

namespace App\Admin\Forms;

use App\Models\MoneyChange;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\UsersInfo;

class Userbalance extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $id = $this->payload['id'] ?? null;

        $balance = $input['balance'] ?? 0;

        $currency = $input['currency'] ?? null;
        $type = $input['type'] ?? null;

        if (!$id) {
            return $this->response()->error('参数错误');
        }

        if (!$currency) {
            return $this->response()->error('没有选择货币');
        }

        if (!is_numeric($balance)) {
            return $this->response()->error('你输入的金额不是数字类型');
        }

        if (!$type) {
            return $this->response()->error('没有选择方式');
        }

        if (!in_array($type, [1, 2])) {
            return $this->response()->error('方式错误');
        }


        DB::beginTransaction();
        try {
            $wallet = UsersInfo::where('user_id', $id)->first();

            if (!$wallet) {
                DB::rollBack();
                return $this->response()->error('钱包数据不存在');
            }


            // 判断对一个的货币存不存在
            if (!$wallet->$currency) {
                DB::rollBack();
                return $this->response()->error('货币不存在');
            }

            // 减金额
            if ($type == 2 && $wallet->$currency < $balance) {
                DB::rollBack();
                return $this->response()->error('账户余额不足，无法完成扣除操作');
            }

            $beforMoney = $wallet->$currency;

            if ($type == 1) {
                UsersInfo::where('user_id', $id)->increment($currency, $balance);
                $afterMoney = $beforMoney + $balance;
                $type = MoneyChange::TYPE_SYSPLUS;
                $changeBalance = $balance;
                $detail = "系统加";
            } else {
                UsersInfo::where('user_id', $id)->decrement($currency, $balance);
                $afterMoney = $beforMoney - $balance;
                $type = MoneyChange::TYPE_SYSREDUCE;
                $changeBalance = $balance * (-1);
                $detail = "系统减";
            }

            $currencyArr = explode('_', $currency);

            $desc = $input['desc'] ? "|{$input['desc']}" : '';
            $detail .= $desc;

            // 添加流水记录
            $model = new MoneyChange();
            $model->create([
                'user_id' => $id,
                'order_sn' => 'S' . time() . $id . rand(1000, 9999),
                'type' => $type,
                'money_type' => strtoupper($currencyArr[0]),
                'before_money' => $beforMoney,
                'money' => $changeBalance,
                'after_money' => $afterMoney,
                'detailed' => $detail,
            ]);

            DB::commit();
            return $this->response()->success('账户余额调整成功')->refresh();

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error('账户余额调整失败:' . $e->getMessage());
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        //$this->confirm('您确定要调整余额吗', 'content');

        $wallet = UsersInfo::where('user_id', $this->payload['id'])->first();

        $currency = [];

        if ($wallet) {
            foreach ($wallet->toArray() as $field => $value) {
                if (Str::endsWith($field, '_balance')) {
                    $tmp = explode('_', $field);
                    $currency[$field] = $tmp[0];
                }
            }
        }

        $this->select('currency', '货币')->options($currency)->rules('required', [
            'required' => '请选择货币',
        ]);
        $this->radio('type', '方式')->options(['1' => '加', '2' => '减'])->default('1');
        $this->number('balance', '调整金额')->rules('required|numeric|min:0.01', [
            'required' => '请输入金额',
            'numeric' => '必须是数字',
            'min' => '必须要大于0',
        ]);
        $this->text('desc', '备注');
    }
}
