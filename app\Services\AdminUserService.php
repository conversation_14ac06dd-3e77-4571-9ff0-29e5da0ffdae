<?php

namespace App\Services;

use App\Models\AdminPosition;
use App\Models\AdminUser;

class AdminUserService
{
    /**
     * 获取当前管理员可见的管理员ID列表（含自己）
     * @param $currentAdminUser 当前登录的管理员账号
     */
    public static function getVisibleAdminIds($currentAdminUser): array
    {
        $admin = $currentAdminUser;

        // 没有绑定职位：超级管理员，返回全部管理员ID
        if (!$admin->position_id) {
            return [];
        }

        // 先判斷  如果當前層級是第四層，則不往下查了 直接返回當前登錄的管理員id就好
        $position = AdminPosition::find($admin->position_id);
        if (!$position) {
            return []; // 不存在也返回全部
        }

        if ($position->depth == 4) {
            return [$admin->id];
        }

        // 获取当前职位及所有下级职位ID
        $positionIds = self::getAllSubPositionIds($admin->position_id);
        $positionIds[] = $admin->position_id;

        // 找到这些职位对应的管理员ID
        return AdminUser::whereIn('position_id', $positionIds)->pluck('id')->toArray();
    }

    /**
     * 递归获取某个职位的所有下级职位ID
     */
    public static function getAllSubPositionIds($positionId): array
    {
        $all = AdminPosition::all();
        $results = [];

        $walk = function ($id) use (&$walk, &$results, $all) {
            foreach ($all as $pos) {
                if ($pos->parent_id == $id) {
                    $results[] = $pos->id;
                    $walk($pos->id);
                }
            }
        };

        $walk($positionId);

        return $results;
    }

}
