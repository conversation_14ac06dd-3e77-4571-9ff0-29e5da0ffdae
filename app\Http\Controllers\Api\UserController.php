<?php

namespace App\Http\Controllers\Api;

use App\Models\AdminPosition;
use App\Models\AdminUser;
use App\Models\MoneyChange;
use App\Models\SystemConfig;
use App\Models\UserOrder;
use App\Models\Users;
use App\Models\UsersInfo;
use App\Models\UserStart;
use App\Models\UserVerify;
use App\Services\TwelvedataService;
use Dcat\Admin\Models\Administrator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Validation\Rule;

class UserController extends ApiBaseController
{
    public function get_message()
    {
        return [
            'old_password.required' => __('请输入旧登录密码'),
            'new_password.required' => __('请输入新登录密码'),
            'new_password.min' => __('新登录密码至少8个字符'),
            'new_password.confirmed' => __('新登录密码和确认密码不一致'),
            'new_pay_password.required' => __('请输入新支付密码'),
            'new_pay_password.min' => __('支付密码至少8个字符'),
            'new_pay_password.confirmed' => __('支付密码和确认密码不一致'),
            'surname.required' => __('api.姓氏不能为空'),
            'surname.string' => __('api.姓氏必须是字符串'),
            'surname.min' => __('api.姓氏不能小于2个字符'),
            'surname.max' => __('api.姓氏不能超过32个字符'),
            'name.required' => __('api.名字不能为空'),
            'name.string' => __('api.名字必须是字符串'),
            'name.min' => __('api.名字不能小于2个字符'),
            'name.max' => __('api.名字不能超过32个字符'),
            'id_card_num.required' => __('api.身份证号码不能为空'),
            'id_card_num.string' => __('api.身份证号码格式不正确'),
            'id_card_num.min' => __('api.身份证号码长度不能小于6个字符'),
            'id_card_num.max' => __('api.身份证号码长度不能超过18个字符'),
            'id_card_img.required' => __('api.身份证图片不能为空'),
            'id_card_img.string' => __('api.身份证图片格式不正确'),
            'id_card_img.max' => __('api.身份证图片不能超过255个字符'),
            'file.required' => __('api.请上传文件'),
            'file.image' => __('api.只能上传图片文件'),
            'file.mimes' => __('api.图片格式仅支持jpeg,png,jpg,gif,svg,webp'),
            'file.max' => __('api.图片大小不能超过10MB'),
        ];
    }

    public function customerService(Request $request)
    {
        $user = $request->user();

        $parent_id = $user->parent_id;

        $admin = AdminUser::query()
            ->with(['position'])
            ->where('id', $parent_id)
            ->first();

        $position_kefu_url = [
            13 => 'https://client.vyntor-support.com/?agentid=ef44a9465702889730163a9671a684f6&language=es', // 顾远组
            15 => 'https://client.vyntor-serve.com/?agentid=e485ee26346d0319954906abfdebd3e6&language=es', // 小林组
            22 => 'https://client.vyntor-online.com/?agentid=9f640f18fd02bac0aa32064ea600c3ec&language=es', // 发发组

            18 => 'https://client.vyntor-support.com/?agentid=ef44a9465702889730163a9671a684f6&language=es', // 顾远组（水军）
            19 => 'https://client.vyntor-serve.com/?agentid=e485ee26346d0319954906abfdebd3e6&language=es', // 小林组（水军）
            25 => 'https://client.vyntor-online.com/?agentid=9f640f18fd02bac0aa32064ea600c3ec&language=es', // 发发组（水军）
        ];

        $kefu_url = SystemConfig::query()->where('name', 'kefu_url')->pluck('value');

        if ($admin->position->depth == 3) {
            $kefu_url = $position_kefu_url[$admin->position->id];
        } else if($admin->position->depth == 4){
            // 业务层
            $parent_position = AdminPosition::query()->where('id', $admin->position->parent_id)->first();
            if ($parent_position) {
                $kefu_url = $position_kefu_url[$parent_position->id] ?? 'kefu_url_1';
            } else {
                $kefu_url = 'kefu_url_1'; // 默认客服链接
            }
        }

        // 返回客服链接
        return $this->returnMsg(200, __('api.请求成功'), [
            'customer_service_url' => $kefu_url,
        ]);
    }

    public function loginCustomerService(Request $request)
    {
        $kefu_url = SystemConfig::query()->where('name', 'kefu_url')->pluck('value');

        // 返回客服链接
        return $this->returnMsg(200, __('api.请求成功'), [
            'customer_service_url' => $kefu_url,
        ]);
    }

    public function get_attention(Request $request)
    {
        // 接收用product_type分组分类的参数
        $productType = $request->input('product_type', null);
        // 验证product_type参数
        if ($productType && !in_array($productType, [1, 2, 3, 4, 5])) {
            return $this->returnMsg(400, __('api.产品类型不正确'));
        }

        // 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        // 获取用户关注列表, 用product_type分组，产品类型 1=股票 2=大宗商品 3=ETF 4=外汇 5=数字货币,
        // 如果有product_type参数，过滤用户关注列表
        if ($productType) {
            $userStarts = UserStart::query()
                ->where('user_id', $request->user()->id)
                ->where('product_type', $productType)
                ->get();
        } else {
            // 如果没有product_type参数，获取所有用户关注列表
            $userStarts = UserStart::query()
                ->where('user_id', $request->user()->id)
                ->get();
        }

        if (empty($userStarts)) {
            return $this->returnMsg(200, __('api.请求成功'), []);
        }

        // 根据$userStarts批量获取产品信息
        $productIds = $userStarts->pluck('product_id')->toArray();
        $product = \App\Models\Product::query()
            ->whereIn('id', $productIds)
            ->get();

        // 批量更新产品行情
        \App\Services\TwelvedataNewService::quoteByBatch($product);

        // 将产品信息关联到用户关注列表
        $attentionList = $userStarts->each(function ($userStart) use ($product) {
            $userStart->product = $product->firstWhere('id', $userStart->product_id);
        });

        // 返回关注列表
        return $this->returnMsg(200, __('api.请求成功'), $attentionList);
    }

    // 获取用户余额信息
    public function getUserBalance(Request $request)
    {
        // 验证用户登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        // 支持的币种列表
        $allowedTypes = ['usd', 'eur', 'jpy', 'gbp', 'myr'];

        // 从请求获取 type，默认 usd
        $type = strtolower($request->input('type', 'usd'));
        if (!in_array($type, $allowedTypes)) {
            return $this->returnMsg(400, __('api.不支持的币种类型'));
        }

        // 动态拼接字段名
        $balanceField = "{$type}_balance";
        $frozenField = "{$type}_dongjie";

        // 查询用户对应记录，只取需要的两个字段
        $record = UsersInfo::query()
            ->where('user_id', $user->id)
            ->first([
                $balanceField . ' as balance',
                $frozenField . ' as dongjie',
            ]);

        if (!$record) {
            return $this->returnMsg(404, __('api.用户余额信息未找到'));
        }

        // 获取市值
        $total = $record->balance + $record->dongjie; // 总资产 = 账户余额 + 冻结金额

        // 计算持仓/挂单 的市值和赢亏
        $marketCount = UserOrder::query()->where('user_id', $user->id)->whereIn('status', [1, 4])->where('currency', $type)->sum('buy_cost');
        $LossCount = UserOrder::query()->where('user_id', $user->id)->whereIn('status', [1, 4])->where('currency', $type)->sum('profit_amount');
        $feeRate = $user->fee_rate > 0 ? $user->fee_rate : SystemConfig::getValue('shouxufeilv');
        $lowFee = SystemConfig::getValue('zuidishouxufei');

        // 返回结构化数据
        $data = [
            'currency' => strtoupper($type),
            'balance' => (float)$record->balance,
            'dongjie' => (float)$record->dongjie,
            'total' => (float)$total,
            'market_count' => (float)$marketCount,
            'loss_count' => (float)$LossCount,
            'fee_rate' => $feeRate,
            'low_fee' => $lowFee,
        ];

        return $this->returnMsg(200, __('api.请求成功'), $data);
    }

    // 修改用户登录密码
    public function updateLoginPassword(Request $request)
    {
        // 1. 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }
        $rules = [
            'old_password' => 'required|string',
            'new_password' => 'required|string|min:8',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        // 3. 验证旧密码是否正确
        if (!Hash::check($request->input('old_password'), $user->password)) {
            return $this->returnMsg(422, __('api.旧密码不正确'));
        }

        // 4. 更新密码
        $user->password = Hash::make($request->input('new_password'));
        $user->save();

        return $this->returnMsg(200, __('api.登录密码修改成功'));
    }

    // 修改支付密码
    public function updatePayPassword(Request $request)
    {
        // 1. 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $rules = [
            'new_pay_password' => 'required|string|min:6',
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());


        // 如果用户已有支付密码，则需要验证旧密码
        if (!empty($user->pay_password)) {
            if (empty($request->input('old_pay_password'))) {
                return $this->returnMsg(422, __('api.请输入旧支付密码'));
            }

            if (!Hash::check($request->input('old_pay_password'), $user->pay_password)) {
                return $this->returnMsg(422, __('api.旧支付密码不正确'));
            }
        }

        // 4. 设置/更新支付密码
        $user->pay_password = Hash::make($request->input('new_pay_password'));
        $user->save();

        $message = empty($user->getOriginal('pay_password'))
            ? __('api.支付密码设置成功')
            : __('api.支付密码修改成功');

        return $this->returnMsg(200, $message);
    }

    public function authRealname(Request $request)
    {
        // 1. 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $rules = [
            'surname' => 'required|string|min:2|max:32', // 姓氏
            'name' => 'required|string|min:2|max:32', // 名字
            'id_card_num' => 'required|string|min:6|max:18', // 身份证号码
            'id_card_img' => 'required|string|max:255',// 身份证图片
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        if ($user->auth_status == 2) {
            return $this->returnMsg(422, __('api.您已完成实名认证'));
        }

        $user->surname = $request->input('surname');
        $user->name = $request->input('name');
        $user->id_card_num = $request->input('id_card_num');
        $user->id_card_img = $request->input('id_card_img');
        $user->auth_status = 1;
        $user->save();

        return $this->returnMsg(200, __('api.实名认证提交成功，请等待审核'));
    }

    // 上传文件接口
    public function uploadFile(Request $request)
    {
        $rules = [
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,svg,webp|max:10240' // 限制最大10MB
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());

        $file = $request->file('file');

        // 生成唯一文件名
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

        // 存储文件到指定目录
        $path = date('Y/m/d');
        $filePath = $file->storeAs($path, $filename);

        // 返回文件访问路径
        return $this->returnMsg(200, __('api.文件上传成功'), ['file_path' => asset('uploads/' . $filePath)]);
    }

    // 用户资金流水
    public function moneyChange(Request $request)
    {
        // 1. 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $params = $request->all();
        $page = isset($params['page']) ? (int)$params['page'] : 1;
        $limit = isset($params['limit']) ? (int)$params['limit'] : 10;

        $money_change = MoneyChange::query()->where('user_id', $user->id)->where('is_show', 1)
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);
        $money_change->getCollection()->transform(function ($item) {
            $detailedRes = explode('|', $item->detailed);
            $tmpRemarkArr = [];
            foreach ($detailedRes as $v) {
                // 检查翻译是否存在
                $v = trim($v);
                $translated = Lang::get('api.' . $v);
                $tmpRemarkArr[] = $translated !== 'api.' . $v ? $translated : $v;
            }

            $detailed = implode('|', $tmpRemarkArr);
            $item->detailed = $detailed;
            return $item;
        });

        return $this->returnMsg(200, __('api.请求成功'), $money_change);
    }

    /**
     * 用户验证
     */
    public function userVerify(Request $request)
    {
        // 1. 验证用户是否登录
        $user = $request->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        $rules = [
            'first_name' => 'required|string|min:2|max:32', // 姓氏
//            'middle_name' => 'required|string|min:2|max:32', // 名字
            'last_name' => 'required|string|min:2|max:100', // 名字
            'birthday' => 'required|date', // 生日
            'passport_no' => 'required|string|min:2|max:100', // 护照
            'job' => 'required|string|min:2|max:100', // 职业
            'boss_name' => 'required|string|min:2|max:100', // 老板名称
            'sex' => [
                'required',
                'string',
                Rule::in(['f', 'm']),
            ], // 性别

            'stocks_trade_times' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 股票交易次数
            'crypto_trade_times' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 数字货币交易次数
            'lever_trade_times' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 杠杆交易次数
            'info_learn' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 培训学习
            'user_knows' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 用户理解常识
            'hobby' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5, 6, 7]),
            ], // 用户体验兴趣
            'hold_time' => [
                'required',
                'integer',
                Rule::in([1, 2, 3]),
            ], // 持有时间
            'trade_mudi' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 交易目的
            'plan_recharge' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5, 6]),
            ], // 计划充值

            'hope_return' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5]),
            ], // 风险回报
            'my_friend' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4]),
            ], // 关系
            'money_from' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5, 6, 7]),
            ], // 资金来源
            'year_money' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5, 6]),
            ], // 年度收入净额
            'money_change' => [
                'required',
                'integer',
                Rule::in([1, 2, 3, 4, 5, 6]),
            ], // 流动资金
        ];

        $validator = Validator::make($request->all(), $rules, $this->get_message());
        if ($validator->fails()) return $this->returnMsg(422, $validator->errors()->first());


        $params = $request->only([
            'first_name',
            'last_name',
            'middle_name',
            'sex',
            'stocks_trade_times',
            'crypto_trade_times',
            'lever_trade_times',
            'info_learn',
            'user_knows',
            'hobby',
            'hold_time',
            'trade_mudi',
            'plan_recharge',
            'hope_return',
            'my_friend',
            'money_from',
            'year_money',
            'money_change',
            'birthday',
            'passport_no',
            'job',
            'boss_name',
        ]);

        $ex = UserVerify::query()->where('user_id', $user->id)->exists();
        if ($ex) {
            return $this->returnMsg(501, __('api.请不要重复提交验证'));
        }
        $params['user_id'] = $user->id;
        $res = UserVerify::create($params);
        if ($res) {
            return $this->returnMsg(200, __('api.提交成功'));
        } else {
            return $this->returnMsg(501, __('api.提交失败'));
        }
    }

    // 获取实名认证的状态
    public function authInfo()
    {
        // 1. 验证用户是否登录
        $user = request()->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        // 2. 获取用户信息
        $Users = Users::query()->where('id', $user->id)
            ->first([
                'id',
                'surname',
                'name',
                'id_card_num',
                'id_card_img',
                'auth_status',
                'created_at',
            ]);
        if (!$Users) {
            return $this->returnMsg(404, __('api.用户信息未找到'));
        }

        // 3. 返回用户信息
        return $this->returnMsg(200, __('api.请求成功'), $Users);
    }

    // 更新个人信息（头像、昵称、电话号、生日）
    public function updateSelfinfo(Request $request)
    {
        // 1. 验证用户是否登录
        $user = request()->user();
        if (!$user) {
            return $this->returnMsg(401, __('api.未登录或登录已过期'));
        }

        // 头像、昵称、电话号、生日
        $params = $request->all();

        $rules = [
            'avatar' => 'nullable|string|max:255',// 身份证图片
            'nickname' => 'nullable|string|max:32', // 昵称
            'mobile' => 'nullable|string|max:15', // 电话号
            'birthday' => 'nullable|date', // 生日
        ];

        $validator = Validator::make($params, $rules, $this->get_message());
        if ($validator->fails()) {
            return $this->returnMsg(422, $validator->errors()->first());
        }

        // 2. 更新用户信息
        $Users = Users::query()->where('id', $user->id)->first();

        if (!$Users) {
            return $this->returnMsg(404, __('api.用户信息未找到'));
        }

        // 更新用户信息
        $Users->avatar = $params['avatar'] ?? $Users->avatar;
        $Users->nickname = $params['nickname'] ?? $Users->nickname;
        $Users->mobile = $params['mobile'] ?? $Users->mobile;
        $Users->birthday = $params['birthday'] ?? $Users->birthday;
        $Users->save();
        // 3. 返回更新后的用户信息
        $updatedUser = Users::query()->where('id', $user->id)
            ->first(['id', 'avatar', 'nickname', 'mobile', 'birthday']);
        if (!$updatedUser) {
            return $this->returnMsg(404, __('api.用户信息未找到'));
        }

        return $this->returnMsg(200, __('api.个人信息更新成功'), $updatedUser);
    }
}

