<?php

namespace App\Admin\Repositories;

use App\Models\Users as Model;
use App\Models\UsersInfo;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;
use Illuminate\Support\Facades\DB;


class Users extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function store(Form $form)
    {
        $result = null;


        DB::transaction(function () use ($form, &$result) {
            $model = $this->model();

            $updates = $form->updates();

            [$relations, $relationKeyMap] = $this->getRelationInputs($model, $updates);

            if ($relations) {
                $updates = Arr::except($updates, array_keys($relationKeyMap));
            }

            $admin = Admin::user();
            $updates['parent_id'] = $admin->id;
            $updates['agent_id'] = $admin->id;

            foreach ($updates as $column => $value) {
                $model->setAttribute($column, $value);
            }

            $result = $model->save();


            // 添加钱包
            UsersInfo::create([
                'user_id' => $model->id,
            ]);

            $this->updateRelation($form, $model, $relations, $relationKeyMap);
        });

        return $this->model()->getKey();
    }
}
